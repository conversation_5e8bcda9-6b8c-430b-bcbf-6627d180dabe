.compare-table {
    background-color: #fff;
    margin-bottom: 40px;
    border-collapse: collapse;
    .compare-image {
        aspect-ratio: 1 / 0.6;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }        
    }
    td, th {
        padding: 10px;
        vertical-align: middle;
        width: 20%;
        font-size: 14px;
        font-weight: 500;
        @media (max-width: 767.98px) {
            white-space: nowrap;
        }
    }
    td {
        &:first-child {
            text-align: right;
        }
    }
    thead {
        tr {
            &:first-child {
                th {
                    background-color: #fff;
                    position: -webkit-sticky;
                    position: sticky;
                    top: 0;
                }
            }
        }
    }
    tbody {
        tr {
            &:nth-of-type(odd) {
                background-color: rgba(0, 0, 0, .05);
            }
            &:hover {
                background-color: $dark-blue;
                color: #fff;
            }
        }
    }
    .table-hover {
        thead {
            tr {
                border-bottom: 1px solid #dce0e0;
                background-color: rgba(220, 224, 224, 0.35);
            }
        }
    }
}