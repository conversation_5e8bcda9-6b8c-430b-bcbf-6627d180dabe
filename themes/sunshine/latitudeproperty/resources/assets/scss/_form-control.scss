label {
    color: $primary;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 3px;
}
.form-control {
    font-family: "Montserrat", Sans-serif;
    border-color: #dce0e0;
    padding: 0.6rem 1rem;
    font-size: 14px;
    font-weight: 500;
}
.form-select {
    &:focus {
        border-color: $sky-blue;
        box-shadow: 0 0 0 0.25rem rgba($color: $sky-blue, $alpha: 0.3);;
    }
}
.btn {
    font-family: "Montserrat", Sans-serif;
    font-size: 15px;
    padding: 12px 24px;
    font-weight: 600;
    line-height: 1.4;
    transition: all .6s ease-in-out;
    &.btn-sm {
        font-size: 14px;
    }
    &.btn-slim {
        padding: 5px 10px;
        font-size: 12px;
    }
    &.save-search-btn {
        background-color: #a7afb2;
        color: #000000;
    }
    &.btn-primary {
        background-color: $primary;
        border-color: $primary;
        color: $sky-blue;
    }
    &.btn-green {
        background-color: #00A04D;
        border-color: #00A04D;
        color: #ffffff;
    }
    &.btn-dark-green {
        background-color: #007F1F;
        border-color: #007F1F;
        color: #ffffff;
    }
    &.btn-sky-blue {
        background-color: $sky-blue;;
        border-color: $sky-blue;
        &:hover, &:active {
            background-color: $light-grey;
            border-color: $light-grey;
        }
    }
    &:hover, &:active {
        background-color: $sky-blue;
        border-color: $sky-blue;
        color: $dark-blue;
        transition: all .6s ease-in-out;
    }
}

.ui-widget {
    width: 95%;
    margin: 0 auto;
    &.ui-widget-content {
        background: $dark-grey;
        border: none;
        height: 5px;
    }
}
.ui-slider {
    .ui-slider-range {
        background: $primary;
        height: 5px;
    }
    .ui-slider-handle {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: $primary;
        border: none;
        top: -0.5rem;
        margin-left: -0.5rem;
        cursor: pointer;
    }
}
.sort-by-select {
    .form-select {
        background-color: transparent;
        font-size: 14px;
        font-weight: 600;
        width: auto;
        &:focus {
            box-shadow: none;
            border: none;
        }
    }
}