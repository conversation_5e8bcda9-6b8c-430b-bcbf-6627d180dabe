.share-item-tool {
    color: #000;
    border: 1px solid #000;
    cursor: pointer;
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    text-align: center;
    margin-right: 5px;
    border-radius: 4px;
    display: inline-block;
    -webkit-transition: 0.2s;
    -o-transition: 0.2s;
    transition: 0.2s;
    &:hover {
        background-color: rgba(0, 0, 0, 0.85);
        color: #fff;
    }
    .dropdown-menu {
        border-color: transparent;
        background-color: rgba(0, 0, 0, 0.85);
        -webkit-box-shadow: none;
        box-shadow: none;
        font-size: 12px;
        font-weight: 700;
        text-align: left;
        text-transform: uppercase;
        z-index: 1021;
        .dropdown-item {
            padding: 10px;
            color: #fff;
            &:hover {
                background-color: black;
            }
        }
    }
}
.page-title-wrap {
    .label {
        font-size: 12px;
        padding: 7px 10px 6px;
        display: inline-block;
    }
}
.label-featured {
    background-color: #e200c4;
    color: #ffffff;
}
.property-form-wrap {
    background-color: #ffffff;
    border: 4px solid #0E2744;
    transition: background 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
    padding: 25px;
    label {
        font-size: 14px;
    }
    .form-control {
        color: #787878;
        border-radius: 4px;
        background: #f7f7f7;
        border: 1px solid #c4c4c4;
    }
}
.detail-wrap {
    background-color: rgba(0, 0, 0, 0.1);
    border: 1px solid #00aeff;
    border-radius: 4px;
    table {
        tr {
            td, th {
                font-size: 14px;
                padding-bottom: 0.4rem;
            }
        }
    }
}
span.label-sold.label {
    position: absolute;
    background: #ff0003;
    font-size: 35px;
    padding: 15px 35px;
    border-radius: 5px;
    bottom: 40%;
    left: 50%;
    z-index: 1;
    font-weight: 600;
    transform: translate(-50%, 0%);
}

.property-slider-large {
    .swiper-slide {
        aspect-ratio: 1 / 0.5;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}
.property-slider-thumb {
    margin-top: 1rem;
    .swiper-slide {
        aspect-ratio: 1 / 0.6;
        opacity: 0.6;
        cursor: pointer;
        &.swiper-slide-thumb-active {
            opacity: 1;
        }
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}