html {
    font-size: 16px;
    @media (max-width: 767px) {
        font-size: 14px;
    }
}
body {
    background-color: $dark-grey;
    color: $primary;
    font-family: "Montserrat", Sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    min-height: 100vh;
}
img {
    max-width: 100%;
}
a {
    color: $sky-blue;
    text-decoration: none;
}

.section-spacing {
    padding: 4rem 0;
    @media (max-width: 991px) {
        padding: 2.5rem 0;
    }
}
.dark-blue-bg {
    background-color: $dark-blue;
}
.bg-red {
    background-color: $red;
}
.bg-yellow {
    background-color: $yellow;
}
.bg-imperial {
    background-color: $imperial;
}
.bg-green {
    background-color: $green;
}
.text-sky-blue {
    color: $sky-blue;
}
.text-dark-grey {
    color: $dark-grey;
}
.text-body {
    color: $primary;
}
.text-black {
    color: $black;
}
.text-grey {
    color: $grey;
}
.text-xs {
    font-size: 12px;
}

.main-title {
    padding: 0.6rem 1rem;
    border-radius: 3rem;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}
.text-shadow-none {
    text-shadow: none;
}

.tooltip-inner {
    font-size: 0.8rem;
}

.offcanvas {
    width: 300px !important;
    visibility: visible;
}
.start-auto {
    left: auto;
}
.top-auto {
    top: auto;
}

.breadcrumb-item {
    color: #0e2745;
    a {
        color: #0e2745;
    }
    font-size: 13px;
    & + .breadcrumb-item {
        &:before {
            content: "\e912";
            font-family: 'iconfont';
            font-size: 10px;
            display: inline-block;
            padding-right: .5rem;
            float: none;
        }
    }
    &.active {
        color: #6c757d;
    }
}

.bg-cover {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}
.overlay {
    position: relative;
    z-index: 1;
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba($color: $black, $alpha: 0.6);
        z-index: -1;
    }
}
