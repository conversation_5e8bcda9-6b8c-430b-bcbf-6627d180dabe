    .hero-section {
    padding: 5rem 0;
    position: relative;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: 1;
    @media (max-width: 767px) {
        padding: 2.5rem 0;
    }
}
.hero-form {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 12px;
}

.property-style-item {
    aspect-ratio: 1 / 0.23;
    @media (max-width: 991px) {
        aspect-ratio: 1 / 0.18;
    }
    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}
.buy-property-banner {
    max-height: 520px;
    width: 100%;
    object-fit: cover;
}

.blog-item {
    .blog-image {
        display: block;
        padding-top: 70%;
        position: relative;
        overflow: hidden;
        img {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}

.labels-wrap {
    &.labels-right {
        display: flex;
        flex-wrap: wrap;
        justify-content: end;
        gap: 8px 0;
        a {
            margin-left: 3px;
        }
    }
}
.label {
    font-size: 10px;
    line-height: 11px;
    font-weight: 500;
    margin: 0;
    text-transform: uppercase;
    padding: 3px 5px;
    color: #fff;
    margin-left: 3px;
    background-color: rgba(0, 0, 0, 0.65);
    border-radius: 2px;
    &.status-color-1 {
        background-color: #622977;
    }
    &.status-color-2 {
        background-color: #007f1f;
    }
    &.status-color-3 {
        background-color: #fdd023;
    }
    &.status-color-4 {
        background-color: #00a04d;
    }
}
.item-wrap {
    box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.07);
    background-color: #FFFFFF;
    border-radius: 4px 4px 0 0;
    .label-featured {
        background-color: #ff3366;
        color: #ffffff;
        bottom: 5px;
        left: 20px;
        top: unset;
        position: absolute;
        z-index: 999;
    }
    .item-price-wrap {
        position: absolute;
        bottom: 30px;
        left: 20px;
        color: #fff;
        font-weight: 600;
        z-index: 2;
        .item-price {
            font-size: 19px;
            font-weight: 700;
        }
    }
    .item-tools {
        position: absolute;
        bottom: 20px;
        right: 20px;
        z-index: 2;
        .item-tool {
            display: inline-block;
            span {
                color: #000;
                border: 1px solid transparent;
                background-color: #fff;
                cursor: pointer;
                width: 30px;
                height: 30px;
                line-height: 30px;
                font-size: 14px;
                text-align: center;
                border-radius: 4px;
                display: inline-block;
                -webkit-transition: 0.2s;
                -o-transition: 0.2s;
                transition: 0.2s;
                &:hover {
                    background-color: #000000;
                    color: #FFFFFF;
                }
            }
        }
    }
}
.item-header {
    margin: 10px;
    position: relative;
}
.hover-effect {
    cursor: pointer;
    overflow: hidden;
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
    color: #0e2745;
    -webkit-transition: 0.2s;
    -o-transition: 0.2s;
    transition: 0.2s;
    &:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: block;
        opacity: 1;
        background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0)), color-stop(0%, rgba(0, 0, 0, 0)), color-stop(50%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.75)));
        background-image: -o-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.75) 100%);
        background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.75) 100%);
        -webkit-transition: 0.2s;
        -o-transition: 0.2s;
        transition: 0.2s;
        z-index: 1;
    }
    &:hover:before {
        opacity: 0;
    }
}
.item-listing-wrap {
    position: relative;
    .labels-wrap {
        position: absolute;
        z-index: 1;
        top: 17px;
        right: 20px;
        &.item-wrap-no-frame {
            .hover-effect {
                border-radius: 4px 4px 0 0;
            }
        }
    }
    .item-body {
        width: 100%;
        padding: 10px 15px 45px 15px;
        position: relative;
    }
    .item-title {
        font-family: "Montserrat", Sans-serif;
        font-size: 15px;
        font-weight: 700;
        white-space: nowrap;
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        a {
            font-size: 15px;
            font-weight: 700;
            color: #222222;
        }
    }
    .item-address {
        margin-bottom: 15px;
        color: #636363;
        font-size: 13px;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
    .item-title, .item-address {
        margin-right: 100px !important;
    }
    .item-amenities {
        font-size: 14px;
        line-height: 16px;
        font-weight: 500;
        padding: 15px 0 0;
        li {
            font-family: "Montserrat", Sans-serif;
            font-size: 9px;
            font-weight: 400;
            border-right: 1px solid #dce0e0;
            padding: 0 10px;
            color: #000000;
            margin-right: 0;
            white-space: nowrap;
            margin-bottom: 10px;
            display: inline-block;
            .hz-figure {
                font-family: "Montserrat", Sans-serif;
                font-weight: 500;
                color: #000000;
                font-size: 17px;
                margin-bottom: 4px;
                display: block;
            }
            i {
                color: #000000;
            }
            &:first-of-type {
                padding: 0 10px 0 0;
            }
            &:last-of-type {
                border-right: none;
            }
        }
    }
    &.list-view {
        width: 100%;
        .item-header {
            max-width: 280px;
        }
        .item-wrap {
            & > .d-flex {
                flex-direction: row !important;
            }
        }
        .hover-effect {
            border-radius: 4px 0 0 4px;
        }
        .item-body {
            position: relative;
            padding: 1rem;
            height: 100%;
            .custom-attr-wrapper {
                top: 51%;
                transform: translate(0px, -40%);
                .custom-attr-return, .custom-attr-yield {
                    width: 112px !important;
                }
            }
            .labels-wrap {
                top: -22px;
                left: 2px;
                margin-bottom: 10px;
            }
            .item-address {
                max-width: 400px;
            }
            .item-amenities {
                padding: 0;
                margin-bottom: 10px;
                height: 40px;
            }
        }
        @media (min-width: 1200px) {
            .item-body {
                .item-title {
                    max-width: 360px;
                }
            }
        }
    }
}
.custom-attr-wrapper {
    line-height: initial;
    position: absolute;
    right: 0;
    font-weight: bold;
    font-size: .8rem;
    width: 100px;
    text-align: center;
    .custom-attr-return, .custom-attr-yield {
        margin-bottom: 10px !important;
        background-color: #00203d;
        padding: 3px;
        width: 100px;
        color: #00defd !important;
        border-top-left-radius: 60px !important;
        border-bottom-left-radius: 60px !important;
        float: right;
        border-bottom: 2px solid white;
        font-family: 'Montserrat';
        font-size: 11px;
    }
}
.compare-property-label {
    background-color: $dark-blue;
    width: 40px;
    height: 40px;
    position: absolute;
    line-height: 40px;
    top: 50%;
    left: -40px;
    text-align: center;
    color: #fff;
    border-radius: 4px 0 0 4px;
    border: none;
    .compare-label {
        background-color: #85c341;
        font-size: 16px;
        font-weight: 500;
        position: absolute;
        width: 24px;
        height: 24px;
        line-height: 24px;
        border-radius: 50%;
        top: -5px;
        left: -12px;
        display: block;
    }
}

@media (min-width: 1200px) {
    .modal-xl {
        --bs-modal-width: 1170px;
    }
}
.newsletter-modal {
    .btn-close {
        cursor: pointer;
        position: absolute;
        top: -16px;
        right: -18px;
        width: auto;
        margin: 0;
        padding: 18px;
        opacity: 1;
        z-index: 1;
        overflow: visible;
        background-color: #FFFFFF;
        -moz-border-radius: 100%;
        -webkit-border-radius: 100%;
        border-radius: 100%;
        outline: none;
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        @media (max-width: 767px) {
            top: 5px;
            right: 5px;
        }
    }
}
.preview-modal {
    .modal-xl {
        --bs-modal-width: 95%;
    }
    .modal-header {
        background-color: $dark-blue;
        color: #ffffff;
        .logo {
            img {
                width: 80px;
                height: auto;
            }
        }
        .btn-close {
            opacity: 1;
        }
    }
    p {
        font-size: 14px;
    }
    .property-overview-data {
        .flex-fill {
            width: calc(100% / 3.1);
            display: inline-block;
            padding: 5px 0;
        }
    }
}
.swiper-button {
    height: 40px;
    width: 40px;
    border-radius: 6px;
    background-color: $dark-blue;
    color: $sky-blue;
    margin-top: 0;
    transform: translateY(-50%);
    &::after {
        font-size: 1rem;
    }
    &:hover {
        opacity: 0.8;
    }
}
.property-slider {
    .btn-expand {
        cursor: pointer;
        position: absolute;
        top: 15px;
        right: 15px;
        background-color: #ffffff;
        height: 40px;
        width: 40px;
        line-height: 40px;
        text-align: center;
        z-index: 2;
    }
}
.btn-expand {
    cursor: pointer;
}
.property-slider-col {
    &.full {
        width: 100%;
        & + .col-xl-3 {
            display: none;
        }
    }
}
