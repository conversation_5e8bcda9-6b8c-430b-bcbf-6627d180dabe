@media (max-width: 991px) {
    .properties-catergory-sidebar {
        .properties-catergory-title {
            position: relative;
            &::after {
                content: "\e912";
                font-family: 'iconfont' !important;
                position: absolute;
                top: 5px;
                left: auto;
                right: 0;
                bottom: auto;
                font-size: 14px;
                transition: all 0.6s ease-in-out;
            }
            &.show {
                &::after {
                    content: "\e910";
                    transition: all 0.6s ease-in-out;
                }
            }
        }
        .properties-catergory-list {
            display: none;
        }
    }
}
.pagination {
    .page-item {
        .page-link {
            margin: 0 3px;
            color: $primary;
            border: none;
            width: 40px;
            line-height: 40px;
            text-align: center;
            padding: 0;
            font-size: 14px;
            font-weight: 500;
            margin-left: 1px;
            border-radius: 4px;
            &:hover {
                background-color: #dce0e0;
            }
            &.active {
                background-color: $primary;
                color: #fff;
            }
        }
    }
}