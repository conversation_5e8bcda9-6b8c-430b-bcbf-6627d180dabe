.header {
    .navbar {
        background-color: $dark-blue;
        padding: 10px 0;
    }
}
.header-logo {
    img {
        width: 200px;
        height: auto;
    }
}
.header-cta-item {
    .header-cta-link {
        @media (max-width: 991px) {
            font-size: 1.6rem;
        }
        @media (max-width: 575px) {
            font-size: 1.4rem;
        }
    }
}
.nav-item {
    .nav-link {
        font-size: 13px;
        color: $sky-blue;
        padding: 13px 20px !important;
        transition: all 0.6s ease-in-out;
        @media (max-width: 1199px) {
            padding: 8px 12px !important;
        }
        @media (max-width: 991px) {
            color: #fff;
            border-bottom: 1px solid rgba(255, 255, 255, 0.388);
            text-align: center;
        }
        &.active, &:hover, &:focus {
            color: #fff;
            transition: all 0.6s ease-in-out;
            @media (max-width: 991px) {
                background-color: #ffffff;
                color: $primary;
            }
        }
    }
}
.header-cta-item {
    .header-cta-link {
        font-size: 18px;
    }
}
.navbar-toggler {
    border: none;
    margin-left: 0.5rem;.
    margin-top: -5px;
    &[aria-expanded="false"] {
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 100%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
    }
    &[aria-expanded="true"] {
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e");
            background-size: 1.1rem;
        }
    }
}
.navbar-collapse {
    @media (max-width: 991px) {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        height: auto;
        background-color: $dark-blue;
        z-index: 10;
    }
}

.form-tools .control {
    color: #636363;
}
.form-tools {
    a {
        min-height: 24px;
        font-size: 14px;
        font-weight: 500;
        color: $primary;
    }
    .control {
        display: block;
        position: relative;
        padding-left: 30px;
        cursor: pointer;
        input:checked ~ .control__indicator:after {
            display: block;
        }
        input:checked ~ .control__indicator {
            background-color: $primary;
        }
        input {
            position: absolute;
            z-index: -1;
            opacity: 0;
        }
        .control__indicator {
            border: 1px solid #dce0e0;
            border-radius: 2px;
            -webkit-transition: 0.2s;
            -o-transition: 0.2s;
            transition: 0.2s;
            position: absolute;
            top: 0;
            left: 0;
            height: 20px;
            width: 20px;
            background: #ffffff;
            &::after {
                content: '';
                position: absolute;
                display: none;
                left: 6px;
                top: 2px;
                width: 6px;
                height: 10px;
                border: solid #fff;
                border-width: 0 2px 2px 0;
                -webkit-transform: rotate(45deg);
                transform: rotate(45deg);
            }
        }
    }
}
.login-modal {
    .modal-content {
        overflow: hidden;
    }
    .modal-body {
        padding: 1.5rem;
    }
    .modal-header {
        padding: 0;
        background-color: $dark-blue;
        border: none;
        .btn-close {
            background-size: 12px;
            margin-right: 10px;
            opacity: 1;
        }
    }
    .nav-tabs {
        border: none;
        .nav-item {
            .nav-link {
                font-size: 1rem;
                font-weight: 500;
                color: #ffffff;
                border-radius: 0;
                border: none;
                &.active {
                    background-color: #fff;
                    border: none;
                    color: $primary;
                }
            }
        }
    }
}
.login-form-wrap, .register-form-wrap {
    background-color: #fff;
    border: 1px solid #dce0e0;
    .form-group-field {
        position: relative;
    }
    .form-group {
        border-bottom: 1px solid #dce0e0;
        .form-control {
            padding-left: 42px;
            border: none;
        }
        &:last-child {
            border-bottom: none;
        }
        .icon::before {
            color: #636363;
            position: absolute;
            top: 14px;
            left: 18px;
            font-size: 14px;
            display: inline-block;
        }
    }
}