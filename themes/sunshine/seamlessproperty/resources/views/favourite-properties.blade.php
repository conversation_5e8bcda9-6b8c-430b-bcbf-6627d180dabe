<x-dashboard-layout>
    @section('title', 'Favorites')

    @section('dashboard-title', 'My Favorites')

    @push('plugin-css')
    {!! theme_style('css/sweetalert2.css') !!}
    @endpush

    @push('plugin-js')
    {!! theme_script('js/sweetalert2.js') !!}
    @endpush

    <div class="row">
        <div class="col-xl-12">
            <div
                class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 p20-xs mb30 overflow-hidden position-relative">
                <div class="row">
                    @if ($properties->count())
                        @foreach ($properties as $item)
                            @include('components.fav-property-item-card', ['item' => $item->property, 'favProperty' => $item])
                        @endforeach
                    @else
                    <div class="alert alert-danger">
                        <p>
                            No Record Found.
                        </p>
                    </div>
                    @endif

                </div>
                @if ($properties->count())
                <div class="row">
                    <div class="mbp_pagination text-center">
                        {{ $properties->links('vendor.pagination.dashboard') }}
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</x-dashboard-layout>
