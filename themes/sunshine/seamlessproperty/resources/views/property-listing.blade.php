<x-app-layout>
    @section('title', isset($propertyType) && $propertyType->meta_title ? $propertyType->meta_title : $title)
    @section('meta-keywords', isset($propertyType) && $propertyType->meta_keywords ? $propertyType->meta_keywords : NULL)
    @section('meta-description', isset($propertyType) && $propertyType->meta_description ? $propertyType->meta_description : NULL)

	@include('components.subdomain-property-stats')

	<!-- Page Header Section Start -->
	<div class="page-header parallaxie property-listing-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<!-- Subpage Header Box Start -->
					<div class="page-header-box">
						<h1 class="text-anime">{{ $title }}</h1>
						<nav class="wow fadeInUp" data-wow-delay="0.25s">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
                                @if( isset($propertyType) )
                                    <li class="breadcrumb-item"><a href="{{ route('property.listing') }}">{{ $title }}</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">{{ $propertyType->title }}</li>
                                @else
								    <li class="breadcrumb-item active" aria-current="page">{{ $title }}</li>
                                @endif
							</ol>
						</nav>
					</div>
					<!-- Subpage Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header Section End -->

    <!--============== Property Search Form Start ==============-->
    <div class="full-row p-0 mb-4 listing-page advance-search-box">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <livewire:property-advance-search
                        :formData="$searchData ?? []"
                        :propertyStatuses="$propertyStatuses"
                        :propertyTypes="$property_types ?? []"
                        :states="$states ?? []"
                        :bedroomsLists="$bedroomsLists ?? []"
                        :bathroomsLists="$bathroomsLists ?? []"
                        :propertyFeatures="$propertyFeatures ?? []"
                    />

                    <hr>
                </div>
            </div>
        </div>
    </div>
    <!--============== Property Search Form End ==============-->


    <div class="row align-items-center justify-content-between mb-4 list-page-details-subtitle">
        <div class="col-md-6">
            <h6 class="mb-0 fw-semibold small">{{ $lists->total() }}  {{__('Properties Found')}}</h6>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-end">
                <div class="sort-by-select d-flex align-items-center">
                    <label class="d-inline-block" for="sort">Sort by:</label>
                    <select id="sort" class="form-select bg-white" aria-label="Sort by filter">
                        <option value="default" data-sort-column='price' data-sort-order="asc" {{ data_get( $searchData ?? [], 'sort_by') == 'default' ? 'selected': '' }}>Default Order</option>
                        <option value="low_to_high" data-sort-column='price' data-sort-order="asc" {{ data_get( $searchData ?? [], 'sort_by') == 'low_to_high' ? 'selected': '' }}>Price - Low to High</option>
                        <option value="high_to_low" data-sort-column='price' data-sort-order="desc" {{ data_get( $searchData ?? [], 'sort_by') == 'high_to_low' ? 'selected': '' }}>Price - High to Low</option>
                        <option value="featured_first" data-sort-column='featured' data-sort-order="desc" {{ data_get( $searchData ?? [], 'sort_by') == 'featured_first' ? 'selected': '' }}>Featured Listings First</option>
                        <option value="old_to_new" data-sort-column='updated_at' data-sort-order="asc" {{ data_get( $searchData ?? [], 'sort_by') == 'old_to_new' ? 'selected': '' }}>Date - Old to New</option>
                        <option value="new_to_old" data-sort-column='updated_at' data-sort-order="desc" {{ data_get( $searchData ?? [], 'sort_by') == 'new_to_old' ? 'selected': '' }}>Date - New to Old</option>
                    </select>
                </div>
                <div class="dropdown">
                    <button class="btn save-search-btn fw-bold"  data-bs-toggle="dropdown" type="button"><i class="icon icon-alarm-bell me-1"></i> Save Search</button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" id="lw-save-search"><i class="icon icon-add"></i> Create New</a></li>
                        @auth

                            <livewire:saved-search-lists />
                        @endauth
                    </ul>
                </div>

                <ul class="d-none d-xl-flex align-items-center justify-content-end list-unstyled view-layout-action">
                    <li>
                        <a href="{{ route('property.map_view', ($searchData ?? [])) }}" class=" bg-transparent border-0 h5" type="button" data-bs-toggle="tooltip" data-bs-title="Map View" aria-label="List Icon">
                            <i class="fa fa-map"></i>
                        </a>
                    </li>
                    <li class="hide-on-mobile">
                        <a href="{{ route('property.search_results', (array_merge(($searchData ?? []), ['view-type' => 'list-view']))) }}" class="btn-grid bg-transparent border-0 h5 list-view-link {{ ( (isset($searchData) && data_get($searchData, 'view-type') ) ? 'enabled' : '' ) }}" type="button" data-bs-toggle="tooltip" data-bs-title="List View" aria-label="Grid Icon">
                            <i class="fa fa-list"></i>
                        </a>
                    </li>

                    <li >
                        <a class="btn-grid bg-transparent border-0 h5 grid-view-link {{ ( (isset($searchData) && data_get($searchData, 'view-type') ) ? '' : 'enabled' ) }}" href="{{ route('property.search_results', ( $searchData ?? [] ) ) }}" data-bs-toggle="tooltip" data-bs-title="Grid View" aria-label="Grid Icon">
                            <i class="fa fa-table"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

	<!-- Property Listing Page Start -->
	<div class="page-property-listing">
		<div class="container">
			<div class="row listing-container">
                @if( $lists->count() )
                    <div class="col-lg-8">
                        <!-- Property Listings Start -->
                        <div class="property-listings">
                            <div class="row {{ ( (isset($searchData) && data_get($searchData, 'view-type')) ? 'list-view' : '' ) }}">
                                @foreach ($lists as $item)
                                @include('components.property-item-card', ['item' => $item, 'wowEffectEnable' => true])

                                @endforeach

                            </div>

                            <div class="row pagination-container">
                                <div class="col-md-12">
                                    <!-- Post Pagination Start -->
                                    {{ $lists->links('vendor.pagination.default') }}
                                    <!-- Post Pagination End -->
                                </div>
                            </div>

                        </div>
                        <!-- Property Listings End -->
                    </div>
                @else
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                        <p>
                            No Properties Found.
                        </p>
                    </div>
                    </div>
                @endif

				<div class="col-lg-4">
					<!-- Property Sidebar Start -->
					<div class="property-sidebar">
						<!-- Property Category Box Start -->
						<div class="property-category-box wow fadeInUp" data-wow-delay="0.25s">
							<h3>Categories</h3>

							<ul>
                                @foreach ($property_types as $item)
                                <li><a href="{{ data_get($item, 'slug') }}">{{ data_get($item, 'title') == 'SMSF Single Contract' ? 'SMSF' : data_get($item, 'title') }}</a></li>
                                @endforeach

							</ul>
						</div>
						<!-- Property Category Box End -->

						<!-- Need Help Box Start -->
						<div class="need-help-box wow fadeInUp" data-wow-delay="0.5s">
							<div class="need-help-img">
								<figure>
									<img src="{{ theme_asset('images/need-help-bg.jpg') }}" alt="">
								</figure>

								<div class="need-help-content">
									<h3>Need Help?<br> Talk to Our Expert.</h3>
									<a href="tel:+123456789" class="btn-default"><i class="fa-solid fa-phone-volume"></i> +123 456 789</a>
								</div>
							</div>
						</div>
						<!-- Need Help Box End -->
					</div>
					<!-- Property Sidebar End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Property Listing Page End -->
</x-app-layout>
