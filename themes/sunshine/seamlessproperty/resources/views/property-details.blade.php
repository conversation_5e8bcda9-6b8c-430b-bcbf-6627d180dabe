<x-app-layout>
    @section('title', $property->getFullAddress())
    @section('meta-part')
        @if ( $property->propertyDescription )

            <meta name="keywords" content="{{ $property->propertyDescription->meta_keywords }}">
            <meta name="description" content="{{ $property->propertyDescription->meta_description }}">
            <meta name="og:description" content="{{ $property->propertyDescription->meta_description }}">
            @if( $property->getPrimaryImageUrl() )
            <meta name='og:image' content='{{ $property->getPrimaryImageUrl() }}'>
            @endif
            <meta name='og:url' content="{{ route('property.details', $property) }}">
        @endif
        <meta name='subject' content="{{ $property->getFullAddress() }}">
        <meta name='od:title' content="{{ $property->getFullAddress() }}">
        <meta name='url' content='{{ route('property.details', $property) }}'>

        @if ( $property->mapSetting )
        <meta name='og:latitude' content='{{ $property->mapSetting->latitude }}'>
        <meta name='og:longitude' content='{{ $property->mapSetting->longitude }}'>
        @endif

        @if ( $property->propertySetting )
        <meta name='og:street-address' content="{{ $property->propertySetting->street_address }}">
        <meta name='og:locality' content="{{ $property->propertySetting?->area?->name }}">
        <meta name='og:postal-code' content="{{ $property->propertySetting->postcode }}">
        <meta name='og:country-name' content="{{ $property->propertySetting?->country?->name }}">
        @endif
    @endsection


	<!-- Page Header Section Start -->
	<div class="page-header parallaxie">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<!-- Subpage Header Box Start -->
					<div class="page-header-box">
						<h1 class="text-anime">{{ $property->title }}</h1>
						<nav class="wow fadeInUp" data-wow-delay="0.25s">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
								<li class="breadcrumb-item"><a href="{{ route('property.listing') }}">Listing</a></li>
								<li class="breadcrumb-item active" aria-current="page">{{ $property->title }}</li>
							</ol>
						</nav>
					</div>
					<!-- Subpage Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header Section End -->

    	<!-- Property Sinlge Page Start -->
	<div class="page-property-single">
		<div class="container">
			<div class="row">
				<div class="col-lg-8 main-detail-container">
					<!-- Property Single Content Start -->
					<div class="property-single-content">
						<!-- Property Photos Slider Start -->
						<div class="property-photos-slider wow fadeInUp" data-wow-delay="0.25s">
							<div class="swiper">
								<div class="swiper-wrapper">
                                    @foreach ($allPropertyImages as $item)
									<!-- Property Photo Slide Start -->
									<div class="swiper-slide">
										<div class="property-photo-item">
											<figure class="image-anime">
												<img src="{{ $item }}" alt="{{ $property->title }}-{{ $loop->index }}">
											</figure>
										</div>
									</div>
									<!-- Property Photo Slide End -->
                                    @endforeach
								</div>

								<!-- If we need navigation buttons -->
								<div class="swiper-arrow-prev"><i class="fa-solid fa-arrow-left"></i></div>
								<div class="swiper-arrow-next"><i class="fa-solid fa-arrow-right"></i></div>
							  </div>
						</div>
						<!-- Property Photos Slider End -->

						<!-- Property Overview Start -->
						<div class="property-overview wow fadeInUp" data-wow-delay="0.5s">
							<div class="property-single-subtitle">
								<h3>Overview</h3>
							</div>

							<div class="property-overview-box">
								<!-- Property Overview Item Start -->
								<div class="property-overview-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-bedrooms.svg') }}" alt="">
									</div>

									<div class="property-overview-content">
										<h3>Bedrooms</h3>
										<p>{{ $property->bedrooms ?? 0 }}</p>
									</div>
								</div>
								<!-- Property Overview Item End -->

								<!-- Property Overview Item Start -->
								<div class="property-overview-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-bathrooms.svg') }}" alt="">
									</div>

									<div class="property-overview-content">
										<h3>Bathrooms</h3>
										<p>{{ $property->bathrooms ?? 0 }}</p>
									</div>
								</div>
								<!-- Property Overview Item End -->

								<!-- Property Overview Item Start -->
								<div class="property-overview-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-areas.svg') }}" alt="">
									</div>

									<div class="property-overview-content">
										<h3>Area</h3>
										<p>{{ $property->areaSizeWithPostfix() }}</p>
									</div>
								</div>
								<!-- Property Overview Item End -->

								<!-- Property Overview Item Start -->
								<div class="property-overview-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-garages.svg') }}" alt="">
									</div>

									<div class="property-overview-content">
										<h3>Garages</h3>
										<p>{{ $property->garages ?? 0 }}</p>
									</div>
								</div>
								<!-- Property Overview Item End -->

								<!-- Property Overview Item Start -->
								<div class="property-overview-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-built-year.svg') }}" alt="">
									</div>

									<div class="property-overview-content">
										<h3>Year Built</h3>
										<p>{{ $property->year_built ?? 'N/A' }}</p>
									</div>
								</div>
								<!-- Property Overview Item End -->

								<!-- Property Overview Item Start -->
								<div class="property-overview-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-plot-size.svg') }}" alt="">
									</div>

									<div class="property-overview-content">
										<h3>Plot Size</h3>
										<p>{{ $property->landAreaSizeWithPostfix() }}</p>
									</div>
								</div>
								<!-- Property Overview Item End -->
							</div>
						</div>
						<!-- Property Overview End -->
                        @if( $property->propertyDescription )
						<!-- About Property Start -->
						<div class="about-property wow fadeInUp" data-wow-delay="0.75s">
							<div class="property-single-subtitle">
								<h3>About This Property</h3>
							</div>

							<div class="about-property-content">
								{!! $property->propertyDescription->description !!}

								<div class="about-property-cta">
									<a href="{{ route('contact_us') }}" class="btn-default btn-border">Contact us</a>
									<a href="tel:+123456789" class="btn-default"><i class="fa-solid fa-phone-volume"></i> +123 456 789</a>
								</div>
							</div>
						</div>
						<!-- About Property End -->
                        @endif

                        @if ($property->propertyFeatures->count() )
						<!-- Property Amenities Start -->
						<div class="property-amenities wow fadeInUp" data-wow-delay="1s">
							<div class="property-single-subtitle">
								<h3>Features & Amenities</h3>
							</div>

							<div class="property-amenities-box">
								<ul>
                                    @foreach ($property->propertyFeatures as $item)
									    <li>{{ $item->title }}</li>
                                    @endforeach
								</ul>
							</div>
						</div>
						<!-- Property Amenities End -->
                        @endif
                        @if (
                                    $property->mapSetting &&
                                    $property->mapSetting->isActive() &&
                                    $property->mapSetting->latitude &&
                                    $property->mapSetting->longitude
                                )
						<!-- Property Map Start -->
						<div class="property-map-location wow fadeInUp" data-wow-delay="1.25s">
							<div class="property-single-subtitle">
								<h3>Map Location</h3>
							</div>

							<div class="property-map-iframe">
                                <div class="h400 bdrs3" id="map-canvas" style="height: 400px"></div>
							</div>
						</div>
						<!-- Property Map End -->
                        @endif
					</div>
					<!-- Property Single Content End -->
				</div>

				<div class="col-lg-4 right-side">
                    <div class="property-actions property-page">
                        <div class="about-property-cta">
                            <livewire:add-to-favourite-action :key='"add-fav".$property->id' :propertyId="$property->id" type="detail" />
                            <div class="d-inline-block btn-default item-tool item-compare {{ in_array( $property->id, session('compare_properties', []) ) ? 'remove-compare-property' : 'add-to-compare' }}" role="button" tabindex="0" data-property-id="{{ $property->id }}" aria-controls="offcanvasRight" aria-label="Compare">
                                <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Add to Compare" class="add-to-compare-icon">
                                    <i class="fa fa-add "></i> Add To Compare
                                </span>
                                <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Remove Compare" class="remove-compare-icon">
                                    <i class="fa fa-minus-circle "></i> Remove Compare
                                </span>
                            </div>
                        </div>
                    </div>
					<!-- Property Single Sidebar Start -->
					<div class="porperty-single-sidebar">
						<!-- Property Information Box Start -->
						<div class="property-info-box wow fadeInUp" data-wow-delay="0.25s">
							<h3>Other Details</h3>

							<div class="property-info-lists">
								<!-- Property Info item Start-->
								<div class="property-info-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-property-location.svg') }}" alt="">
									</div>

									<p>{{ $property->getFullAddress() }}</p>
								</div>
								<!-- Property Info item End-->

								<!-- Property Info item Start-->
								<div class="property-info-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-property-phone.svg') }}" alt="">
									</div>

									<p>(+0) 123 456 789</p>
								</div>
								<!-- Property Info item End-->

								<!-- Property Info item Start-->
								<div class="property-info-item">
									<div class="icon-box">
										<img src="{{ theme_asset('images/icon-property-price.svg') }}" alt="">
									</div>

									<p>{{ $property->priceWithPrefix() }}</p>
								</div>
								<!-- Property Info item End-->
							</div>
						</div>
						<!-- Property Information Box End -->

						<!-- Property Inquiry Form Start -->
						<div class="property-inquiry-box wow fadeInUp" data-wow-delay="0.5s">
							<h3>Send Inquiry</h3>
                            @if (config('company_data')?->isFeatureEnabled('Property Enquiries'))
                                <livewire:property-enquiry-form :propertyId="$property->id" :propertyTitle="$property->title" :agentId="$agent?->id" :alreadySubmitted="$propertyEnquirySubmitted"/>
                            @endif
						</div>
						<!-- Property Inquiry Form End -->
					</div>
					<!-- Property Single Sidebar End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Property Sinlge Page End -->

    @push('plugin-js')
        @if ($property->mapSetting && $property->mapSetting->isActive() )

            <script>
                var markerIcon = '{!!  theme_asset('images/favicon.png') !!}';
                var lat = {{ $property->mapSetting->latitude }};
                var lng = {{ $property->mapSetting->longitude }};
                var mapTitle = "{{ $property->title }}";
                var markerPin = "{{ theme_asset('images/seamless-property-map-marker-icon.png') }}";
                var mapDescription =`{{ $property->mapSetting->map_address ?? $property->getFullAddress() }}`;
                </script>
            <script src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_map.api_key') }}">
            </script>
            {!! theme_script('js/googlemaps1.js') !!}
        @endif
    @endpush
</x-app-layout>
