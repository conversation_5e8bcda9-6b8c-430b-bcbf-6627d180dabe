<x-app-layout>
    @section('title', 'Compare Properties')
    @section('page-css-classname', 'compare-properties-page')

    @section('meta-part')
        <meta name='subject' content="Compare Properties">
        <meta name='od:title' content="Compare Properties">

    @endsection

    <!-- Page Header Section Start -->
	<div class="page-header parallaxie cms-page-compare">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<!-- Subpage Header Box Start -->
					<div class="page-header-box">
						<h1 class="text-anime">Compare</h1>
						<nav class="wow fadeInUp" data-wow-delay="0.25s">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Compare</li>
							</ol>
						</nav>
					</div>
					<!-- Subpage Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header Section End -->

    <section class="section-spacing pt-0 mb-4">
        <div class="container pb-4 property-compare-box">
            @if( $properties->count() )
            <div class="table-responsive-md">
                <table class="compare-table table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>
                                <!-- empty -->
                            </th>
                            @foreach ($properties as $item)
                            <th>
                                <a href="{{ route('property.details', $item) }}">
                                    <div class="compare-image">
                                        <img src="{{ $item->getPrimaryImageUrl() ?? theme_asset('images/properties/Lot-1104-House-Render-1.jpg') }}" alt="{{ $item->title }}">
                                    </div>
                                </a>
                            </th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $columns = [
                                [
                                    'title' => 'Title',
                                    'key' => 'title'
                                ],
                                [
                                    'title' => 'Property Type',
                                    'clouser' => fn( $item ) => $item?->propertyTypes?->first()?->title
                                ],
                                [
                                    'title' => 'Price',
                                    'function' => 'priceWithPrefixPostfix',
                                    'fn_args' => [ 0 ]
                                ],
                                [
                                    'title' => 'Land Price',
                                    'function' => 'getLandPriceWithFormat',
                                    'fn_args' => [ 0 ]
                                ],
                                [
                                    'title' => 'Build Price',
                                    'function' => 'getBuildPriceWithFormat',
                                    'fn_args' => [ 0 ]
                                ],
                                [
                                    'title' => 'Gross Per Week',
                                    'function' => 'getWeeklyRentWithFormat',
                                    'fn_args' => [ 0 ]
                                ],
                                [
                                    'title' => 'Gross Per Annum',
                                    'function' => 'getGrossReturnFormat',
                                    'fn_args' => [ 0 ]
                                ],
                                [
                                    'title' => 'Gross Yield',
                                    'function' => 'getGrossYieldFormat'
                                ],
                                // [
                                //     'title' => 'SMSF Deposit',
                                //     'function' => 'getSmsfDeposit'
                                // ],
                                [
                                    'title' => 'Capital Growth 12 Months',
                                    'function' => 'getCapitalGrowthFormat'
                                ],
                                [
                                    'title' => 'Capital Growth 10 Year Annualised',
                                    'function' => 'getCapitalGrowthTenYearAnnualFormat'
                                ],
                                [
                                    'title' => 'Vacancy Rate',
                                    'function' => 'getVacancyRateFormat'
                                ],
                                [
                                    'title' => 'Property Size',
                                    'clouser' => fn( $item ) => (!empty( $item->area_size ) ? $item->area_size . 'm<sup>2</sup>' : 'N/A'),
                                    'is_html' => true
                                ],
                                [
                                    'title' => 'Land Area',
                                    'clouser' => fn( $item ) => (!empty( $item->land_area_size ) ? $item->land_area_size . 'm<sup>2</sup>' : 'N/A'),
                                    'is_html' => true
                                ],
                                [
                                    'title' => 'Bedrooms',
                                    'key' => 'bedrooms'
                                ],
                                [
                                    'title' => 'Bathrooms',
                                    'key' => 'bathrooms'
                                ],
                                [
                                    'title' => 'Parking',
                                    'key' => 'garages'
                                ],
                                [
                                    'title' => 'Title Status',
                                    'key' => 'title_status'
                                ],
                                [
                                    'title' => 'Floor Plan',
                                    'key' => 'floor_plan_text'
                                ],
                                [
                                    'title' => 'Property ID',
                                    'key' => 'property_id_text'
                                ],
                                [
                                    'title' => 'SKU',
                                    'clouser' => fn( $item ) => $item?->propertySetting?->property_sku
                                ],
                            ]
                        @endphp
                        @foreach ($columns as $col )

                            <tr>
                                <td><strong>{{ data_get( $col, 'title') }}</strong></td>
                                @foreach ($properties as $item)
                                <td>
                                    @if( data_get( $col, 'key') )
                                    {{ data_get( $item, data_get( $col, 'key') ) ?? '--' }}
                                    @elseif( $cb = data_get( $col, 'function') )
                                        @if( !empty( data_get( $col, 'fn_args' ) ) )
                                        {{ $item->$cb( ...data_get( $col, 'fn_args' )) ?? '--' }}

                                        @else
                                        {{ $item->$cb() ?? '--' }}
                                        @endif
                                    @elseif( $cb = data_get( $col, 'clouser') )
                                        @if( data_get( $col, 'is_html'))
                                        {!! $cb( $item) ?? '--' !!}

                                        @else
                                        {{ $cb( $item) ?? '--' }}
                                        @endif
                                    @endif
                                </td>
                                @endforeach
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="alert alert-danger">
                <p>
                    There are not properties added for the comparision. To comapare properties click on below link and add properties to compare.
                </p>
                <div class="w-100 text-center">

                    <a href="{{ route('property.listing') }}" class="btn btn-info btn-sm" rel="noopener noreferrer">
                        View Properties
                    </a>
                </div>
            </div>
            @endif
        </div>
    </section>
</x-app-layout>
