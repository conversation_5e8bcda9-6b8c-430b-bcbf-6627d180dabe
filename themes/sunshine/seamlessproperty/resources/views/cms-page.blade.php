<x-app-layout>
    @section('title', $cmsPage->name)
    @section('page-css-classname', 'cms-page ' . \Str::slug($cmsPage->name))
    @section('meta-part')
        <meta name="keywords" content="{{ $cmsPage->meta_keywords }}">
        <meta name="description" content="{{ $cmsPage->meta_description }}">
        <meta name="og:description" content="{{ $cmsPage->meta_description }}">
        @if( $cmsPage->getFirstMedia('feature_image') )
        <meta name='og:image' content='{{ $cmsPage->getFirstMediaUrl('feature_image')}}'>
        @endif
        <meta name='og:url' content="{{ route('cms_page', $cmsPage) }}">
        <meta name='url' content='{{ route('cms_page', $cmsPage) }}'>
        <meta name='subject' content="{{ $cmsPage->name }}">
        <meta name='od:title' content="{{ $cmsPage->name }}">

    @endsection

    <!-- Page Header Section Start -->
	<div class="page-header parallaxie cms-page-{{ $cmsPage->slug }}">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<!-- Subpage Header Box Start -->
					<div class="page-header-box">
						<h1 class="text-anime">{{ $cmsPage->name }}</h1>
						<nav class="wow fadeInUp" data-wow-delay="0.25s">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page">{{ $cmsPage->name }}</li>
							</ol>
						</nav>
					</div>
					<!-- Subpage Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header Section End -->

    <section class="section-spacing cms-page-container-{{ $cmsPage->slug }}">
        <div class="container">
            @if( $cmsPage->isIframe() )
                <iframe src="{{ $cmsPage->external_url }}" class="iframe-min-height" frameborder="0" width="100%"></iframe>
            @else
            <div class="bg-white p-4">
                {!! $cmsPage->content !!}
            </div>
            @endif
        </div>
    </section>
    @push('plugin-js')
    <script type="text/javascript">

        function resizeIframe( iframe ){
            iframe.style.height = iframe.contentWindow.document.documentElement.scrollHeight + 'px';
        }
    </script>
    @endpush
</x-app-layout>
