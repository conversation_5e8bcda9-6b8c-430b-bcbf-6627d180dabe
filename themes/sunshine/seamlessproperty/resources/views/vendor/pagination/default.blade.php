@if ($paginator->hasPages())
<div class="post-pagination wow fadeInUp" data-wow-delay="1.5s">
    <ul class="pagination">
        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <li class=" disabled active" aria-disabled="true" aria-label="@lang('pagination.previous')">
                <a href="#" aria-hidden="true"><i class="fa-solid fa-arrow-left-long"></i></a>
            </li>
        @else
            <li>
                <a href="{{ $paginator->previousPageUrl() }}" rel="prev" aria-label="@lang('pagination.previous')"><i class="fa-solid fa-arrow-left-long"></i></a>
            </li>
        @endif

        {{-- Pagination Elements --}}
        @foreach ($elements as $element)
            {{-- "Three Dots" Separator --}}
            @if (is_string($element))
                <li class="disabled active" aria-disabled="true"><a href="#">{{ $element }}</a></li>
            @endif

            {{-- Array Of Links --}}
            @if (is_array($element))
                @foreach ($element as $page => $url)
                    @if ($page == $paginator->currentPage())
                        <li class="active" aria-current="page"><a href="#">{{ $page }}</a></li>
                    @else
                        <li><a href="{{ $url }}">{{ $page }}</a></li>
                    @endif
                @endforeach
            @endif
        @endforeach

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <li>
                <a href="{{ $paginator->nextPageUrl() }}" rel="next" aria-label="@lang('pagination.next')"><i class="fa-solid fa-arrow-right-long"></i></span></a>
            </li>
        @else
            <li class="active" aria-disabled="true" aria-label="@lang('pagination.next')">
                <a href="#" aria-hidden="true"><i class="fa-solid fa-arrow-right-long"></i></a></span>
            </li>
        @endif
    </ul>
</div>
@endif
