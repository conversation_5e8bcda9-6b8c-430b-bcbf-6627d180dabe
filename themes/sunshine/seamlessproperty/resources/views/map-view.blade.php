<x-app-layout>
    @section('title', $title)
    @section('page-css-classname', 'property-listing-map-page')

    <!-- Page Header Section Start -->
	<div class="page-header parallaxie map-view-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<!-- Subpage Header Box Start -->
					<div class="page-header-box">
						<h1 class="text-anime">{{ $title }}</h1>
						<nav class="wow fadeInUp" data-wow-delay="0.25s">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
                                @if( isset($propertyType) )
                                    <li class="breadcrumb-item"><a href="{{ route('property.listing') }}">{{ $title }}</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">{{ $propertyType->title }}</li>
                                @else
								    <li class="breadcrumb-item active" aria-current="page">{{ $title }}</li>
                                @endif
							</ol>
						</nav>
					</div>
					<!-- Subpage Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header Section End -->

    <section class="section-spacing pt-0 map-view-page">
        <div class="container">
            <div class="bg-white rounded-3 p-3 shadow-md">
                <livewire:property-advance-search
                    searchFormRoute="property.map_view"
                    :formData="$searchData ?? []"
                    :propertyStatuses="$propertyStatuses"
                    :propertyTypes="$property_types ?? []"
                    :states="$states ?? []"
                    :bedroomsLists="$bedroomsLists ?? []"
                    :propertyFeatures="$propertyFeatures ?? []"
                />
            </div>
        </div>
    </section>



    <section class="section-spacing pt-0 ">
        <div class="container">

            <div class="row align-items-center justify-content-between mb-4">
                <div class="col-md-6">
                    {{-- <h6 class="mb-0 fw-semibold small">{{ $lists->total() }}  {{__('Properties Found')}}</h6> --}}
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end">
                        <div class="dropdown">
                            <button class="btn save-search-btn fw-bold"  data-bs-toggle="dropdown" type="button"><i class="icon icon-alarm-bell me-1"></i> Save Search</button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="lw-save-search"><i class="icon icon-add"></i> Create New</a></li>
                                @auth

                                    <livewire:saved-search-lists />
                                @endauth
                            </ul>
                        </div>
                        <ul class="d-none d-xl-flex align-items-center justify-content-end list-unstyled view-layout-action">
                            <li>
                                <button class="bg-transparent border-0 h5" type="button" data-bs-toggle="tooltip" data-bs-title="Map View" aria-label="List Icon">
                                    <i class="fa fa-map"></i>
                                </button>
                            </li>
                            <li class="hide-on-mobile">
                                <a href="{{ route('property.search_results', ['view-type' => 'list-view']) }}" class="btn-grid bg-transparent border-0 h5 list-view-link" type="button" data-bs-toggle="tooltip" data-bs-title="List View" aria-label="Grid Icon">
                                    <i class="fa fa-list"></i>
                                </a>
                            </li>

                            <li >
                                <a class="btn-grid bg-transparent border-0 h5 grid-view-link" href="{{ route('property.listing') }}" data-bs-toggle="tooltip" data-bs-title="Grid View" aria-label="Grid Icon">
                                    <i class="fa fa-table"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="map-container">

        <div class="row gy-4">
            <div class="col-12">
                <div class="map-canvas half_style" id="map-canvas" data-map-zoom="9" data-map-scroll="true" style="height: 90vh;">
                </div>
            </div>
        </div>
    </section>
    @push('plugin-js')
        <script>
            var markerIcon = '{!!  theme_asset('images/seamless-property-map-marker-icon.png') !!}';
            var ajaxRequestUrl = '{{ route('property.ajax_map_data') }}';
            var searchData = @json( $searchData ?? []);
            @if( !empty( $mapCenter ) )
            var mapCenterPoint = @json( $mapCenter );
            @endif
            // var themeWiseMarkerClickEv = ( data ) => {
            //     console.log('MarkerId', data );

            // }
        </script>
        <script src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_map.api_key') }}">
        </script>
        <script src="{{url('js/google-map-cluster.min.js')}}"></script>
        <script src="{{url('js/map-view.js')}}"></script>
    @endpush
</x-app-layout>
