<div>

    @if( $showSignIn )
        @if ($isVerifiedRegistration)
            <div class="alert alert-success">
                <p>
                    Success! Verification successfully done.
                </p>
            </div>
        @endif
        @if ($errorMessage)
            <div class="alert alert-danger">
                <p>
                    {{ $errorMessage }}
                </p>
                @if ( $optionResendVerification )
                    <a href="#" wire:click.prevent="resendEmailVerificationLink">
                        Resend Link <i class="fa fa-spin spinner" wire:loading></i>
                    </a>
                @endif
            </div>
        @endif
        @if ($successMesasge)
            <div class="alert alert-success">
                <p>
                    {{ $successMesasge }}
                </p>
            </div>
        @endif
        @if( $formType == 'login' )
            <div class="row wow fadeInRight" data-wow-delay="300ms">
                <div class="col-lg-6">

                    <div class="log-reg-form signup-modal form-style1 bgc-white p50 p30-sm default-box-shadow2 bdrs12">
                        <div class="text-center mb40">
                            <img class="mb25" src="images/header-logo2.svg" alt="">
                            <h2>Sign In</h2>
                            <p class="text">Sign in with this account across the following sites.</p>
                        </div>
                        <div class="mb25">
                            <label class="form-label fw600 dark-color">Email</label>
                            <input type="email" class="form-control" placeholder="Enter Email" wire:model="login.email">
                             @error ( 'login.email' )
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb15">
                            <label class="form-label fw600 dark-color">Password</label>
                            <input type="password" class="form-control" placeholder="Enter Password" wire:model="login.password">
                            @error ( 'login.password' )
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="checkbox-style1 d-block d-sm-flex align-items-center justify-content-between mb10">
                            <label class="custom_checkbox fz14 ff-heading">Remember me
                                <input type="checkbox" checked="checked" wire:model="login.remember">
                                <span class="checkmark"></span>
                            </label>
                            <a class="fz14 ff-heading" href="#" wire:click.prevent="changeScreenInLoginTab('forgot_password')">Lost your password?</a>
                        </div>
                        <div class="d-grid mb20">
                            <button class="ud-btn btn-thm" type="button" wire:click.prevent="submitLogin">Sign in <i class="fal fa-arrow-right-long"></i> <i class="fa fa-spin fa-spinner" wire:loading wire:target="submitLogin"></i></button>
                        </div>
                        <div class="hr_content mb20"><hr><span class="hr_top_text">OR</span></div>
                        <p class="dark-color text-center mb0 mt10">Not signed up? <a class="dark-color fw600" href="{{ route('register') }}">Create an account.</a></p>
                    </div>
                </div>
            </div>

        @endif
        @if( $formType == 'forgot_password' )
            <form wire:submit="sendResetPasswordLink">
                <div class="row">

                    <div class="col-lg-6">
                        <div class="log-reg-form signup-modal form-style1 bgc-white p50 p30-sm default-box-shadow2 bdrs12">
                            <div class="text-center mb40">
                                <img class="mb25" src="images/header-logo2.svg" alt="">
                                <h2>Forgot Password?</h2>
                                <p class="text">Sign in with this account across the following sites.</p>
                            </div>
                            <div class="mb25">
                                <label class="form-label fw600 dark-color">Email</label>
                                <input type="email" class="form-control" placeholder="Enter Email" wire:model="login.email">
                                    @error ( 'login.email' )
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="d-grid mb20">
                                <button type="submit" class="ud-btn btn-thm" wire:loading.attr="disabled">Send Reset Link <i class="fa fa-spin spinner" wire:loading></i></button>
                                <div class="hr_content mb20"><hr><span class="hr_top_text">OR</span></div>
                                <a class="dark-color fw600" wire:loading.attr="disabled" wire:click.prevent="changeScreenInLoginTab('login')">Back to Login <i class="fa fa-spin spinner" wire:loading></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        @endif
    @elseif( $showRegister )
    <form wire:submit="submitRegisteration">
        @if ($successMesasge)
            <div class="alert alert-success">
                <p>
                    {{ $successMesasge }}
                </p>
            </div>
        @endif


        <div class="row wow fadeInRight" data-wow-delay="300ms">
            <div class="col-lg-6">
                <div
                    class="log-reg-form signup-modal form-style1 bgc-white p50 p30-sm default-box-shadow2 bdrs12">
                    <div class="text-center mb40">
                        <img class="mb25" src="images/header-logo2.svg" alt="">
                        <h2>Create account</h2>
                        <p class="text">Sign in with this account across the following sites.</p>
                    </div>
                    <div class="mb25">
                        <label class="form-label fw600 dark-color">Your Name</label>
                        <input type="email" class="form-control" placeholder="Enter Email" wire:model="register.name">
                        @error ( 'register.name' )
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mb25">
                        <label class="form-label fw600 dark-color">Phone</label>
                        <input type="text" class="form-control" placeholder="Enter Phone" wire:model="register.phone">
                        @error ( 'register.phone' )
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mb25">
                        <label class="form-label fw600 dark-color">Email</label>
                        <input type="email" class="form-control" placeholder="Enter Email" wire:model="register.email">
                        @error ( 'register.email' )
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mb15">
                        <label class="form-label fw600 dark-color">Password</label>
                        <input type="password" class="form-control" placeholder="Enter Password" wire:model="register.password">
                        @error ( 'register.password' )
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mb15">
                        <label class="form-label fw600 dark-color">Re-Enter Password</label>
                        <input type="password" class="form-control" placeholder="Enter Password" wire:model="register.confirm_password">
                        @error ( 'register.confirm_password' )
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div
                        class="checkbox-style1 d-block d-sm-flex align-items-center justify-content-between mb10">
                        <label class="custom_checkbox fz14 ff-heading">Accept Terms and Conditions
                            <input type="checkbox" wire:model="register.terms">
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    <div class="d-grid mb20">
                        <button class="ud-btn btn-thm" type="button" wire:click.prevent="submitRegisteration">Create Account <i
                                class="fal fa-arrow-right-long"></i> <i class="fa fa-spin fa-spinner" wire:loading wire:target="submitRegisteration"></i></button>
                    </div>
                    <div class="hr_content mb20">
                        <hr><span class="hr_top_text">OR</span></div>
                    <p class="dark-color text-center mb0 mt10">Already Registered? <a class="dark-color fw600"
                            href="{{ route('signin') }}">Login.</a></p>
                </div>
            </div>
        </div>
            @if(config('company_data')?->isFeatureEnabled('Social Login') && data_get( config('company_data'), 'companySetting.settings.social_login'))
                @if(config('services.google.isEnabled'))
                    <div class="col">
                        <a href="{{ route('login.redirection', 'google') }}"
                        class="btn"
                        style="background-color: #DD4B39; color: #ffffff; font-weight: 500; padding: 0px 25px; font-size: 15px; line-height: 45px;">
                            <svg xmlns="http://www.w3.org/2000/svg" height="15" width="15" viewBox="0 0 488 512" style="margin-right: 8px;">
                                <path fill="#ffffff" d="M488 261.8C488 403.3 391.1 *********** 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"></path>
                            </svg>
                            Sign Up With Google
                        </a>
                    </div>
                @endif
                @if(config('services.facebook.isEnabled'))
                    <div class="col">
                        <a href="{{ route('login.redirection', 'facebook') }}"
                        class="btn"
                        style="background-color: #3B55A0; color: #ffffff; font-weight: 500; padding: 0px 25px; font-size: 15px; line-height: 45px;">
                            <svg xmlns="http://www.w3.org/2000/svg" height="15" width="15" viewBox="0 0 512 512" style="margin-right: 8px;">
                                <path fill="#ffffff" d="M512 256C512 114.6 397.4 0 256 0S0 114.6 0 256C0 376 82.7 476.8 194.2 504.5V334.2H141.4V256h52.8V222.3c0-87.1 39.4-127.5 125-127.5c16.2 0 44.2 3.2 55.7 6.4V172c-6-.6-16.5-1-29.6-1c-42 0-58.2 15.9-58.2 57.2V256h83.6l-14.4 78.2H287V510.1C413.8 494.8 512 386.9 512 256h0z"></path>
                            </svg>
                            Sign Up With Facebook
                        </a>
                    </div>
                @endif
            @endif
            <div class="col">
                <a href="{{ route('static_page', 'terms-condition') }}" class="btn-link text-dark">View Terms and Condition</a>
            </div>
        </div>
    </form>
    @endif

<!-- Reset Password Modal -->
</div>
