<div wire:key="add_fav_{{ $propertyId }}">
    @if( $type == 'detail')

        <a href="{{ route('contact_us') }}"
            wire:click.prevent="processAddToFav"
            :key="'add_fave_pro_' . $propertyId"
            class="btn-default btn-border {{ $isFavourite ? 'btn-favorited' : '' }}" >
            <i class="fa fa-spin fa-spinner" wire:loading wire:target="processAddToFav"></i>
            <i class="fa fa-heart {{ $isFavourite ? 'text-warning fill-favourite' : '' }}" wire:loading.class="d-none" wire:target="processAddToFav"></i>

            {{ $isFavourite ? ' Favorited' : ' Favorite' }}
        </a>
    @else
        <span

            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-title="Favourite"
            wire:click="processAddToFav"
            :key="'add_fave_pro_' . $propertyId"
            >
            <i class="fa fa-spin fa-spinner" wire:loading wire:target="processAddToFav"></i>
            <i class="fa fa-heart {{ $isFavourite ? 'text-warning fill-favourite' : '' }}" wire:loading.class="d-none" wire:target="processAddToFav"></i>
        </span>

    @endif
</div>

