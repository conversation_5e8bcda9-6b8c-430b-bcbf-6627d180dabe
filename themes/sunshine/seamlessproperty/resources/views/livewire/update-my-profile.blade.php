<div class="col-lg-12">
    <form class="form-style1" wire:submit.prevent="submit">
        <div class="row">
            <div class="col-sm-6 col-xl-4">
                <div class="mb20">
                    <label
                        class="heading-color ff-heading fw600 mb10">Full Name</label>
                    <input type="text" class="form-control" placeholder="Yours Name" wire:model="name">
                    @error('name')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="col-sm-6 col-xl-4">
                <div class="mb20">
                    <label class="heading-color ff-heading fw600 mb10">Email</label>
                    <input type="email" class="form-control" placeholder="Your Email" wire:model="email">
                    @error('email')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="col-sm-6 col-xl-4">
                <div class="mb20">
                    <label class="heading-color ff-heading fw600 mb10">Phone</label>
                    <input type="text" class="form-control" placeholder="Phone Number" wire:model="phone">
                    @error('phone')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="text-end d-flex justify-content-end align-items-baseline gap-2">
                    @if ($successMesasge)
                        <p class="text-success">
                            {{ $successMesasge }}
                        </p>
                    @endif
                    <a class="ud-btn btn-dark" href="#" wire:click.prevent="submit">Update Profile<i class="fal fa-arrow-right-long"></i> <i class="fa fa-spin fa-spinner" wire:loading wire:target="submit"></i></a>
                </div>
            </div>
        </div>
    </form>
</div>
