
<div class="property-inquiry-form">
    @if ($successMesasge)

        <div class="alert alert-success">
            <p>
                {{ $successMesasge}}
            </p>
        </div>
    @endif
    @if( $alreadySubmitted)
        <div class="alert alert-warning">
            <p>
                Enquiry has already been submitted.
            </p>
        </div>
    @endif

    <form id="property-enquiry-form" action="#" method="POST" wire:submit.prevent="submit()">
        <div class="row">
            <div class="form-group col-md-6 mb-3">
                <input type="text" name="first_name" class="form-control" id="first_name" placeholder="First Name" required wire:model="first_name" >
                <div class="help-block with-errors">
                    @error ( 'first_name' )
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="form-group col-md-6 mb-3">
                <input type="text" name="last_name" class="form-control" id="last_name" placeholder="Last Name" required wire:model="last_name"  >
                <div class="help-block with-errors">
                    @error ( 'last_name' )
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <div class="form-group col-md-12 mb-3">
                <input type="email" name ="email" class="form-control" id="email" placeholder="Email" required wire:model="email" >
                <div class="help-block with-errors">
                    @error ( 'email' )
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <div class="form-group col-md-12 mb-3">
                <input type="text" name="phone" class="form-control" id="phone" placeholder="Phone" required wire:model="phone" >
                <div class="help-block with-errors">
                    @error ( 'phone' )
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <div class="form-group col-md-12 mb-3">
                <textarea name="msg" class="form-control" id="msg" rows="4" placeholder="Write a Message" required wire:model="message"></textarea>
                <div class="help-block with-errors">
                    @error ( 'message' )
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <div class="col-md-12 text-center">
                <button type="submit" class="btn-default">Send Message <i class="fa fa-spin fa-spinner" wire:loading wire:target="submit"></i></button>
                <p class="mb-0 small text-center fw-medium">By submitting this form you agree to our Terms & Conditions</p>
                <div id="msgSubmit" class="h3 text-left hidden"></div>
            </div>
        </div>
    </form>
</div>
