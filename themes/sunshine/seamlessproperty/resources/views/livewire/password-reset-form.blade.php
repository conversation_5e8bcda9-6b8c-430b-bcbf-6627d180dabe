<div class="row wow fadeInRight" data-wow-delay="300ms">
@if ($successMesasge)

    <div class="row row-cols-1 g-3">
        <div class="col-md-12">
            <div class="alert alert-success">
                <p>
                    {{ $successMesasge }}
                </p>
            </div>
        </div>
    </div>
@endif
<div class="col-lg-6">
    <div
        class="log-reg-form signup-modal form-style1 bgc-white p50 p30-sm default-box-shadow2 bdrs12">
        <div class="text-center mb40">
            <img class="mb25" src="{{ theme_asset('images/header-logo2.svg') }}" alt="" width="200px">
            <h2>Reset Password</h2>
        </div>
        <div class="mb25">
            <label class="form-label fw600 dark-color">Email</label>
            <input type="email" class="form-control" placeholder="Enter Email" value="{{ $email }}" readonly>
            @error ( 'email' )
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="mb15">
            <label class="form-label fw600 dark-color">New Password</label>
            <input type="password" class="form-control" placeholder="Enter New Password" wire:model="password">
            @error ( 'password' )
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="mb15">
            <label class="form-label fw600 dark-color">Re-type New Password</label>
            <input type="password" class="form-control" placeholder="Re-type New Password" wire:model="password_confirmation">
            @error ( 'password_confirmation' )
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="d-grid mb20">
            <button class="ud-btn btn-thm" type="button" wire:click.prevent="submit()" wire:loading.attr="disabled">Reset Password <i
                    class="fal fa-arrow-right-long"></i></button>
        </div>
        <div class="hr_content mb20">
            <hr><span class="hr_top_text">OR</span></div>
        <p class="dark-color text-center mb0 mt10"><a class="dark-color fw600"
                href="{{ route('signin') }}">Go To Sign In.</a></p>
    </div>
</div>
</div>
