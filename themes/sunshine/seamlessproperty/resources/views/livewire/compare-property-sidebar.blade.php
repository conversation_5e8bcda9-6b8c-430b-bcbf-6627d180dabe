<div class="offcanvas offcanvas-end {{ count($propertyIds) ? 'active': '' }} {{ $isOpen ? 'show' : '' }}" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="compare-sidebar" aria-labelledby="compareItemLabel">
    <button class="compare-property-label" data-bs-toggle="offcanvas" data-bs-target="#compare-sidebar" data-bs-target="#offcanvasScrolling" aria-controls="compare-sidebar">
        <span class="compare-count compare-label">{{ count($propertyIds) }}</span>
        <i class="fa fa-right-left"></i>
    </button>
    <div class="offcanvas-header pb-0">
        <h2 class="h6 fw-semibold offcanvas-title" id="compareItemLabel">Compare listings</h5>
    </div>
    <div class="offcanvas-body">
        @if( $limitErr)
        <div class="row g-2 mb-2">
            <div class="col-12">
                <p class="text-danger">
                    You can compare maximum {{ $maxLimit }} properties.
                </p>
            </div>
        </div>
        @endif
        <div class="row g-2 mb-2">
            @foreach ($properties as $key => $item)
            <div class="col-6 position-relative">
                <a href="#" aria-label="Cross Rounded Icon" wire:click.prevent="removeProperty({{ $item->id }})" class="d-block position-absolute bottom-0 end-0 start-auto top-auto text-white pe-4"><i role="img" class="fa fa-trash"></i></a>
                <img class="img-fluid" src="{{ $item->getPrimaryImageUrl() ?? theme_asset('images/properties/property-1.jpg') }}" width="200" height="150" alt="{{ $item->title }}">
            </div>
            @endforeach
        </div>

        <a href="{{ route('compare_properties') }}" class="btn-default w-100">Compare</a>
        <button type="button" class="btn-default btn-border w-100 mt-2" data-bs-dismiss="offcanvas" aria-label="Close">Close</button>
    </div>
</div>
