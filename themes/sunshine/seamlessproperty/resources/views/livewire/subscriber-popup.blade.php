<!-- Newsletter Modal -->
<div class="modal newsletter-modal fade" id="subscriberModal"   tabindex="-1" aria-labelledby="newsletterModalLabel" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="closePopup"></button>
                @if( $success )
                    <div class="alert alert-success">
                        <p>
                            {{ $successMessage }}
                        </p>
                    </div>
                @else
                @if( $imageUrl )
                <div class="ratio ratio-4x3">
                    <img class="rounded-1" src="{{ $imageUrl }}" alt="Banner">
                </div>
                @else
                <div class="ratio ratio-4x3">
                    <img class="rounded-1" src="/shared/img1.png" alt="Banner">
                </div>
                @endif
                <h2 class="text-center h5 my-3 fw-bold">{{ $description }}</h2>
                <form wire:submit.prevent="submit" method="post">
                    <input type="email" name="email" id="email" class="form-control" aria-label="Email" placeholder="Type your email"  wire:model="email">
                    @error('email')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                    <button type="submit" class="btn btn-primary w-100 mt-2" wire:click="submit">Submit</button>
                </form>
                @endif
            </div>
        </div>
    </div>
</div>
