<div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 overflow-hidden position-relative">
    <h4 class="title fz17 mb30">Change password</h4>
    <form class="form-style1" wire:submit.prevent="submit">
        <div class="row">
            <div class="col-sm-6 col-xl-4">
                <div class="mb20">
                    <label class="heading-color ff-heading fw600 mb10">Current Password</label>
                    <input type="password" class="form-control" placeholder="Your Current Password" wire:model="password">
                    @error('password')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6 col-xl-4">
                <div class="mb20">
                    <label class="heading-color ff-heading fw600 mb10">New Password</label>
                    <input type="password" class="form-control" placeholder="New Password" wire:model="new_password">
                    @error('new_password')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="col-sm-6 col-xl-4">
                <div class="mb20">
                    <label class="heading-color ff-heading fw600 mb10">Confirm New Password</label>
                    <input type="password" class="form-control" placeholder="Confirm New Password" wire:model="new_password_confirmation">
                    @error('new_password_confirmation')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="text-end d-flex justify-content-end align-items-baseline gap-2">
                    @if ($successMesasge)
                        <p class="text-success">
                            {{ $successMesasge }}
                        </p>
                    @endif
                    <a class="ud-btn btn-dark" href="#" wire:click.prevent="submit">Change Password<i class="fal fa-arrow-right-long"></i> <i class="fa fa-spin fa-spinner" wire:loading wire:target="submit"></i></a>
                </div>
            </div>
        </div>
    </form>
</div>
