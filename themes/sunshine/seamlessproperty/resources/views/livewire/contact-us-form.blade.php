<form id="contact-form" wire:submit.prevent="submit()" novalidate="novalidate">
    @if ($successMesasge)

        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <p>
                        {{ $successMesasge }}
                    </p>
                </div>
            </div>
        </div>
    @endif
    <div class="row">
        <div class="col-md-6 mb-2">
            <label class="mb-2">First Name:</label>
            <input type="text" class="form-control bg-white" name="form_first_name" required="" wire:model="first_name">
            @error ( 'first_name' )
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-6 mb-2">
            <label class="mb-2">Last Name:</label>
            <input type="text" class="form-control bg-white" name="form_last_name" required="" wire:model="last_name">
            @error ( 'last_name' )
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-6 mb-2">
            <label class="mb-2">Your Email:</label>
            <input type="email" class="form-control bg-white" name="email" required="" wire:model="email">
            @error ( 'email' )
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-6 mb-2">
            <label class="mb-2">Your Phone:</label>
            <input type="text" class="form-control bg-white" name="phone" required="" wire:model="phone">
            @error ( 'phone' )
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        {{-- <div class="col-md-12 mb-20">
            <label class="mb-2">Subject:</label>
            <input type="text" class="form-control bg-white" name="subject" required="">
        </div> --}}
        <div class="col-md-12 mb-4">
            <label class="mb-2">Message:</label>
            <textarea class="form-control bg-white" name="message" rows="8" required="" wire:model="message"></textarea>
            @error ( 'message' )
                <span class="text-danger">{{ $message }}</span>
            @enderror
        </div>
        <div class="col-md-12">
            <button class="btn btn-default" name="submit" type="submit">Send Message <i class="fa fa-spin fa-spinner" wire:loading wire:target="submit"></i></button>
        </div>
    </div>
</form>
