<div class="row gy-2 gy-lg-0">
    @if ($successMesasge)
        <div class="col-12">
            <div class="alert alert-success">
                <p>
                    {{ $successMesasge }}
                </p>
            </div>
        </div>
    @endif

    <div class="col-xl-7">
        <div class="profile-box position-relative d-md-flex align-items-end mb50">
            <div class="profile-img position-relative overflow-hidden bdrs12 mb20-sm">
                @if( $photo || auth()->user()->getFirstMediaUrl('logo') )
                <img class="w-100" src="{{ $photo?->temporaryUrl() ?? auth()->user()->getFirstMediaUrl('logo', 'logo') }}" alt="">
                @else
                <img class="w-100" src="{{ theme_asset('dashboard/images/resource/user.png') }}" alt="">
                @endif
                <a href="#" class="tag-del d-none" data-bs-toggle="tooltip"
                    data-bs-placement="top" title=""
                    data-bs-original-title="Delete Image" aria-label="Delete Item"><span
                        class="fas fa-trash-can"></span></a>
            </div>
            <div class="profile-content ml30 ml0-sm">
                <input type="file" name="profile-photo" id="profile-photo" class="form-control d-none" accept=".gif, .jpg, .png" wire:model="photo">
                <label for="profile-photo" class="ud-btn btn-white2 mb30">Upload Profile Files <i class="fal fa-arrow-right-long"></i> <i class="fa fa-spin fa-spinner" wire:loading></i></label>
                <p class="text">Photos must be JPEG or PNG format and least 2048x768</p>
                @error('photo')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>
        </div>
    </div>
</div>
