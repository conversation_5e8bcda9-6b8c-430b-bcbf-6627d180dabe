<div>

    <form action="{{ route($searchFormRoute ?? 'property.search_results') }}" id="advance-search-form">
        <div class="row gy-3 gy-lg-4">
            <div class="d-flex search-row">
                <div class="mb-3">
                    <select name="full_location" id="full_location" class="form-control form-select full-location" aria-label="Select Location" wire:model="formData.full_location">
                        <option value="">Location</option>
                        @foreach ($locations as $item)
                        <option value="{{ $item->title }}">{{ $item->title }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <select name="property_types" id="property-type" class="form-control form-select" aria-label="Select Property Type" wire:model="formData.property_types">
                        <option value="">All</option>
                        @foreach ($propertyTypes as $item)
                        <option value="{{ $item->slug }}">{{ $item->title }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <select name="state" id="state" class="form-control form-select" aria-label="Select State" wire:model="formData.state">
                        <option value="">Select State</option>
                        @foreach ($states as $item)
                        <option value="{{ $item->id }}">{{ $item->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="d-flex search-row">
                <div class="mb-3" wire:ignore>
                    <label class="mb-3" for="price-range1">Price Range <span id="grossReturnRange1"></span></label>
                    <div  id="gross-return-range1" class="price-filter-range" name="rangeInput"
                            data-step="1000"
                            data-min="350000"
                            data-max="2500000"
                            data-values="{{ data_get($formData ?? [],'price_range.0', 350000) }}, {{ data_get($formData ?? [],'price_range.1', 2500000) }}"
                            data-form-input="price-range1">
                            <input type="hidden" id="price-range1" name="price_range" wire:model="formData.price_range"/>
                    </div>
                </div>
                <div class="mb-3" wire:ignore>
                    <label class="mb-3 text-nowrap" for="gross-range2">Gross Return Range <span id="grossReturnRange2"></span></label>
                    <div id="gross-return-range2" class="price-filter-range"
                        data-step="10000"
                        data-min="10000"
                        data-max="150000"
                        data-form-input="gross-range2"
                        data-values="{{ data_get($formData ?? [],'gross_return.0', 10000) }}, {{ data_get($formData ?? [],'gross_return.1', 150000) }}">
                        <input type="hidden" id="gross-range2" name="gross_return" wire:model="formData.gross_return" />
                    </div>
                </div>
                <div class="mb-3" wire:ignore>
                    <label class="mb-3" for="gross-yield3">Gross Yield Range <span id="grossYieldRange"></span></label>
                    <div id="gross-yield-range" class="price-filter-range"
                            data-step="1"
                            data-min="3"
                            data-max="20"
                            data-form-input="gross-yield3"
                            data-values="{{ data_get($formData ?? [],'gross_yield.0', 3) }}, {{ data_get($formData ?? [],'gross_yield.1',20) }}"
                            data-postfix-symbol="%">
                            <input type="hidden" id="gross-yield3" name="gross_yield" wire:model="formData.gross_yield" />
                    </div>
                </div>
            </div>
            <div class="d-flex search-row plr" style="margin-top: 20px;">
                <input type="text" name="title" id="keyword" class="form-control" aria-label="Type Keyword" placeholder="Type Keyword" wire:model="formData.title">
            </div>
            <div class="text-left" style="text-align: center;">
                <input type="hidden" id="sort-column" name="sort[column]" />
                <input type="hidden" id="sort-order" name="sort[order]" />
                <input type="hidden" id="sort-by" name="sort_by" />
                <button type="submit" class="btn-default btn-search">Search Listings</button>
                <button type="button" class="btn-default btn btn-primary mb-1 mb-sm-0 me-sm-2" data-bs-toggle="modal" data-bs-target="#advanceSearchModal">Advance Filter @if( !empty( array_filter( \Arr::except($formData, ['sort', 'property_type_title', 'sort_by']) ))) ( {{ count( array_filter( \Arr::except($formData, ['sort', 'property_type_title', 'view-type']) ) ) }} ) @endif</button>
                <button type="reset" class="btn-default btn-border btn-reset-search" id="reset-search">Reset Search</button>

            </div>
        </div>

        <div class=" modal fade " id="advanceSearchModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-hidden="true" wire:ignore.self>
            <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header lt-bg-primary">
                        <h5 class="modal-title text-reset">
                            Advance Search Filters
                        </h5>
                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    </div>
                    <div class="modal-body">
                        {{-- <form action="{{ route($searchFormRoute ?? 'property.search_results') }}" x-ref="advanceSeachForm" id="modalSearchForm" > --}}
                            {{-- <input type="hidden" class="sort-column" name="sort[column]" value="{{ data_get($formData, 'sort.column') }}" />
                            <input type="hidden" class="sort-order" name="sort[order]" value="{{ data_get($formData, 'sort.order') }}" />
                            <input type="hidden" class="sort-by" name="sort_by" value="{{ data_get($formData, 'sort_by') }}" /> --}}
                            <div class="row">
                                <div class="col-12 border-bottom pb-3">
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Property Types
                                            </strong>
                                        </div>
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-12 col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="property_type_title" value="" id="property_type_all_cb"  @if( empty( data_get( $formData, 'property_type_titles') )  ) checked @endif x-on:click="(e) => { if( !$el.checked ){ e.preventDefault(); return; } }" x-on:change="(e) => { if( !$el.checked ){ e.preventDefault(); return; } if( $el.checked ) { document.querySelectorAll('.property-type-cb:checked').forEach( el => el.checked = false ); } }">
                                                        <label class="form-check-label" for="property_type_all_cb">
                                                            All Property Types
                                                        </label>
                                                    </div>
                                                </div>
                                                @foreach ($propertyTypes as $item)
                                                    @if( strtolower($item->title) == 'ndis' ) @continue @endif
                                                    <div class="col-12 col-md-6">
                                                        <div class="form-check">
                                                            <input class="form-check-input property-type-cb property_type_cb_{{ $item->slug }}" type="checkbox" name="property_type_titles[]" value="{{ $item->slug }}" id="property_type_cb_{{ $loop->index }}" @if( in_array( $item->slug,( data_get( $formData, 'property_type_titles', []) ?? [] ) ) || ( data_get( $formData, 'property_type_title') == $item->slug ) ) checked @endif x-on:change="() => { if( $el.checked || document.querySelectorAll('.property-type-cb:checked')?.length ){ document.getElementById('property_type_all_cb').checked = false; }else{ document.getElementById('property_type_all_cb').checked = true; }}">
                                                            <label class="form-check-label" for="property_type_cb_{{ $loop->index }}">
                                                                {{ ( \Str::contains( $item->title, 'SMSF') ? 'SMSF' : $item->title) }}
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                State
                                            </strong>
                                        </div>
                                        <div class="col-12">
                                            <select name="state" id="as_state" class="form-control form-select" aria-label="Select State" wire:model="formData.state">
                                                <option value="">Select State</option>
                                                @foreach ($states as $item)
                                                <option value="{{ $item->id }}" data-latitude="{{ $item->latitude }}" data-longitude="{{ $item->longitude }}" data-map-bounds='{{ $item->viewport_bounds }}'>{{ $item->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Price
                                            </strong>
                                        </div>
                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="price-range1"><span id="asPriceRange1"></span></label>
                                            <div  id="as-price-range-el" class="price-filter-range" name="asPriceRange"
                                                data-step="50000"
                                                data-min="350000"
                                                data-max="2000000"
                                                data-values="{{ data_get($formData ?? [],'price_range.0', 350000) }}, {{ data_get($formData ?? [],'price_range.1', 2000000) }}"
                                                data-form-input="as-price-range1">
                                                <input type="hidden" id="as-price-range1" name="price_range" wire:model="formData.price_range"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Land Price
                                            </strong>
                                        </div>
                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="as_land_price-range1"><span id="asLandPriceRang1"></span></label>
                                            <div  id="as-land-price-range1" class="price-filter-range" name="aslandpricerangeInput"
                                                data-step="5000"
                                                data-min="{{ config('themes-manager.search_data.min_land_price.latitudeproperty', 100000) }}"
                                                data-max="{{ config('themes-manager.search_data.max_land_price.latitudeproperty', 1100000) }}"
                                                data-values="{{ data_get($formData ?? [],'land_price_range.0', 100000) }}, {{ data_get($formData ?? [],'land_price_range.1', 1100000) }}"
                                                data-form-input="as_land_price-range1">
                                                <input type="hidden" id="as_land_price-range1" name="land_price_range" wire:model="formData.land_price_range"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Build Price
                                            </strong>
                                        </div>

                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="as_build_price-range1"><span id="asBuildPriceRange1"></span></label>
                                            <div  id="as-build-price-range1" class="price-filter-range" name="asBuildpricerangeInput"
                                                data-step="5000"
                                                data-min="{{ config('themes-manager.search_data.min_build_price.latitudeproperty', 100000) }}"
                                                data-max="{{ config('themes-manager.search_data.max_build_price.latitudeproperty', 1100000) }}"
                                                data-values="{{ data_get($formData ?? [],'build_price_range.0', 100000) }}, {{ data_get($formData ?? [],'build_price_range.1', 1100000) }}"
                                                data-form-input="as_build_price-range1">
                                                <input type="hidden" id="as_build_price-range1" name="build_price_range" wire:model="formData.build_price_range"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Gross Return
                                            </strong>
                                        </div>
                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="gross-range2"><span id="asGrossReturnRange2"></span></label>
                                            <div id="as-gross-return-range2" class="price-filter-range"
                                                data-step="10000"
                                                data-min="10000"
                                                data-max="150000"
                                                data-form-input="as-gross-range2"
                                                data-values="{{ data_get($formData ?? [],'gross_return.0', 10000) }}, {{ data_get($formData ?? [],'gross_return.1', 150000) }}">
                                                <input type="hidden" id="as-gross-range2" name="gross_return" wire:model="formData.gross_return" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Gross Yield
                                            </strong>
                                        </div>

                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="as-gross-yield3"><span id="asGrossYieldRange"></span></label>
                                            <div id="as-gross-yield-range" class="price-filter-range"
                                                data-step="1"
                                                data-min="3"
                                                data-max="20"
                                                data-form-input="as-gross-yield3"
                                                data-postfix-sign="%"
                                                data-values="{{ data_get($formData ?? [],'gross_yield.0', 3) }}, {{ data_get($formData ?? [],'gross_yield.1',20) }}">
                                                <input type="hidden" id="as-gross-yield3" name="gross_yield" wire:model="formData.gross_yield" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Gross Per Week
                                            </strong>
                                        </div>
                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="as_weekly_rent-range1"><span id="asWeeklyRentRange1"></span></label>
                                            <div  id="as-weekly-rent-range1" class="price-filter-range" name="asWeeklyRentrangeInput"
                                                data-step="100"
                                                data-min="{{ config('themes-manager.search_data.min_weekly_rent.latitudeproperty', 0) }}"
                                                data-max="{{ config('themes-manager.search_data.max_weekly_rent.latitudeproperty', 10000) }}"
                                                data-values="{{ data_get($formData ?? [],'weekly_rent_range.0', 0) }}, {{ data_get($formData ?? [],'weekly_rent_range.1', 10000) }}"
                                                data-form-input="as_weekly_rent-range1">
                                                <input type="hidden" id="as_weekly_rent-range1" name="weekly_rent_range" wire:model="formData.weekly_rent_range"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Capital Growth 12 Months
                                            </strong>
                                        </div>
                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="as_capital_growth_year-range1"><span id="asCapitalGrowthYearRange1"></span></label>
                                            <div  id="as-capital-growth-year-range1" class="price-filter-range" name="asCapitalGrowthYearrangeInput"
                                                data-step="1"
                                                data-postfix-sign="%"
                                                data-min="{{ config('themes-manager.search_data.min_capital_growth_year.latitudeproperty', 0) }}"
                                                data-max="{{ config('themes-manager.search_data.max_capital_growth_year.latitudeproperty', 100) }}"
                                                data-values="{{ data_get($formData ?? [],'capital_growth_year_range.0', 0) }}, {{ data_get($formData ?? [],'capital_growth_year_range.1', 100) }}"
                                                data-form-input="as_capital_growth_year-range1">
                                                <input type="hidden" id="as_capital_growth_year-range1" name="capital_growth_year_range" wire:model="formData.capital_growth_year_range"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Capital Growth 10 Years
                                            </strong>
                                        </div>

                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="as_capital_growth_ten_year-range1"><span id="asCapitalGrowthTenYearRange1"></span></label>
                                            <div  id="as-capital-growth-ten-year-range1" class="price-filter-range" name="asCapitalGrowthTenYearrangeInput"
                                                data-step="1"
                                                data-postfix-sign="%"
                                                data-min="{{ config('themes-manager.search_data.min_capital_growth_ten_year.latitudeproperty', 0) }}"
                                                data-max="{{ config('themes-manager.search_data.max_capital_growth_ten_year.latitudeproperty', 100) }}"
                                                data-values="{{ data_get($formData ?? [],'capital_growth_ten_year_range.0', 0) }}, {{ data_get($formData ?? [],'capital_growth_ten_year_range.1', 100) }}"
                                                data-form-input="as_capital_growth_ten_year-range1">
                                                <input type="hidden" id="as_capital_growth_ten_year-range1" name="capital_growth_ten_year_range" wire:model="formData.capital_growth_ten_year_range"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-2">
                                            <strong>
                                                Vacancy Rate
                                            </strong>
                                        </div>
                                        <div class="col-12" wire:ignore>
                                            <label class="mb-3" for="as_vacancy_rate-range1"><span id="asCapitalGrowthTenYearRange1"></span></label>
                                            <div  id="as-vacancy-rate-range1" class="price-filter-range" name="asCapitalGrowthTenYearrangeInput"
                                                data-step="1"
                                                data-postfix-sign="%"
                                                data-min="{{ config('themes-manager.search_data.min_vacancy_rate.latitudeproperty', 0) }}"
                                                data-max="{{ config('themes-manager.search_data.max_vacancy_rate.latitudeproperty', 100) }}"
                                                data-values="{{ data_get($formData ?? [],'vacancy_rate_range.0', 0) }}, {{ data_get($formData ?? [],'vacancy_rate_range.1', 100) }}"
                                                data-form-input="as_vacancy_rate-range1">
                                                <input type="hidden" id="as_vacancy_rate-range1" name="vacancy_rate_range" wire:model="formData.vacancy_rate_range"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @if( false)
                                <div class="col-12 border-bottom pb-3 mt-2">
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Bedrooms
                                            </strong>
                                        </div>
                                        <div class="col-12"  wire:ignore>

                                                <div  id="bedrooms-div-range1" class="price-filter-range" name="bedroomsrangeInput"
                                                    data-step="1"
                                                    data-min="{{ config('themes-manager.search_data.min_bedrooms.latitudeproperty', 0) }}"
                                                    data-max="{{ config('themes-manager.search_data.max_bedrooms.latitudeproperty', 10000) }}"
                                                    data-values="{{ data_get($formData ?? [],'bedrooms_range.0', 0) }}, {{ data_get($formData ?? [],'bedrooms_range.1', 8) }}"
                                                    data-form-input="bedrooms-range1">
                                                    <input type="hidden" id="bedrooms-range1" name="bedrooms_range" wire:model="formData.bedrooms_range"/>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                <div class="col-12 border-bottom pb-3 mt-2" x-data="{min_bedroom: '{{ data_get( $formData, 'min_bedrooms') }}', max_bedroom:'{{ data_get( $formData, 'max_bedrooms') }}'}">
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Bedrooms
                                            </strong>
                                        </div>
                                        <div class="col-12"  >
                                            <div class="row">
                                                <div class="col-12 col-md-6">
                                                    <div class="form-group">
                                                        <label>
                                                            Min
                                                        </label>
                                                        <select name="min_bedrooms" id="min_bedrooms" class="form-control form-select" aria-label="Select Bedrooms" wire:model="formData.min_bedrooms" x-model="min_bedroom" >
                                                            <option value="">Any</option>
                                                            @foreach ($bedroomsLists as $item)
                                                                <option value="{{ $item }}" wire:key="as-property-min-bedrooms-{{ $item }}" x-show="(max_bedroom == '' || ( max_bedroom != '' && {{$item}} <= max_bedroom ))">{{ $item }}</option>

                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-6">
                                                    <div class="form-group">
                                                        <label>
                                                            Max
                                                        </label>
                                                        <select name="max_bedrooms" id="max_bedrooms" class="form-control form-select" aria-label="Select Bedrooms" wire:model="formData.max_bedrooms" x-model="max_bedroom">
                                                            <option value="">Any</option>
                                                            @foreach ($bedroomsLists as $item)
                                                                <option value="{{ $item }}" wire:key="as-property-max-bedrooms-{{ $item }}" x-show="(min_bedroom == '' || ( min_bedroom != '' && {{$item}} >= min_bedroom ))">{{ $item }}</option>

                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Bathrooms
                                            </strong>
                                        </div>
                                        <div class="col-12"  >
                                            <div class="form-group">
                                                <select name="min_bathrooms" id="min_bathrooms" class="form-control form-select" aria-label="Select Bathrooms" wire:model="formData.min_bathrooms">
                                                    <option value="">Any</option>
                                                    @foreach ($bathroomsLists as $item)
                                                        <option value="{{ $item }}" wire:key="as-property-min-bathrooms-{{ $item }}" >{{ $item }}+</option>

                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Car Spaces
                                            </strong>
                                        </div>
                                        <div class="col-12"  >
                                            <div class="form-group">
                                                <select name="min_garages" id="min_garages" class="form-control form-select" aria-label="Select Garages" wire:model="formData.min_garages">
                                                    <option value="">Any</option>
                                                    @foreach ($garagesLists as $item)
                                                        <option value="{{ $item }}" wire:key="as-property-min-garages-{{ $item }}" >{{ $item }}+</option>

                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Property (House) Size
                                            </strong>
                                        </div>

                                        <div class="row" x-data="{min_property_size: '{{ data_get( $formData, 'min_area') }}', max_property_size: '{{ data_get( $formData, 'max_area') }}'}" >
                                            <div class="col-12 col-md-6"  >
                                                <div class="form-group">
                                                    <label>Min</label>
                                                    <select name="min_area" id="min_property_size" class="form-control form-select" aria-label="Select Property (House) Size" wire:model="formData.min_area" x-model="min_property_size">
                                                        <option value="">Any</option>
                                                        @foreach ($landAreaSizeList as $key => $item)
                                                            <option value="{{ $key }}" wire:key="as-property-min-property-area-size-{{ $key }}" x-show="( max_property_size == '' || ( max_property_size != '' && {{$key}} <= max_property_size))">{{ $item }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md-6"  >
                                                <div class="form-group">
                                                    <label>Max</label>
                                                    <select name="max_area" id="max_property_size" class="form-control form-select" aria-label="Select Property (House) Size" wire:model="formData.max_area" x-model="max_property_size">
                                                        <option value="">Any</option>
                                                        @foreach ($landAreaSizeList as $key => $item)
                                                            <option value="{{ $key }}" wire:key="as-property-min-property-area-size-{{ $key }}" x-show="( min_property_size == '' || ( min_property_size != '' && {{$key}} >= min_property_size))">{{ $item }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Land Area
                                            </strong>
                                        </div>

                                        <div class="row" x-data="{min_land_area: '{{ data_get( $formData, 'min_land_area_size') }}', max_land_area: '{{ data_get( $formData, 'max_land_area_size') }}'}" >
                                            <div class="col-12 col-md-6"  >
                                                <div class="form-group">
                                                    <label>Min</label>
                                                    <select name="min_land_area_size" id="min_land_area_size" class="form-control form-select" aria-label="Select Property (House) Size" wire:model="formData.min_land_area_size" x-model="min_land_area">
                                                        <option value="">Any</option>
                                                        @foreach ($landAreaSizeList as $key => $item)
                                                            <option value="{{ $key }}" wire:key="as-property-min-land-area-size-{{ $key }}" x-show="( max_land_area == '' || ( max_land_area != '' && {{$key}} <= max_land_area))">{{ $item }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md-6"  >
                                                <div class="form-group">
                                                    <label>Max</label>
                                                    <select name="max_land_area_size" id="max_land_area_size" class="form-control form-select" aria-label="Select Property (House) Size" wire:model="formData.max_land_area_size" x-model="max_land_area">
                                                        <option value="">Any</option>
                                                        @foreach ($landAreaSizeList as $key => $item)
                                                            <option value="{{ $key }}" wire:key="as-property-min-land-area-size-{{ $key }}" x-show="( min_land_area == '' || ( min_land_area != '' && {{$key}} >= min_land_area))">{{ $item }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Title Status
                                            </strong>
                                        </div>

                                        <div class="col-12"  >
                                            <input type="text" name="title_status" id="as_title_status" class="form-control" aria-label="Title Status" placeholder="Title Status" wire:model="formData.title_status">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Property ID
                                            </strong>
                                        </div>

                                        <div class="col-12"  >
                                            <input type="text" name="property_id" id="as_property_id" class="form-control" aria-label="Property ID" placeholder="Property ID" wire:model="formData.property_id">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Property SKU
                                            </strong>
                                        </div>

                                        <div class="col-12"  >
                                            <input type="text" name="property_sku" id="as_property_sku" class="form-control" aria-label="Property SKU" placeholder="Property SKU" wire:model="formData.property_sku">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <strong>
                                                Keywords
                                            </strong>
                                        </div>

                                        <div class="col-12"  >
                                            <input type="text" name="common_keyword" id="as_keyword" class="form-control" aria-label="Type Keyword" placeholder="Type Keyword" wire:model="formData.common_keyword">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pb-3 mt-2" >
                                    <div class="row">
                                        <div class="col-12 pb-3">
                                            <label for="as_exclude_sold_properties">

                                                <input type="checkbox" name="exclude_sold_properties" id="as_exclude_sold_properties" wire:model="formData.exclude_sold_properties" {{ data_get( $formData, 'exclude_sold_properties') == 'on' ? 'checked':'' }}>
                                                Exclude sold properies?
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row position-absolute top-0 " style="visibility: hidden;">
                                <div class="col-12">
                                    <button class="btn btn-link btn-slim ml-3 reset-advance-search-form" type="button" x-ref="resetFormButton">
                                        Clear Filter
                                    </button>
                                </div>
                            </div>
                        {{-- </form> --}}
                    </div>
                    <div class="modal-footer justify-content-between">
                        <button class="btn btn-link btn-slim ml-3 reset-search" type="button">
                            Clear Filter
                        </button>
                        <button class="btn btn-primary ml-3x" type="submit" >
                            Search
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div class=" modal fade " id="createSearchModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        Add Search
                    </h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">×</span></button>
                </div>
                <div class="modal-body container pb30 pl0 pr0 pt0">
                    @if( $successMessage )
                    <div class="alert alert-success">
                        <p>
                            {{ $successMessage }}
                        </p>
                    </div>
                    @endif
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group p-3">
                                <label for="">Title</label>
                                <input type="text" class="form-control" placeholder="Enter search title" wire:model="search_title">
                                @error('search_title')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                           <button class="btn btn-primary ml-3" wire:click.prevent="saveSearch" type="button" wire:loading.attr="disabled">
                            Add Search @if( $processing ) <i class="fa fa-spin fa-spinner"></i> @endif
                           </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
            document.addEventListener('DOMContentLoaded', () => {
                $(document).ready( function(){
                    $("#createSearchModal").modal({
                        show: false
                    });
                })

            })
    </script>
</div>
