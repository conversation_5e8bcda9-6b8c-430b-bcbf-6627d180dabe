<!DOCTYPE html>
<html lang="en">
<head>
    @include('layouts.partials.css-head')
    @if(config('current_subdomain'))
        <!-- Subdomain Context Meta -->
        <meta name="subdomain" content="{{ config('current_subdomain')->subdomain }}">
        <meta name="subdomain-domain" content="{{ config('current_subdomain')->full_domain ?? config('current_subdomain')->full_domain_accessor }}">
    @endif
</head>
<body class="tt-magic-cursor @if(config('current_subdomain')) subdomain-context subdomain-{{ config('current_subdomain')->subdomain }} @endif">

    {{-- Google Tag Manager (noscript) --}}
    @php
        $subdomainData = config('subdomain_data');
        $analytics = $subdomainData['analytics'] ?? [];
    @endphp
    @if(isset($analytics['google_tag_manager']) && !empty($analytics['google_tag_manager']))
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ $analytics['google_tag_manager'] }}"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    @endif

    <!-- Preloader Start -->
	<div class="preloader">
		<div class="loading-container">
			<div class="loading"></div>
			<div id="loading-icon"><img src="{{ theme_asset('images/icon-heading-light.svg') }}" alt=""></div>
		</div>
	</div>
	<!-- Preloader End -->

	<!-- Magic Cursor Start -->
	<div id="magic-cursor">
		<div id="ball"></div>
	</div>
	<!-- Magic Cursor End -->

    @include('layouts.partials.navigation-header')

    <main>
        {{ $slot }}
    </main>

    @include('layouts.partials.footer')

    <livewire:subscriber-popup :key='"subscriber_popup"' />
    <livewire:compare-property-sidebar :key="'compare_sidebar'" :currentRequest="url()->current()" />

    @include('layouts.partials.scripts')

</body>
</html>
