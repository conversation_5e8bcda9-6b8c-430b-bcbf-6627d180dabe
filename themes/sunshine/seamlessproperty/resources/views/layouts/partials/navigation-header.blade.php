

@include('components.theme-styled-subdomain-nav')

<!-- Header Start -->
<header class="main-header">
    <div class="header-sticky">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <!-- Logo Start -->
                <div class="navbar-brand-container">
                    <a class="navbar-brand" href="{{ route('index') }}">
                        <img src="{{ theme_asset('images/logo.svg') }}" alt="Logo" width="180px">
                    </a>
                    @if(config('current_subdomain'))
                        <div class="subdomain-indicator" style="font-size: 11px; color: #666; margin-top: 2px; text-align: left;">
                            {{ config('current_subdomain')->subdomain }}
                        </div>
                    @endif
                </div>
                <!-- Logo End -->

                <!-- Main Menu start -->
                <div class="collapse navbar-collapse main-menu">
                    <ul class="navbar-nav mr-auto" id="menu">
                        <li class="nav-item"><a class="nav-link {{ url()->current() == route('index') ? 'active': '' }}" href="{{ route('index') }}">Home</a>
                        </li>
                        <li class="nav-item"><a class="nav-link {{ url()->current() == route('property.listing') ? 'active': '' }}" href="{{ route('property.listing') }}">Property Listings</a></li>
                        @if( config('company_data')?->isFeatureEnabled('CMS Page') )
                            @foreach ($header_links as $item)

                                <li class="nav-item">
                                    <a class="nav-link  {{ url()->current() == route('cms_page', $item) ? 'active': '' }}" href="{{ route('cms_page', $item) }}">
                                        <span class="title">{{ $item->name }}</span>
                                    </a>
                                </li>
                            @endforeach
                        @endif
                        <li class="nav-item"><a class="nav-link {{ url()->current() == route('contact_us') ? 'active': '' }}" href="{{ route('contact_us') }}">Contact us</a></li>
                        @auth
                            <li class="nav-item highlighted-menu">
                                <a href="{{ route('dashboard') }}" role="button" class="nav-link">
                                    {{ auth()->user()->name }}
                                </a>
                            </li>
                        @else
                            <li class="nav-item highlighted-menu">
                                <a href="{{ route('signin')}}" role="button" class="nav-link" aria-label="Open Login Modal">Login</a>
                            </li>
                        @endauth
                    </ul>
                </div>
                <!-- Main Menu End -->

                <div class="navbar-toggle"></div>
            </div>
        </nav>

        <div class="responsive-menu"></div>
    </div>
</header>
<!-- Header End -->
