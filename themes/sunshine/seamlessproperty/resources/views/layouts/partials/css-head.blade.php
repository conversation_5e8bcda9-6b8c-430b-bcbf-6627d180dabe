
<!-- Meta -->
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">

@yield('meta-part')

<!-- Dynamic SEO Meta Tags -->
@include('components.seo-meta')

<!-- Page Title -->
<title>@yield('title') - Seamless Property Solutions</title>
<!-- Favicon Icon -->
<link rel="shortcut icon" type="image/x-icon" href="{{ theme_asset('images/favicon.png') }}">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
<link href="{{ theme_asset('css/bootstrap.min.css') }}" rel="stylesheet" media="screen">
<link href="{{ theme_asset('css/slicknav.min.css') }}" rel="stylesheet">
<link href="{{ theme_asset('css/jquery-ui.css') }}" rel="stylesheet">
<link rel="stylesheet" href="{{ theme_asset('css/swiper-bundle.min.css') }}">
<link href="{{ theme_asset('css/all.min.css') }}" rel="stylesheet" media="screen">
<link href="{{ theme_asset('css/animate.css') }}" rel="stylesheet">
<link href="{{ theme_asset('css/magnific-popup.css') }}" rel="stylesheet">
<link rel="stylesheet" href="{{ url('css/googlemap.css')}}">
<link href="{{ theme_asset('css/custom.css') }}" rel="stylesheet" media="screen">
<link rel="stylesheet" href="https://styles.realtorsdash.com/css/seamless.css">


@stack('plugin-css')
@include('components.dynamic-external-css')

<!-- Subdomain-specific styling -->
@if(config('current_subdomain'))
    <link rel="stylesheet" href="{{ url('css/subdomain-styles.css') }}">
@endif

<!-- Dynamic Subdomain Branding -->
{{-- Temporarily disabled for debugging
@if(isset($subdomain_data))
    <x-subdomain-branding :subdomainData="$subdomain_data" />
@elseif(config('subdomain_data'))
    <x-subdomain-branding :subdomainData="config('subdomain_data')" />
@endif
--}}

{{-- Debug and branding implementation --}}
@php
    // Helper function to convert hex to RGB (declare first)
    if (!function_exists('hexToRgb')) {
        function hexToRgb($hex) {
            $hex = ltrim($hex, '#');
            if (strlen($hex) == 3) {
                $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
            }
            return implode(', ', array_map('hexdec', str_split($hex, 2)));
        }
    }

    $debugInfo = [];
    $debugInfo['config_subdomain_data'] = config('subdomain_data') ? 'EXISTS' : 'NULL';
    $debugInfo['isset_subdomain_data'] = isset($subdomain_data) ? 'EXISTS' : 'NULL';
    $debugInfo['current_subdomain'] = config('current_subdomain') ? config('current_subdomain')->subdomain : 'NULL';
    
    // Try multiple ways to get subdomain data
    $subdomainConfig = null;
    if (config('subdomain_data')) {
        $subdomainConfig = config('subdomain_data');
        $debugInfo['source'] = 'config';
    } elseif (isset($subdomain_data)) {
        $subdomainConfig = $subdomain_data;
        $debugInfo['source'] = 'view_variable';
    }
    
    if ($subdomainConfig) {
        $primaryColor = $subdomainConfig['styling']['custom_settings']['primary_color'] ?? '#007bff';
        $secondaryColor = $subdomainConfig['styling']['custom_settings']['secondary_color'] ?? '#6c757d';
        $subdomainName = $subdomainConfig['name'] ?? 'Unknown';
    } else {
        $primaryColor = '#ff0000'; // Red to indicate no config found
        $secondaryColor = '#ff6600'; // Orange to indicate no config found
        $subdomainName = 'NO CONFIG FOUND';
    }
@endphp

<!-- Debug Information -->
<!--
DEBUG INFO:
{{ json_encode($debugInfo, JSON_PRETTY_PRINT) }}
PRIMARY COLOR: {{ $primaryColor }}
SECONDARY COLOR: {{ $secondaryColor }}
-->

@if(config('current_subdomain'))
<style>
    /* Override CSS custom properties (theme variables) */
    :root {
        --primary-color: {{ $primaryColor }} !important;
        --secondary-color: {{ $secondaryColor }} !important;
        --bs-primary: {{ $primaryColor }} !important;
        --bs-secondary: {{ $secondaryColor }} !important;
        --subdomain-primary: {{ $primaryColor }};
        --subdomain-secondary: {{ $secondaryColor }};
    }
    
    /* Override theme-specific CSS variables */
    html {
        --primary-color: {{ $primaryColor }} !important;
        --secondary-color: {{ $secondaryColor }} !important;
    }
    
    /* Global color overrides using var() targeting */
    * {
        --primary-color: {{ $primaryColor }} !important;
        --secondary-color: {{ $secondaryColor }} !important;
    }
    
    /* Force override all primary button styles with highest specificity */
    html body .btn-primary,
    html body button.btn-primary,
    html body input[type="submit"].btn-primary,
    html body a.btn-primary,
    html body .btn.btn-primary {
        background-color: {{ $primaryColor }} !important;
        border-color: {{ $primaryColor }} !important;
        color: white !important;
    }
    
    html body .btn-primary:hover,
    html body .btn-primary:focus,
    html body .btn-primary:active,
    html body .btn-primary.active,
    html body .show > .btn-primary.dropdown-toggle {
        background-color: {{ $secondaryColor }} !important;
        border-color: {{ $secondaryColor }} !important;
        color: white !important;
    }
    
    /* Override any elements using CSS variables */
    html body h1,
    html body h2,
    html body h3,
    html body h4,
    html body h5,
    html body h6 {
        color: {{ $primaryColor }} !important;
    }
    
    /* Override Bootstrap primary classes with high specificity */
    html body .text-primary {
        color: {{ $primaryColor }} !important;
    }
    
    html body .bg-primary {
        background-color: {{ $primaryColor }} !important;
    }
    
    html body .border-primary {
        border-color: {{ $primaryColor }} !important;
    }
    
    /* Links with high specificity */
    html body a:not(.btn) {
        color: {{ $primaryColor }} !important;
    }
    
    html body a:not(.btn):hover {
        color: {{ $secondaryColor }} !important;
    }
    
    /* Navigation brand */
    html body .navbar-brand {
        color: {{ $primaryColor }} !important;
    }
    
    /* Target all button variations */
    html body button,
    html body .button,
    html body .btn,
    html body input[type="submit"],
    html body input[type="button"] {
        background-color: {{ $primaryColor }} !important;
        border-color: {{ $primaryColor }} !important;
        color: white !important;
    }
    
    html body button:hover,
    html body .button:hover,
    html body .btn:hover,
    html body input[type="submit"]:hover,
    html body input[type="button"]:hover {
        background-color: {{ $secondaryColor }} !important;
        border-color: {{ $secondaryColor }} !important;
        color: white !important;
    }
    
    /* Target specific button classes that might exist */
    html body .btn-outline-primary {
        color: {{ $primaryColor }} !important;
        border-color: {{ $primaryColor }} !important;
        background-color: transparent !important;
    }
    
    html body .btn-outline-primary:hover {
        background-color: {{ $primaryColor }} !important;
        border-color: {{ $primaryColor }} !important;
        color: white !important;
    }
    
    /* Form controls */
    html body .form-control:focus {
        border-color: {{ $primaryColor }} !important;
        box-shadow: 0 0 0 0.2rem rgba({{ hexToRgb($primaryColor) }}, 0.25) !important;
    }
    
    html body .form-check-input:checked {
        background-color: {{ $primaryColor }} !important;
        border-color: {{ $primaryColor }} !important;
    }
    
    /* Progress bars */
    html body .progress-bar {
        background-color: {{ $primaryColor }} !important;
    }
    
    /* Badges */
    html body .badge-primary,
    html body .badge.bg-primary {
        background-color: {{ $primaryColor }} !important;
    }
    
    /* Cards and components */
    html body .card-header.bg-primary {
        background-color: {{ $primaryColor }} !important;
    }
    
    /* Alert components */
    html body .alert-primary {
        color: {{ $primaryColor }} !important;
        background-color: rgba({{ hexToRgb($primaryColor) }}, 0.1) !important;
        border-color: rgba({{ hexToRgb($primaryColor) }}, 0.2) !important;
    }
    
    /* Custom theme-specific overrides with highest specificity */
    html body .tt-btn,
    html body .theme-btn,
    html body .custom-btn,
    html body [class*="btn"],
    html body [class*="button"] {
        background-color: {{ $primaryColor }} !important;
        border-color: {{ $primaryColor }} !important;
        color: white !important;
    }
    
    html body .tt-btn:hover,
    html body .theme-btn:hover,
    html body .custom-btn:hover,
    html body [class*="btn"]:hover,
    html body [class*="button"]:hover {
        background-color: {{ $secondaryColor }} !important;
        border-color: {{ $secondaryColor }} !important;
        color: white !important;
    }
    
    /* Target any elements that might use var(--primary-color) */
    html body *[style*="var(--primary-color)"] {
        color: {{ $primaryColor }} !important;
        background-color: {{ $primaryColor }} !important;
    }
    
    
    /* Force override any conflicting styles with maximum specificity */
    html body [class*="btn-primary"],
    html body [class*="btn-"],
    html body [class*="button-"],
    html body [class*="primary"] {
        background-color: {{ $primaryColor }} !important;
        border-color: {{ $primaryColor }} !important;
    }
    
    html body [class*="btn-primary"]:hover,
    html body [class*="btn-"]:hover,
    html body [class*="button-"]:hover,
    html body [class*="primary"]:hover {
        background-color: {{ $secondaryColor }} !important;
        border-color: {{ $secondaryColor }} !important;
    }
    
    /* Navbar brand container styling for subdomain indicator below logo */
    .navbar-brand-container {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    
    .navbar-brand-container .navbar-brand {
        margin-bottom: 0 !important;
        line-height: 1 !important;
    }
    
    .subdomain-indicator {
        font-weight: 500 !important;
        opacity: 0.8 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }
    
    /* Responsive adjustments for mobile */
    @media (max-width: 768px) {
        .navbar-brand-container {
            align-items: center !important;
        }
        
        .subdomain-indicator {
            text-align: center !important;
            font-size: 10px !important;
        }
    }
</style>
@endif

