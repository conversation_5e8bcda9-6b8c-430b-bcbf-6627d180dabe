<x-dashboard-layout>
    @section('title', 'Saved Search')
    @section('dashboard-title', 'Saved Search')
    @push('plugin-css')
    {!! theme_style('css/sweetalert2.css') !!}
    @endpush
    @push('plugin-js')
    {!! theme_script('js/sweetalert2.js') !!}
    @endpush



    <div class="row">
        <div class="col-xl-12">
            <div
                class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 overflow-hidden position-relative">
                <div class="packages_table table-responsive">
                    @if ($savedSearches->count())
                        <table class="table-style3 table at-savesearch">
                            <thead class="t-head">
                                <tr>
                                    <th scope="col">Listing title</th>
                                    <th scope="col">Date Created</th>
                                    <th scope="col">Action</th>
                                </tr>
                            </thead>
                            <tbody class="t-body">
                                @foreach ($savedSearches as $item)
                                    <tr>
                                        <th scope="row">{{ $item->title }}</th>
                                        <td>{{ $item->created_at?->format('M d, Y') ?? 'N/A' }}</td>
                                        <td>
                                            <div class="d-flex">
                                                <a href="{{ route('property.search_results', $item->search_data) }}" class="icon" data-bs-toggle="tooltip"
                                                    data-bs-placement="top" title="View Result"><span
                                                        class="flaticon-fullscreen-1"></span></a>
                                                <a href="#" class="icon remove-saved-search" data-bs-toggle="tooltip"
                                                    data-bs-placement="top" title="Delete" data-saved-search-id="{{ $item->id }}"><span
                                                        class="flaticon-bin"></span></a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>

                        <div class="mbp_pagination text-center mt30">
                            {{ $savedSearches->links('vendor.pagination.dashboard') }}
                        </div>
                    @else
                        <div class="alert alert-danger">
                            <p>
                                No Record Found.
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <livewire:save-search-delete-action/>
</x-dashboard-layout>
