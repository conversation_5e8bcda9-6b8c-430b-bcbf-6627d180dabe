@if( !empty( $cities ) )
    <!-- Property By City Section Start -->
	<div class="property-by-city">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<!-- Section Title Start -->
					<div class="section-title">
						<h3 class="wow fadeInUp">Location</h3>
						<h2 class="text-anime">Discover Properties</h2>
					</div>
					<!-- Section Title End -->
				</div>
			</div>

			<div class="row">
                @foreach ($cities as $item)
                    <div class="col-lg-3 col-md-6">
                        <!-- Location Item Start -->
                        <div class="location-item wow fadeInUp" data-wow-delay="0.25s">
                            <!-- Location Image Start -->
                            <div class="location-image">
                                <figure>
                                    <img src="{{ data_get($item, 'icon') ?? theme_asset('images/city-1.jpg')}}" alt="">
                                </figure>
                            </div>
                            <!-- Location Image End -->

                            <!-- Location Content Start -->
                            <div class="location-content">
                                <div class="location-header">
                                    <h3>{{ data_get($item, 'name') }}</h3>
                                    <p>{{ data_get($item, 'total_properties') ?? 0 }} Properties</p>
                                </div>

                                <div class="location-footer">
                                    <a href="{{ !empty( data_get($item, 'slug') ) ? route('property.by_state', ['state'=>data_get($item, 'slug')]) : '#' }}" class="btn-default">See More</a>
                                </div>
                            </div>
                            <!-- Location Content End -->
                        </div>
                        <!-- Location Item End -->
                    </div>
                @endforeach

			</div>
		</div>
	</div>
	<!-- Property By City Section End -->
@endif
