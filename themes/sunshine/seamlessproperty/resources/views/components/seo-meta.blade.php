{{-- Dynamic SEO Meta Tags Component --}}
@php
    $subdomainSeo = $subdomain_seo ?? [];
    $subdomainData = config('subdomain_data');
    $currentSubdomain = config('current_subdomain');
    
    // Helper function to safely convert values to strings
    if (!function_exists('safeString')) {
        function safeString($value, $default = '') {
            if (is_array($value)) {
                return implode(', ', $value);
            }
            return (string) ($value ?? $default);
        }
    }
    
    // Helper function to safely get array values
    if (!function_exists('safeArray')) {
        function safeArray($value) {
            if (is_array($value)) {
                return $value;
            }
            return empty($value) ? [] : [$value];
        }
    }
@endphp

{{-- Basic SEO Meta Tags --}}
@if(isset($subdomainSeo['title']) && !empty($subdomainSeo['title']))
    <title>{{ safeString($subdomainSeo['title']) }}</title>
@endif

@if(isset($subdomainSeo['description']) && !empty($subdomainSeo['description']))
    <meta name="description" content="{{ safeString($subdomainSeo['description']) }}">
@endif

@if(isset($subdomainSeo['keywords']) && !empty($subdomainSeo['keywords']))
    @php
        $keywords = safeArray($subdomainSeo['keywords']);
    @endphp
    @if(!empty($keywords))
        <meta name="keywords" content="{{ implode(', ', $keywords) }}">
    @endif
@endif

{{-- Canonical URL --}}
@if(isset($subdomainSeo['canonical_url']))
    <link rel="canonical" href="{{ $subdomainSeo['canonical_url'] }}">
@elseif($currentSubdomain)
    <link rel="canonical" href="{{ url()->current() }}">
@endif

{{-- Open Graph Meta Tags --}}
@if(isset($subdomainSeo['og_title']) && !empty($subdomainSeo['og_title']))
    <meta property="og:title" content="{{ safeString($subdomainSeo['og_title']) }}">
@endif

@if(isset($subdomainSeo['og_description']) && !empty($subdomainSeo['og_description']))
    <meta property="og:description" content="{{ safeString($subdomainSeo['og_description']) }}">
@endif

@if(isset($subdomainSeo['og_image']) && !empty($subdomainSeo['og_image']))
    <meta property="og:image" content="{{ safeString($subdomainSeo['og_image']) }}">
@endif

<meta property="og:url" content="{{ url()->current() }}">
<meta property="og:type" content="website">

{{-- Twitter Card Meta Tags --}}
<meta name="twitter:card" content="summary_large_image">

@if(isset($subdomainSeo['og_title']) && !empty($subdomainSeo['og_title']))
    <meta name="twitter:title" content="{{ safeString($subdomainSeo['og_title']) }}">
@endif

@if(isset($subdomainSeo['og_description']) && !empty($subdomainSeo['og_description']))
    <meta name="twitter:description" content="{{ safeString($subdomainSeo['og_description']) }}">
@endif

@if(isset($subdomainSeo['og_image']) && !empty($subdomainSeo['og_image']))
    <meta name="twitter:image" content="{{ safeString($subdomainSeo['og_image']) }}">
@endif

{{-- Schema.org Structured Data --}}
@if(isset($subdomainSeo['schema_markup']) && !empty($subdomainSeo['schema_markup']))
@php
    $defaultName = 'Property Investment Solutions';
    $defaultDescription = 'Find your perfect investment property';
    
    // Get basic schema data as strings only
    $schemaMarkupType = is_string($subdomainSeo['schema_markup']) ? $subdomainSeo['schema_markup'] : 'RealEstateAgent';
    
    $schemaName = $defaultName;
    if (isset($subdomainSeo['title']) && is_string($subdomainSeo['title'])) {
        $schemaName = $subdomainSeo['title'];
    } elseif (isset($subdomainData['name']) && is_string($subdomainData['name'])) {
        $schemaName = $subdomainData['name'];
    }
    
    $schemaDescription = $defaultDescription;
    if (isset($subdomainSeo['description']) && is_string($subdomainSeo['description'])) {
        $schemaDescription = $subdomainSeo['description'];
    }
    
    $schemaLogo = '';
    if (isset($subdomainData['branding']['logo']) && is_string($subdomainData['branding']['logo'])) {
        $schemaLogo = $subdomainData['branding']['logo'];
    }
    
    $schemaPhone = '';
    if (isset($subdomainData['contact']['phone']) && is_string($subdomainData['contact']['phone'])) {
        $schemaPhone = $subdomainData['contact']['phone'];
    }
    
    $schemaEmail = '';
    if (isset($subdomainData['contact']['email']) && is_string($subdomainData['contact']['email'])) {
        $schemaEmail = $subdomainData['contact']['email'];
    }
@endphp
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "{{ $schemaMarkupType }}",
    "name": "{{ $schemaName }}",
    "description": "{{ $schemaDescription }}",
    "url": "{{ url()->current() }}"
    @if($currentSubdomain && !empty($schemaLogo))
    ,"logo": "{{ $schemaLogo }}"
    @endif
    @if(!empty($schemaPhone))
    ,"telephone": "{{ $schemaPhone }}"
    @endif
    @if(!empty($schemaEmail))
    ,"email": "{{ $schemaEmail }}"
    @endif
}
</script>
@endif