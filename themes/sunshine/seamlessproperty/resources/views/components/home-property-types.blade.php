@if( $property_types->count() )

    <!-- Property Type Section Start -->
    <div class="property-types">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp">Property Types</h3>
                        <h2 class="text-anime">Explore Apartment Types</h2>
                    </div>
                    <!-- Section Title End -->
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <!-- Property Type Slider Start -->
                    <div class="property-type-slider">
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                @foreach ($property_types->sortBy('properties_count')->take(5) as $item)
                                    <!-- Property Type Slide Start -->
                                    <div class="swiper-slide">
                                        <div class="property-type-item">
                                            <div class="icon-box">
                                                <img src="{{ $item->media()->count() ? $item->getFirstMediaUrl('icon', 'thumbnail') : theme_asset('images/icon-property-type-1.svg') }}" alt="">
                                            </div>

                                            <h3>{{ $item->title }}</h3>
                                            <p>{{ $item->properties_count ?? 0 }} Properties</p>
                                        </div>
                                    </div>
                                    <!-- Property Type Slide End -->
                                @endforeach

                            </div>

                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                    <!-- Property Type Slider End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Property Type Section End -->
@endif
