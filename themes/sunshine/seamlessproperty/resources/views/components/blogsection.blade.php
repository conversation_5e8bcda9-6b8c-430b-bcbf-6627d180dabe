@if( $blogs->count() )

	<!-- Latest Posts Section Start -->
	<div class="latest-posts">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<!-- Section Title Start -->
					<div class="section-title">
						<h3 class="wow fadeInUp">News & Blog</h3>
						<h2 class="text-anime">Read Our Latest News</h2>
					</div>
					<!-- Section Title End -->
				</div>
			</div>

			<div class="row">
                @foreach ($blogs as $item)
				<div class="col-lg-4">
					<!-- Post Item Start -->
					<div class="post-item wow fadeInUp" data-wow-delay="0.25s">
						<!-- Post Featured Image Start -->
						<div class="post-featured-image">
							<figure>
								<a href="{{ route('blog_detail', $item) }}"><img src="{!! $item->getMedia('feature_image') ? $item->getFirstMediaUrl('feature_image') : theme_asset('images/post-1.jpg') !!}" alt="{{ $item->title }}"></a>
							</figure>

							<div class="post-read-more">
								<a href="{{ route('blog_detail', $item) }}" class="btn-default">Read More</a>
							</div>
						</div>
						<!-- Post Featured Image End -->

						<!-- Post Body Start -->
						<div class="post-body">
							<div class="post-category">
                                @if ( $item->categories->count() )
								<ul>
                                    @foreach ( $item->categories as $catItem )
									    <li><a href="{{ route('blogs.category', $catItem) }}">{{ $catItem->name }}</a></li>
                                    @endforeach
								</ul>
                                @endif
							</div>

							<h3><a href="#">{{ $item->title }}</a></h3>
						</div>
						<!-- Post Body End -->
					</div>
					<!-- Post Item End -->
				</div>
                @endforeach
			</div>
		</div>
	</div>
	<!-- Latest Posts Section End -->
@endif
