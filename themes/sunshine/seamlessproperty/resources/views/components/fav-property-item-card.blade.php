<div class="col-sm-6 col-xl-3">
    <div class="listing-style1 style2">
        <div class="list-thumb">
            <a href="#" class="tag-del remove-favourite" data-fav-property-id="{{ $favProperty->id }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Remove Favourite"><span
                    class="fas fa-trash-can"></span></a>
            <img class="w-100" src="{!! $item->getPrimaryImageUrl() ?? theme_asset('images/listings/g1-1.jpg') !!}" alt="">
            @if( $item?->propertySetting?->is_featured || ( isset($isSold) && $isSold ) )
            <div class="list-tag fz12">
                @if( $item?->propertySetting?->is_featured )
                <span class="flaticon-electricity me-2"></span>FEATURED
                @endif
                @if( isset($isSold) && $isSold )
                <span class="flaticon-electricity me-2 label-sold"></span>SOLD
                @endif
            </div>
            @endif
            <div class="list-price">{{ $item->priceWithPrefix() }} @if( !empty( $item->price_postfix ) ) / <span>{{ $item->price_postfix }}</span> @endif </div>
        </div>
        <div class="list-content">
            <h6 class="list-title"><a href="{{ route('property.details', $item) }}">{{ mb_strimwidth($item->title, 0, 50, "...") }}</a></h6>
            <p class="list-text">{{ $item->getFullAddress() }}</p>
            <div class="list-meta d-flex align-items-center">
                <a href="#"><span class="flaticon-bed"></span>{{ $item->bedrooms ?? 0 }} bed</a>
                <a href="#"><span class="flaticon-shower"></span>{{ $item->bathrooms ?? 0 }} bath</a>
                <a href="#"><span class="flaticon-expand"></span>{{ $item->areaSizeWithPostfix() }}</a>
            </div>
            @if( false )
            <hr class="mt-2 mb-2">
            <div
                class="list-meta2 d-flex justify-content-between align-items-center">
                <span class="for-what">For Rent</span>
                <div class="icons d-flex align-items-center">
                    <a href="#"><span class="flaticon-fullscreen"></span></a>
                    <a href="#"><span class="flaticon-new-tab"></span></a>
                    <a href="#"><span class="flaticon-like"></span></a>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- OOOLLLDDD -->
<div class="col-xl-4 col-md-6 item-listing-wrap list-view d-none">
    <div class="item-wrap item-wrap-no-frame h-100">
        <div class="d-flex flex-column align-items-center h-100 position-relative">
            <div class="item-header">

                @if ($item->propertyTypes?->count())
                <div class="labels-wrap labels-right">
                    @foreach ($item->propertyTypes as $typeItem)
                        <span class="label-status label status-color-{{ $loop->index + 1 }}">{{ $typeItem->title }}</span>
                    @endforeach
                </div>
                @endif
                <ul class="item-price-wrap list-unstyled mb-0">
                    <li class="item-price">{{ $item->priceWithPrefixPostfix() }}</li>
                </ul>

                <ul class="item-tools list-unstyled mb-0">
                    <li class="item-tool item-favorite remove-favourite" role="button" tabindex="0" aria-label="Favourite" data-fav-property-id="{{ $favProperty->id }}">
                        <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Remove Favourite">
                            <i class="icon icon-love-it-remove"></i>
                        </span>
                    </li>
                </ul>
                <div class="listing-image-wrap">
                    <div class="listing-thumb">
                        <a href="{{ route('property.details', $item) }}" class="listing-featured-thumb hover-effect">
                            <img src="{!! $item->getPrimaryImageUrl() ?? theme_asset('images/properties/property-2.jpg') !!}" alt="{{$item->title}}">
                        </a>
                    </div>
                </div>
                @if( isset($isSold) && $isSold )
                <span class="label-sold label">Sold</span>
                @endif
            </div>
            <div class="item-body flex-grow-1">
                <div class="custom-attr-wrapper">
                    <div class="custom-attr-return">
                        Gross Return<br>
                        {{ $item->getGrossReturnFormat() ?? 'N/A' }}
                    </div>
                    <div class="custom-attr-yield">
                        Gross Yield<br>
                        {{ $item->getGrossYieldFormat() ?? 'N/A' }}
                    </div>
                    <div class="custom-attr-yield custom-attr-cap-growth">
                        <span>Capital Growth<br>
                            {{ $item?->getCapitalGrowthFormat() ?? 'N/A' }}</span>
                    </div>
                    <div class="custom-attr-yield">
                        Vacancy Rate<br>
                        {{ $item?->getAnnualGrowthVacancyRateFormat() ?? '0.00%' }}
                    </div>
                </div>
                <h2 class="item-title">
                    <a href="{{ route('property.details', $item) }}">{{ mb_strimwidth($item->title, 0, 50, "...") }}</a>
                </h2>
                <address class="item-address">{{ $item->getFullAddress() }}</address>
                <ul class="item-amenities item-amenities-with-icons list-unstyled mb-0">
                    <li class="h-beds">
                        <span class="hz-figure"><i class="icon icon-hotel-double-bed-1 me-1"></i>{{ $item->bedrooms ?? 0 }} </span> Bedrooms
                    </li>
                    <li class="h-baths">
                        <span class="hz-figure"><i class="icon icon-bathroom-shower-1 me-1"></i>{{ $item->bathrooms ?? 0 }} </span>Bathrooms
                    </li>
                    <li class="h-cars">
                        <span class="hz-figure"><i class="icon icon-car-1 me-1"></i>{{ $item->garages ?? 0 }}</span>Cars
                    </li>
                </ul>
                <div class="property_type_lable">
                    <span class="lable fw-medium small">House and Land</span>
                </div>
            </div>
        </div>
    </div>
</div>
