{{-- Dynamic External CSS and Google Fonts Component --}}
@php
    $subdomainData = config('subdomain_data');
    $styling = $subdomainData['styling'] ?? [];
    
    // Helper function to safely convert values to strings
    if (!function_exists('safeCssString')) {
        function safeCssString($value, $default = '') {
            if (is_array($value)) {
                return implode(', ', $value);
            }
            return (string) ($value ?? $default);
        }
    }
@endphp

{{-- Google Fonts --}}
@if(isset($styling['google_fonts']) && !empty($styling['google_fonts']))
    @php
        $googleFonts = safeCssString($styling['google_fonts']);
        $fontFamily = explode(':', $googleFonts)[0] ?? 'Roboto';
    @endphp
    @if(!empty($googleFonts))
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family={{ $googleFonts }}&display=swap" rel="stylesheet">
        
        <style>
            body, .default-font {
                font-family: '{{ $fontFamily }}', var(--default-font), sans-serif !important;
            }
        </style>
    @endif
@endif

{{-- External CSS URL --}}
@if(isset($styling['external_css_url']) && !empty($styling['external_css_url']))
    @php
        $externalCss = safeCssString($styling['external_css_url']);
    @endphp
    @if(!empty($externalCss))
        <link rel="stylesheet" href="{{ $externalCss }}" media="screen">
    @endif
@endif

{{-- Custom CSS --}}
@if(isset($styling['custom_css']) && !empty($styling['custom_css']))
    @php
        $customCss = safeCssString($styling['custom_css']);
    @endphp
    @if(!empty($customCss))
        <style>
            {!! $customCss !!}
        </style>
    @endif
@endif