<div class="{{ $mainClass ?? 'col-md-6' }} {{ $gridClass ?? '' }}">
    <!-- Property Item Start -->
    <div class="property-item {{ isset( $wowEffectEnable ) && !empty( $wowEffectEnable ) ? ' wow fadeInUp ' : '' }}" @if( isset( $wowEffectEnable ) && !empty( $wowEffectEnable ) ) data-wow-delay="0.25s" @endif >
        <!-- Property Item Header Start -->
        <div class="property-header">
            <figure class="image-anime">
                <img src="{!! $item->getPrimaryImageUrl() ?? theme_asset('images/property-1.jpg') !!}" alt="">
            </figure>

            @if( $item?->propertySetting?->is_featured )
            <span class="property-label label-featured">Featured</span>
            @endif
            @if ($item->propertyTypes?->count())
                <div class="labels-wrap labels-right">
                    @foreach ($item->propertyTypes as $typeItem)
                        @if( in_array( strtolower( $typeItem->title ), ['sda', 'co-living', 'rooming house', 'smsf single contract', 'dual key', 'dual occupancy', 'duplex']))
                        <span class="property-label status-color-{{ $loop->index + 1 }} type-color-{{ \Str::slug($typeItem->title) }}">{{ \Str::contains( strtolower( $typeItem->title ), 'smsf' ) ? 'SMSF' : $typeItem->title }}</span>
                        @endif
                    @endforeach
                    @if ($item->propertyStatuses?->count() && $item->propertyStatuses?->firstWhere('title', 'Rental Guarantee'))
                        <span class="property-label status-color type-color-rental-guarantee">Rental Guarantee</span>
                        <span class="label-status label status-color type-color-rental-guarantee">Rental Guarantee</span>
                    @endif
                </div>
            @endif
            <div class="header-actions">

                <div class="d-inline-block item-tool item-compare {{ in_array( $item->id, session('compare_properties', []) ) ? 'remove-compare-property' : 'add-to-compare' }}" role="button" tabindex="0" data-property-id="{{ $item->id }}" aria-controls="offcanvasRight" aria-label="Compare">
                    <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Add to Compare" class="add-to-compare-icon">
                        <i class="fa fa-add "></i>
                    </span>
                    <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Remove Compare" class="remove-compare-icon">
                        <i class="fa fa-minus-circle "></i>
                    </span>
                </div>
                <livewire:add-to-favourite-action :key='"add-fav".$item->id' :propertyId="$item->id"/>
            </div>

        </div>
        <!-- Property Item Header End -->

        <!-- Property Item Body Start -->
        <div class="property-body">
            <h3>{{ mb_strimwidth($item->title, 0, 50, "...") }}</h3>
            <p>{{ $item->getFullAddress() }}</p>

            <div class="property-meta">
                <div class="property-amenity-item">
                    <div class="icon-box">
                        <img src="{{ theme_asset('images/icon-badroom.svg') }}" alt="">
                    </div>

                    <span>{{ $item->bedrooms ?? 0 }} Bedrooms</span>
                </div>

                <div class="property-amenity-item">
                    <div class="icon-box">
                        <img src="{{ theme_asset('images/icon-bathroom.svg') }}" alt="">
                    </div>

                    <span>{{ $item->bathrooms ?? 0 }} Bathrooms</span>
                </div>

                <div class="property-amenity-item">
                    <div class="icon-box">
                        <img src="{{ theme_asset('images/icon-area.svg') }}" alt="">
                    </div>

                    <span>{{ $item->areaSizeWithPostfix() ?? 'N/A' }}</span>
                </div>

                <div class="property-amenity-item">
                    <div class="icon-box">
                        <img src="{{ theme_asset('images/icon-garage.svg') }}" alt="">
                    </div>

                    <span>{{ $item->garages ?? 0 }} Garages</span>
                </div>
            </div>
        </div>
        <!-- Property Item Body End -->

        <!-- Property Item Footer Start -->
        <div class="property-footer">
            <p class="property-price">{{ $item->priceWithPrefixPostfix( 0 ) }}</p>
            <div class="property-actions">
                <a href="{{ route('property.details', $item) }}" class="btn-default">View Property</a>
            </div>
        </div>
        <!-- Property Item Footer Start -->
    </div>
    <!-- Property Item End -->
</div>

