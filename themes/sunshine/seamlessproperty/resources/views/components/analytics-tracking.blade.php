{{-- Dynamic Analytics and Tracking Component --}}
@php
    $subdomainData = config('subdomain_data');
    $analytics = $subdomainData['analytics'] ?? [];
    $integrations = $subdomainData['integrations'] ?? [];
@endphp

{{-- Google Analytics --}}
@if(isset($analytics['google_analytics']) && !empty($analytics['google_analytics']))
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id={{ $analytics['google_analytics'] }}"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', '{{ $analytics['google_analytics'] }}');
</script>
@endif

{{-- Google Tag Manager --}}
@if(isset($analytics['google_tag_manager']) && !empty($analytics['google_tag_manager']))
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','{{ $analytics['google_tag_manager'] }}');</script>
@endif

{{-- Facebook Pixel --}}
@if(isset($analytics['facebook_pixel']) && !empty($analytics['facebook_pixel']))
<!-- Facebook Pixel -->
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '{{ $analytics['facebook_pixel'] }}');
fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none"
src="https://www.facebook.com/tr?id={{ $analytics['facebook_pixel'] }}&ev=PageView&noscript=1"
/></noscript>
@endif

{{-- Live Chat Integration --}}
@if(isset($integrations['live_chat']['provider']) && isset($integrations['live_chat']['chat_id']))
@php
    $chatProvider = $integrations['live_chat']['provider'];
    $chatId = $integrations['live_chat']['chat_id'];
@endphp

@if($chatProvider === 'intercom')
<!-- Intercom Live Chat -->
<script>
  window.intercomSettings = {
    app_id: "{{ $chatId }}"
  };
</script>
<script>(function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',w.intercomSettings);}else{var d=document;var i=function(){i.c(arguments);};i.q=[];i.c=function(args){i.q.push(args);};w.Intercom=i;var l=function(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/{{ $chatId }}';var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s, x);};if(document.readyState==='complete'){l();}else if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})();</script>

@elseif($chatProvider === 'zendesk')
<!-- Zendesk Live Chat -->
<script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key={{ $chatId }}"></script>

@elseif($chatProvider === 'tidio')
<!-- Tidio Live Chat -->
<script src="//code.tidio.co/{{ $chatId }}.js" async></script>

@elseif($chatProvider === 'drift')
<!-- Drift Live Chat -->
<script>
"use strict";
!function() {
  var t = window.driftt = window.drift = window.driftt || [];
  if (!t.init) {
    if (t.invoked) return void (window.console && console.error && console.error("Drift snippet included twice."));
    t.invoked = !0, t.methods = [ "identify", "config", "track", "reset", "debug", "show", "ping", "page", "hide", "off", "on" ], 
    t.factory = function(e) {
      return function() {
        var n = Array.prototype.slice.call(arguments);
        return n.unshift(e), t.push(n), t;
      };
    }, t.methods.forEach(function(e) {
      t[e] = t.factory(e);
    }), t.load = function(t) {
      var e = 3e5, n = Math.ceil(new Date() / e) * e, o = document.createElement("script");
      o.type = "text/javascript", o.async = !0, o.crossorigin = "anonymous", o.src = "https://js.driftt.com/include/" + n + "/" + t + ".js";
      var i = document.getElementsByTagName("script")[0];
      i.parentNode.insertBefore(o, i);
    };
  }
}();
drift.SNIPPET_VERSION = '0.3.1';
drift.load('{{ $chatId }}');
</script>

@elseif($chatProvider === 'crisp')
<!-- Crisp Live Chat -->
<script type="text/javascript">
window.$crisp=[];window.CRISP_WEBSITE_ID="{{ $chatId }}";
(function(){d=document;s=d.createElement("script");s.src="https://client.crisp.chat/l.js";s.async=1;d.getElementsByTagName("head")[0].appendChild(s);})();
</script>
@endif
@endif

{{-- Custom Scripts --}}
@if(isset($integrations['custom_scripts']) && !empty($integrations['custom_scripts']))
<!-- Custom Scripts -->
<script>
{!! $integrations['custom_scripts'] !!}
</script>
@endif