var MY_MAPTYPE_ID = 'style_KINESB';

function initialize() {
  var featureOpts = [
    {
        "featureType": "administrative",
        "elementType": "labels.text.fill",
        "stylers": [
            {
                "color": "#666666"
            }
        ]
    },
    {
    "featureType": 'all',
    "elementType": 'labels',
    "stylers": [
            { visibility: 'simplified' }
        ]
    },
    {
        "featureType": "landscape",
        "elementType": "all",
        "stylers": [
            {
                "color": "#e2e2e2"
            }
        ]
    },
    {
        "featureType": "poi",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "road",
        "elementType": "all",
        "stylers": [
            {
                "saturation": -100
            },
            {
                "lightness": 45
            },
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "road.highway",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "road.arterial",
        "elementType": "labels.icon",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "transit",
        "elementType": "all",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "water",
        "elementType": "all",
        "stylers": [
            {
                "color": "#aadaff"
            },
            {
                "visibility": "on"
            }
        ]
    }
];
  var myGent = new google.maps.LatLng(lat,lng);
  var Kine = new google.maps.LatLng(lat,lng);
  var mapOptions = {
    zoom: 17,
    mapTypeControl: true,
    zoomControl: true,
    zoomControlOptions: {
        style: google.maps.ZoomControlStyle.SMALL,
        position: google.maps.ControlPosition.LEFT_TOP,
        mapTypeIds: [google.maps.MapTypeId.ROADMAP]
    },
    mapTypeId: google.maps.MapTypeId.ROADMAP,
    scaleControl: false,
    streetViewControl: false,
    center: myGent
  }
  var map = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);
  var styledMapOptions = {
    name: 'style_KINESB'
  };

    var image = markerPin;
    const icon = {
        url: image, // url
        scaledSize: new google.maps.Size(15, 15), // scaled size
        origin: new google.maps.Point(0,0), // origin
        anchor: new google.maps.Point(0, 0) // anchor
    };

    function CustomMarker(latlng, map, args, markerIco) {
        this.latlng = latlng;
        this.args = args;
        this.markerIco = markerIco;
        this.setMap(map);
     }
     CustomMarker.prototype = new google.maps.OverlayView();
     CustomMarker.prototype.draw = function () {
        var self = this;
        var div = this.div;
        if (!div) {
           div = this.div = document.createElement('div');
           div.className = 'map-marker-container';
           div.innerHTML = '<div class="marker-container">' +
              '<div class="marker-card">' +
              self.markerIco +
              '</div>' +
              '</div>'
        //    google.maps.event.addDomListener(div, "click", function (event) {
        //       $('.map-marker-container').removeClass('clicked infoBox-opened');
        //       google.maps.event.trigger(self, "click");
        //       console.log( $(this ));
        //       $(this).addClass('clicked infoBox-opened');
        //       if( self?.args?.prop_slug ){
        //           // if( $("[data-prop-slug="+self?.args?.prop_slug+"]").length ){
        //           //     $("[data-prop-slug="+self?.args?.prop_slug+"]")[0].scrollIntoView();
        //           // }
        //       }
        //    });
           if (typeof (self.args.marker_id) !== 'undefined') {}
           var panes = this.getPanes();
           panes.overlayImage.appendChild(div);
        }
        var point = this.getProjection().fromLatLngToDivPixel(this.latlng);
        if (point) {
           div.style.left = (point.x) + 'px';
           div.style.top = (point.y) + 'px';
        }
     };
     CustomMarker.prototype.remove = function () {
        if (this.div) {
           this.div.parentNode.removeChild(this.div);
           this.div = null;
           $(this).removeClass('clicked');
        }
     };
     CustomMarker.prototype.getPosition = function () {
        return this.latlng;
     };

    var overlay = new CustomMarker(Kine, map, {marker_id: '1'}, '<img src="'+image+'"></img>');

    // var ib = new InfoBox({maxWidth: 420, boxStyle:{ width: '420px'}});

    var boxText = document.createElement("div");
    boxText.className = 'map-box'

    var boxOptions = {
        content: boxText,
        disableAutoPan: false,
        alignBottom: true,
        maxWidth: 0,
        pixelOffset: new google.maps.Size(-134, -55),
        zIndex: null,
        boxStyle: {
           width: "320px"
        },
        closeBoxMargin: "0",
        closeBoxURL: "",
        infoBoxClearance: new google.maps.Size(25, 25),
        isHidden: false,
        pane: "floatPane",
        enableEventPropagation: false,
     };
    google.maps.event.addDomListener(overlay, 'click', (function (overlay) {
        return function () {
        //    ib.setOptions(boxOptions);
           boxText.innerHTML = `
                    <div class="feat_property list listing mb-0">
                    <div class="infoBox-close btn btn-sm btn-danger"><i class="fa fa-times icon-close icon"></i></div>
            <div class="thumb">
                <img class="img-whp"
                    src="${propertyPrimaryImage}">
                <div class="thmb_cntnt">
                </div>
            </div>
            <div class="details style7">
                <div class="tc_content">
                    <div class="wrapper float-left fn-md">
                        <h4><a href="#">${mapTitle}</a>
                        </h4>
                        <p>${mapDescription}</p>
                    </div>
                </div>
                <div class="fp_footer">
                    <ul class="fp_meta float-left mb0">
                        <li class="list-inline-item">
                            <a href="#">
                                <span class="heading-color fw600">${propertyPrice}</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
            `;
        //    ib.close();
        //    ib.open(map, overlay);
        //    currentInfobox = locations[i][3];
           google.maps.event.addListener(ib, 'domready', function () {
              $('.infoBox-close').on('click', function (e) {
                 e.preventDefault();
                //  ib.close();
                 $('.map-marker-container').removeClass('clicked infoBox-opened');
              });
           });
        }
     })(overlay));


//   var marker = new google.maps.Marker({
//         position: Kine,
//         map: map,
//         animation: google.maps.Animation.DROP,
//         title: typeof mapTitle !== 'undefined' ? mapTitle : '',
//         icon: icon
//   });
  if( typeof mapTitle !== 'undefined' ){
        var infoContent = `<div class="card">
            <div class="card-header">
                <h5 class="card-title">${mapTitle}</h5>
            </div>
            <div class="card-body">
                ${ typeof mapDescription !== 'undefined' ? mapDescription:'' }
            </div>
        </div>`;

      var infoWindow = new google.maps.InfoWindow({
        content: infoContent
      })

    //   infoWindow.open({
    //     anchor: marker,
    //     map,
    //   });

    setTimeout(() => {
        // google.maps.event.trigger(overlay, "click");

    }, 2000);
  }


  var customMapType = new google.maps.StyledMapType(featureOpts, styledMapOptions);
//   map.mapTypes.set(MY_MAPTYPE_ID, customMapType);

}
google.maps.event.addDomListener(window, 'load', initialize);
