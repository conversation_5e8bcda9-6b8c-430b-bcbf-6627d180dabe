/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function() {
return /******/ (function() { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./libs/sweetalert2/sweetalert2.js":
/*!*****************************************!*\
  !*** ./libs/sweetalert2/sweetalert2.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Swal: function() { return /* binding */ Swal; }\n/* harmony export */ });\n/* harmony import */ var sweetalert2_dist_sweetalert2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sweetalert2/dist/sweetalert2 */ \"./node_modules/sweetalert2/dist/sweetalert2.js\");\n/* harmony import */ var sweetalert2_dist_sweetalert2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sweetalert2_dist_sweetalert2__WEBPACK_IMPORTED_MODULE_0__);\n\nvar Swal = sweetalert2_dist_sweetalert2__WEBPACK_IMPORTED_MODULE_0___default().mixin({\n  buttonsStyling: false,\n  customClass: {\n    confirmButton: 'btn btn-primary',\n    cancelButton: 'btn btn-label-danger',\n    denyButton: 'btn btn-label-secondary'\n  }\n});\ntry {\n  window.Swal = Swal;\n} catch (e) {}\n\n\n//# sourceURL=webpack://Vuexy/./libs/sweetalert2/sweetalert2.js?");

/***/ }),

/***/ "./node_modules/sweetalert2/dist/sweetalert2.js":
/*!******************************************************!*\
  !*** ./node_modules/sweetalert2/dist/sweetalert2.js ***!
  \******************************************************/
/***/ (function(module) {

eval("/*!\n* sweetalert2 v11.10.4\n* Released under the MIT License.\n*/\n(function (global, factory) {\n   true ? module.exports = factory() :\n  0;\n})(this, (function () { 'use strict';\n\n  function _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n  }\n  function _construct(t, e, r) {\n    if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n    var o = [null];\n    o.push.apply(o, e);\n    var p = new (t.bind.apply(t, o))();\n    return r && _setPrototypeOf(p, r.prototype), p;\n  }\n  function _isNativeReflectConstruct() {\n    try {\n      var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function () {\n      return !!t;\n    })();\n  }\n  function _iterableToArrayLimit(r, l) {\n    var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n    if (null != t) {\n      var e,\n        n,\n        i,\n        u,\n        a = [],\n        f = !0,\n        o = !1;\n      try {\n        if (i = (t = t.call(r)).next, 0 === l) {\n          if (Object(t) !== t) return;\n          f = !1;\n        } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n      } catch (r) {\n        o = !0, n = r;\n      } finally {\n        try {\n          if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;\n        } finally {\n          if (o) throw n;\n        }\n      }\n      return a;\n    }\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : String(i);\n  }\n  function _typeof(o) {\n    \"@babel/helpers - typeof\";\n\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n      return typeof o;\n    } : function (o) {\n      return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n  }\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n  function _defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n    }\n  }\n  function _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", {\n      writable: false\n    });\n    return Constructor;\n  }\n  function _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n      constructor: {\n        value: subClass,\n        writable: true,\n        configurable: true\n      }\n    });\n    Object.defineProperty(subClass, \"prototype\", {\n      writable: false\n    });\n    if (superClass) _setPrototypeOf(subClass, superClass);\n  }\n  function _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n      return o.__proto__ || Object.getPrototypeOf(o);\n    };\n    return _getPrototypeOf(o);\n  }\n  function _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n      o.__proto__ = p;\n      return o;\n    };\n    return _setPrototypeOf(o, p);\n  }\n  function _assertThisInitialized(self) {\n    if (self === void 0) {\n      throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n  }\n  function _possibleConstructorReturn(self, call) {\n    if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n      return call;\n    } else if (call !== void 0) {\n      throw new TypeError(\"Derived constructors may only return object or undefined\");\n    }\n    return _assertThisInitialized(self);\n  }\n  function _superPropBase(object, property) {\n    while (!Object.prototype.hasOwnProperty.call(object, property)) {\n      object = _getPrototypeOf(object);\n      if (object === null) break;\n    }\n    return object;\n  }\n  function _get() {\n    if (typeof Reflect !== \"undefined\" && Reflect.get) {\n      _get = Reflect.get.bind();\n    } else {\n      _get = function _get(target, property, receiver) {\n        var base = _superPropBase(target, property);\n        if (!base) return;\n        var desc = Object.getOwnPropertyDescriptor(base, property);\n        if (desc.get) {\n          return desc.get.call(arguments.length < 3 ? target : receiver);\n        }\n        return desc.value;\n      };\n    }\n    return _get.apply(this, arguments);\n  }\n  function _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n  }\n  function _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n  }\n  function _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n  }\n  function _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n  }\n  function _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n  }\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n    return arr2;\n  }\n  function _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  function _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  function _classPrivateFieldGet(receiver, privateMap) {\n    var descriptor = _classExtractFieldDescriptor(receiver, privateMap, \"get\");\n    return _classApplyDescriptorGet(receiver, descriptor);\n  }\n  function _classPrivateFieldSet(receiver, privateMap, value) {\n    var descriptor = _classExtractFieldDescriptor(receiver, privateMap, \"set\");\n    _classApplyDescriptorSet(receiver, descriptor, value);\n    return value;\n  }\n  function _classExtractFieldDescriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) {\n      throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n    }\n    return privateMap.get(receiver);\n  }\n  function _classApplyDescriptorGet(receiver, descriptor) {\n    if (descriptor.get) {\n      return descriptor.get.call(receiver);\n    }\n    return descriptor.value;\n  }\n  function _classApplyDescriptorSet(receiver, descriptor, value) {\n    if (descriptor.set) {\n      descriptor.set.call(receiver, value);\n    } else {\n      if (!descriptor.writable) {\n        throw new TypeError(\"attempted to set read only private field\");\n      }\n      descriptor.value = value;\n    }\n  }\n  function _checkPrivateRedeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n      throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n  }\n  function _classPrivateFieldInitSpec(obj, privateMap, value) {\n    _checkPrivateRedeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n  }\n\n  var RESTORE_FOCUS_TIMEOUT = 100;\n\n  /** @type {GlobalState} */\n  var globalState = {};\n  var focusPreviousActiveElement = function focusPreviousActiveElement() {\n    if (globalState.previousActiveElement instanceof HTMLElement) {\n      globalState.previousActiveElement.focus();\n      globalState.previousActiveElement = null;\n    } else if (document.body) {\n      document.body.focus();\n    }\n  };\n\n  /**\n   * Restore previous active (focused) element\n   *\n   * @param {boolean} returnFocus\n   * @returns {Promise<void>}\n   */\n  var restoreActiveElement = function restoreActiveElement(returnFocus) {\n    return new Promise(function (resolve) {\n      if (!returnFocus) {\n        return resolve();\n      }\n      var x = window.scrollX;\n      var y = window.scrollY;\n      globalState.restoreFocusTimeout = setTimeout(function () {\n        focusPreviousActiveElement();\n        resolve();\n      }, RESTORE_FOCUS_TIMEOUT); // issues/900\n\n      window.scrollTo(x, y);\n    });\n  };\n\n  var swalPrefix = 'swal2-';\n\n  /**\n   * @typedef\n   * { | 'container'\n   *   | 'shown'\n   *   | 'height-auto'\n   *   | 'iosfix'\n   *   | 'popup'\n   *   | 'modal'\n   *   | 'no-backdrop'\n   *   | 'no-transition'\n   *   | 'toast'\n   *   | 'toast-shown'\n   *   | 'show'\n   *   | 'hide'\n   *   | 'close'\n   *   | 'title'\n   *   | 'html-container'\n   *   | 'actions'\n   *   | 'confirm'\n   *   | 'deny'\n   *   | 'cancel'\n   *   | 'default-outline'\n   *   | 'footer'\n   *   | 'icon'\n   *   | 'icon-content'\n   *   | 'image'\n   *   | 'input'\n   *   | 'file'\n   *   | 'range'\n   *   | 'select'\n   *   | 'radio'\n   *   | 'checkbox'\n   *   | 'label'\n   *   | 'textarea'\n   *   | 'inputerror'\n   *   | 'input-label'\n   *   | 'validation-message'\n   *   | 'progress-steps'\n   *   | 'active-progress-step'\n   *   | 'progress-step'\n   *   | 'progress-step-line'\n   *   | 'loader'\n   *   | 'loading'\n   *   | 'styled'\n   *   | 'top'\n   *   | 'top-start'\n   *   | 'top-end'\n   *   | 'top-left'\n   *   | 'top-right'\n   *   | 'center'\n   *   | 'center-start'\n   *   | 'center-end'\n   *   | 'center-left'\n   *   | 'center-right'\n   *   | 'bottom'\n   *   | 'bottom-start'\n   *   | 'bottom-end'\n   *   | 'bottom-left'\n   *   | 'bottom-right'\n   *   | 'grow-row'\n   *   | 'grow-column'\n   *   | 'grow-fullscreen'\n   *   | 'rtl'\n   *   | 'timer-progress-bar'\n   *   | 'timer-progress-bar-container'\n   *   | 'scrollbar-measure'\n   *   | 'icon-success'\n   *   | 'icon-warning'\n   *   | 'icon-info'\n   *   | 'icon-question'\n   *   | 'icon-error'\n   * } SwalClass\n   * @typedef {Record<SwalClass, string>} SwalClasses\n   */\n\n  /**\n   * @typedef {'success' | 'warning' | 'info' | 'question' | 'error'} SwalIcon\n   * @typedef {Record<SwalIcon, string>} SwalIcons\n   */\n\n  /** @type {SwalClass[]} */\n  var classNames = ['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'no-transition', 'toast', 'toast-shown', 'show', 'hide', 'close', 'title', 'html-container', 'actions', 'confirm', 'deny', 'cancel', 'default-outline', 'footer', 'icon', 'icon-content', 'image', 'input', 'file', 'range', 'select', 'radio', 'checkbox', 'label', 'textarea', 'inputerror', 'input-label', 'validation-message', 'progress-steps', 'active-progress-step', 'progress-step', 'progress-step-line', 'loader', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen', 'rtl', 'timer-progress-bar', 'timer-progress-bar-container', 'scrollbar-measure', 'icon-success', 'icon-warning', 'icon-info', 'icon-question', 'icon-error'];\n  var swalClasses = classNames.reduce(function (acc, className) {\n    acc[className] = swalPrefix + className;\n    return acc;\n  }, /** @type {SwalClasses} */{});\n\n  /** @type {SwalIcon[]} */\n  var icons = ['success', 'warning', 'info', 'question', 'error'];\n  var iconTypes = icons.reduce(function (acc, icon) {\n    acc[icon] = swalPrefix + icon;\n    return acc;\n  }, /** @type {SwalIcons} */{});\n\n  var consolePrefix = 'SweetAlert2:';\n\n  /**\n   * Capitalize the first letter of a string\n   *\n   * @param {string} str\n   * @returns {string}\n   */\n  var capitalizeFirstLetter = function capitalizeFirstLetter(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  };\n\n  /**\n   * Standardize console warnings\n   *\n   * @param {string | string[]} message\n   */\n  var warn = function warn(message) {\n    console.warn(\"\".concat(consolePrefix, \" \").concat(_typeof(message) === 'object' ? message.join(' ') : message));\n  };\n\n  /**\n   * Standardize console errors\n   *\n   * @param {string} message\n   */\n  var error = function error(message) {\n    console.error(\"\".concat(consolePrefix, \" \").concat(message));\n  };\n\n  /**\n   * Private global state for `warnOnce`\n   *\n   * @type {string[]}\n   * @private\n   */\n  var previousWarnOnceMessages = [];\n\n  /**\n   * Show a console warning, but only if it hasn't already been shown\n   *\n   * @param {string} message\n   */\n  var warnOnce = function warnOnce(message) {\n    if (!previousWarnOnceMessages.includes(message)) {\n      previousWarnOnceMessages.push(message);\n      warn(message);\n    }\n  };\n\n  /**\n   * Show a one-time console warning about deprecated params/methods\n   *\n   * @param {string} deprecatedParam\n   * @param {string} useInstead\n   */\n  var warnAboutDeprecation = function warnAboutDeprecation(deprecatedParam, useInstead) {\n    warnOnce(\"\\\"\".concat(deprecatedParam, \"\\\" is deprecated and will be removed in the next major release. Please use \\\"\").concat(useInstead, \"\\\" instead.\"));\n  };\n\n  /**\n   * If `arg` is a function, call it (with no arguments or context) and return the result.\n   * Otherwise, just pass the value through\n   *\n   * @param {Function | any} arg\n   * @returns {any}\n   */\n  var callIfFunction = function callIfFunction(arg) {\n    return typeof arg === 'function' ? arg() : arg;\n  };\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  var hasToPromiseFn = function hasToPromiseFn(arg) {\n    return arg && typeof arg.toPromise === 'function';\n  };\n\n  /**\n   * @param {any} arg\n   * @returns {Promise<any>}\n   */\n  var asPromise = function asPromise(arg) {\n    return hasToPromiseFn(arg) ? arg.toPromise() : Promise.resolve(arg);\n  };\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  var isPromise = function isPromise(arg) {\n    return arg && Promise.resolve(arg) === arg;\n  };\n\n  /**\n   * Gets the popup container which contains the backdrop and the popup itself.\n   *\n   * @returns {HTMLElement | null}\n   */\n  var getContainer = function getContainer() {\n    return document.body.querySelector(\".\".concat(swalClasses.container));\n  };\n\n  /**\n   * @param {string} selectorString\n   * @returns {HTMLElement | null}\n   */\n  var elementBySelector = function elementBySelector(selectorString) {\n    var container = getContainer();\n    return container ? container.querySelector(selectorString) : null;\n  };\n\n  /**\n   * @param {string} className\n   * @returns {HTMLElement | null}\n   */\n  var elementByClass = function elementByClass(className) {\n    return elementBySelector(\".\".concat(className));\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getPopup = function getPopup() {\n    return elementByClass(swalClasses.popup);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getIcon = function getIcon() {\n    return elementByClass(swalClasses.icon);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getIconContent = function getIconContent() {\n    return elementByClass(swalClasses['icon-content']);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getTitle = function getTitle() {\n    return elementByClass(swalClasses.title);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getHtmlContainer = function getHtmlContainer() {\n    return elementByClass(swalClasses['html-container']);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getImage = function getImage() {\n    return elementByClass(swalClasses.image);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getProgressSteps = function getProgressSteps() {\n    return elementByClass(swalClasses['progress-steps']);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getValidationMessage = function getValidationMessage() {\n    return elementByClass(swalClasses['validation-message']);\n  };\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  var getConfirmButton = function getConfirmButton() {\n    return /** @type {HTMLButtonElement} */elementBySelector(\".\".concat(swalClasses.actions, \" .\").concat(swalClasses.confirm));\n  };\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  var getCancelButton = function getCancelButton() {\n    return /** @type {HTMLButtonElement} */elementBySelector(\".\".concat(swalClasses.actions, \" .\").concat(swalClasses.cancel));\n  };\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  var getDenyButton = function getDenyButton() {\n    return /** @type {HTMLButtonElement} */elementBySelector(\".\".concat(swalClasses.actions, \" .\").concat(swalClasses.deny));\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getInputLabel = function getInputLabel() {\n    return elementByClass(swalClasses['input-label']);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getLoader = function getLoader() {\n    return elementBySelector(\".\".concat(swalClasses.loader));\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getActions = function getActions() {\n    return elementByClass(swalClasses.actions);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getFooter = function getFooter() {\n    return elementByClass(swalClasses.footer);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getTimerProgressBar = function getTimerProgressBar() {\n    return elementByClass(swalClasses['timer-progress-bar']);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  var getCloseButton = function getCloseButton() {\n    return elementByClass(swalClasses.close);\n  };\n\n  // https://github.com/jkup/focusable/blob/master/index.js\n  var focusable = \"\\n  a[href],\\n  area[href],\\n  input:not([disabled]),\\n  select:not([disabled]),\\n  textarea:not([disabled]),\\n  button:not([disabled]),\\n  iframe,\\n  object,\\n  embed,\\n  [tabindex=\\\"0\\\"],\\n  [contenteditable],\\n  audio[controls],\\n  video[controls],\\n  summary\\n\";\n  /**\n   * @returns {HTMLElement[]}\n   */\n  var getFocusableElements = function getFocusableElements() {\n    var popup = getPopup();\n    if (!popup) {\n      return [];\n    }\n    /** @type {NodeListOf<HTMLElement>} */\n    var focusableElementsWithTabindex = popup.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]):not([tabindex=\"0\"])');\n    var focusableElementsWithTabindexSorted = Array.from(focusableElementsWithTabindex)\n    // sort according to tabindex\n    .sort(function (a, b) {\n      var tabindexA = parseInt(a.getAttribute('tabindex') || '0');\n      var tabindexB = parseInt(b.getAttribute('tabindex') || '0');\n      if (tabindexA > tabindexB) {\n        return 1;\n      } else if (tabindexA < tabindexB) {\n        return -1;\n      }\n      return 0;\n    });\n\n    /** @type {NodeListOf<HTMLElement>} */\n    var otherFocusableElements = popup.querySelectorAll(focusable);\n    var otherFocusableElementsFiltered = Array.from(otherFocusableElements).filter(function (el) {\n      return el.getAttribute('tabindex') !== '-1';\n    });\n    return _toConsumableArray(new Set(focusableElementsWithTabindexSorted.concat(otherFocusableElementsFiltered))).filter(function (el) {\n      return isVisible$1(el);\n    });\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  var isModal = function isModal() {\n    return hasClass(document.body, swalClasses.shown) && !hasClass(document.body, swalClasses['toast-shown']) && !hasClass(document.body, swalClasses['no-backdrop']);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  var isToast = function isToast() {\n    var popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return hasClass(popup, swalClasses.toast);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  var isLoading = function isLoading() {\n    var popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return popup.hasAttribute('data-loading');\n  };\n\n  /**\n   * Securely set innerHTML of an element\n   * https://github.com/sweetalert2/sweetalert2/issues/1926\n   *\n   * @param {HTMLElement} elem\n   * @param {string} html\n   */\n  var setInnerHtml = function setInnerHtml(elem, html) {\n    elem.textContent = '';\n    if (html) {\n      var parser = new DOMParser();\n      var parsed = parser.parseFromString(html, \"text/html\");\n      var head = parsed.querySelector('head');\n      head && Array.from(head.childNodes).forEach(function (child) {\n        elem.appendChild(child);\n      });\n      var body = parsed.querySelector('body');\n      body && Array.from(body.childNodes).forEach(function (child) {\n        if (child instanceof HTMLVideoElement || child instanceof HTMLAudioElement) {\n          elem.appendChild(child.cloneNode(true)); // https://github.com/sweetalert2/sweetalert2/issues/2507\n        } else {\n          elem.appendChild(child);\n        }\n      });\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {boolean}\n   */\n  var hasClass = function hasClass(elem, className) {\n    if (!className) {\n      return false;\n    }\n    var classList = className.split(/\\s+/);\n    for (var i = 0; i < classList.length; i++) {\n      if (!elem.classList.contains(classList[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   */\n  var removeCustomClasses = function removeCustomClasses(elem, params) {\n    Array.from(elem.classList).forEach(function (className) {\n      if (!Object.values(swalClasses).includes(className) && !Object.values(iconTypes).includes(className) && !Object.values(params.showClass || {}).includes(className)) {\n        elem.classList.remove(className);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   * @param {string} className\n   */\n  var applyCustomClass = function applyCustomClass(elem, params, className) {\n    removeCustomClasses(elem, params);\n    if (params.customClass && params.customClass[className]) {\n      if (typeof params.customClass[className] !== 'string' && !params.customClass[className].forEach) {\n        warn(\"Invalid type of customClass.\".concat(className, \"! Expected string or iterable object, got \\\"\").concat(_typeof(params.customClass[className]), \"\\\"\"));\n        return;\n      }\n      addClass(elem, params.customClass[className]);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {import('./renderers/renderInput').InputClass | SweetAlertInput} inputClass\n   * @returns {HTMLInputElement | null}\n   */\n  var getInput$1 = function getInput(popup, inputClass) {\n    if (!inputClass) {\n      return null;\n    }\n    switch (inputClass) {\n      case 'select':\n      case 'textarea':\n      case 'file':\n        return popup.querySelector(\".\".concat(swalClasses.popup, \" > .\").concat(swalClasses[inputClass]));\n      case 'checkbox':\n        return popup.querySelector(\".\".concat(swalClasses.popup, \" > .\").concat(swalClasses.checkbox, \" input\"));\n      case 'radio':\n        return popup.querySelector(\".\".concat(swalClasses.popup, \" > .\").concat(swalClasses.radio, \" input:checked\")) || popup.querySelector(\".\".concat(swalClasses.popup, \" > .\").concat(swalClasses.radio, \" input:first-child\"));\n      case 'range':\n        return popup.querySelector(\".\".concat(swalClasses.popup, \" > .\").concat(swalClasses.range, \" input\"));\n      default:\n        return popup.querySelector(\".\".concat(swalClasses.popup, \" > .\").concat(swalClasses.input));\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement} input\n   */\n  var focusInput = function focusInput(input) {\n    input.focus();\n\n    // place cursor at end of text in text input\n    if (input.type !== 'file') {\n      // http://stackoverflow.com/a/2345915\n      var val = input.value;\n      input.value = '';\n      input.value = val;\n    }\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   * @param {boolean} condition\n   */\n  var toggleClass = function toggleClass(target, classList, condition) {\n    if (!target || !classList) {\n      return;\n    }\n    if (typeof classList === 'string') {\n      classList = classList.split(/\\s+/).filter(Boolean);\n    }\n    classList.forEach(function (className) {\n      if (Array.isArray(target)) {\n        target.forEach(function (elem) {\n          condition ? elem.classList.add(className) : elem.classList.remove(className);\n        });\n      } else {\n        condition ? target.classList.add(className) : target.classList.remove(className);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  var addClass = function addClass(target, classList) {\n    toggleClass(target, classList, true);\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  var removeClass = function removeClass(target, classList) {\n    toggleClass(target, classList, false);\n  };\n\n  /**\n   * Get direct child of an element by class name\n   *\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {HTMLElement | undefined}\n   */\n  var getDirectChildByClass = function getDirectChildByClass(elem, className) {\n    var children = Array.from(elem.children);\n    for (var i = 0; i < children.length; i++) {\n      var child = children[i];\n      if (child instanceof HTMLElement && hasClass(child, className)) {\n        return child;\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} property\n   * @param {*} value\n   */\n  var applyNumericalStyle = function applyNumericalStyle(elem, property, value) {\n    if (value === \"\".concat(parseInt(value))) {\n      value = parseInt(value);\n    }\n    if (value || parseInt(value) === 0) {\n      elem.style.setProperty(property, typeof value === 'number' ? \"\".concat(value, \"px\") : value);\n    } else {\n      elem.style.removeProperty(property);\n    }\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  var show = function show(elem) {\n    var display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'flex';\n    elem && (elem.style.display = display);\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   */\n  var hide = function hide(elem) {\n    elem && (elem.style.display = 'none');\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  var showWhenInnerHtmlPresent = function showWhenInnerHtmlPresent(elem) {\n    var display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'block';\n    if (!elem) {\n      return;\n    }\n    new MutationObserver(function () {\n      toggle(elem, elem.innerHTML, display);\n    }).observe(elem, {\n      childList: true,\n      subtree: true\n    });\n  };\n\n  /**\n   * @param {HTMLElement} parent\n   * @param {string} selector\n   * @param {string} property\n   * @param {string} value\n   */\n  var setStyle = function setStyle(parent, selector, property, value) {\n    /** @type {HTMLElement | null} */\n    var el = parent.querySelector(selector);\n    if (el) {\n      el.style.setProperty(property, value);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {any} condition\n   * @param {string} display\n   */\n  var toggle = function toggle(elem, condition) {\n    var display = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'flex';\n    condition ? show(elem, display) : hide(elem);\n  };\n\n  /**\n   * borrowed from jquery $(elem).is(':visible') implementation\n   *\n   * @param {HTMLElement | null} elem\n   * @returns {boolean}\n   */\n  var isVisible$1 = function isVisible(elem) {\n    return !!(elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length));\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  var allButtonsAreHidden = function allButtonsAreHidden() {\n    return !isVisible$1(getConfirmButton()) && !isVisible$1(getDenyButton()) && !isVisible$1(getCancelButton());\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  var isScrollable = function isScrollable(elem) {\n    return !!(elem.scrollHeight > elem.clientHeight);\n  };\n\n  /**\n   * borrowed from https://stackoverflow.com/a/46352119\n   *\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  var hasCssAnimation = function hasCssAnimation(elem) {\n    var style = window.getComputedStyle(elem);\n    var animDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');\n    var transDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');\n    return animDuration > 0 || transDuration > 0;\n  };\n\n  /**\n   * @param {number} timer\n   * @param {boolean} reset\n   */\n  var animateTimerProgressBar = function animateTimerProgressBar(timer) {\n    var reset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    if (isVisible$1(timerProgressBar)) {\n      if (reset) {\n        timerProgressBar.style.transition = 'none';\n        timerProgressBar.style.width = '100%';\n      }\n      setTimeout(function () {\n        timerProgressBar.style.transition = \"width \".concat(timer / 1000, \"s linear\");\n        timerProgressBar.style.width = '0%';\n      }, 10);\n    }\n  };\n  var stopTimerProgressBar = function stopTimerProgressBar() {\n    var timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    var timerProgressBarWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    timerProgressBar.style.removeProperty('transition');\n    timerProgressBar.style.width = '100%';\n    var timerProgressBarFullWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    var timerProgressBarPercent = timerProgressBarWidth / timerProgressBarFullWidth * 100;\n    timerProgressBar.style.width = \"\".concat(timerProgressBarPercent, \"%\");\n  };\n\n  /**\n   * Detect Node env\n   *\n   * @returns {boolean}\n   */\n  var isNodeEnv = function isNodeEnv() {\n    return typeof window === 'undefined' || typeof document === 'undefined';\n  };\n\n  var sweetHTML = \"\\n <div aria-labelledby=\\\"\".concat(swalClasses.title, \"\\\" aria-describedby=\\\"\").concat(swalClasses['html-container'], \"\\\" class=\\\"\").concat(swalClasses.popup, \"\\\" tabindex=\\\"-1\\\">\\n   <button type=\\\"button\\\" class=\\\"\").concat(swalClasses.close, \"\\\"></button>\\n   <ul class=\\\"\").concat(swalClasses['progress-steps'], \"\\\"></ul>\\n   <div class=\\\"\").concat(swalClasses.icon, \"\\\"></div>\\n   <img class=\\\"\").concat(swalClasses.image, \"\\\" />\\n   <h2 class=\\\"\").concat(swalClasses.title, \"\\\" id=\\\"\").concat(swalClasses.title, \"\\\"></h2>\\n   <div class=\\\"\").concat(swalClasses['html-container'], \"\\\" id=\\\"\").concat(swalClasses['html-container'], \"\\\"></div>\\n   <input class=\\\"\").concat(swalClasses.input, \"\\\" id=\\\"\").concat(swalClasses.input, \"\\\" />\\n   <input type=\\\"file\\\" class=\\\"\").concat(swalClasses.file, \"\\\" />\\n   <div class=\\\"\").concat(swalClasses.range, \"\\\">\\n     <input type=\\\"range\\\" />\\n     <output></output>\\n   </div>\\n   <select class=\\\"\").concat(swalClasses.select, \"\\\" id=\\\"\").concat(swalClasses.select, \"\\\"></select>\\n   <div class=\\\"\").concat(swalClasses.radio, \"\\\"></div>\\n   <label class=\\\"\").concat(swalClasses.checkbox, \"\\\">\\n     <input type=\\\"checkbox\\\" id=\\\"\").concat(swalClasses.checkbox, \"\\\" />\\n     <span class=\\\"\").concat(swalClasses.label, \"\\\"></span>\\n   </label>\\n   <textarea class=\\\"\").concat(swalClasses.textarea, \"\\\" id=\\\"\").concat(swalClasses.textarea, \"\\\"></textarea>\\n   <div class=\\\"\").concat(swalClasses['validation-message'], \"\\\" id=\\\"\").concat(swalClasses['validation-message'], \"\\\"></div>\\n   <div class=\\\"\").concat(swalClasses.actions, \"\\\">\\n     <div class=\\\"\").concat(swalClasses.loader, \"\\\"></div>\\n     <button type=\\\"button\\\" class=\\\"\").concat(swalClasses.confirm, \"\\\"></button>\\n     <button type=\\\"button\\\" class=\\\"\").concat(swalClasses.deny, \"\\\"></button>\\n     <button type=\\\"button\\\" class=\\\"\").concat(swalClasses.cancel, \"\\\"></button>\\n   </div>\\n   <div class=\\\"\").concat(swalClasses.footer, \"\\\"></div>\\n   <div class=\\\"\").concat(swalClasses['timer-progress-bar-container'], \"\\\">\\n     <div class=\\\"\").concat(swalClasses['timer-progress-bar'], \"\\\"></div>\\n   </div>\\n </div>\\n\").replace(/(^|\\n)\\s*/g, '');\n\n  /**\n   * @returns {boolean}\n   */\n  var resetOldContainer = function resetOldContainer() {\n    var oldContainer = getContainer();\n    if (!oldContainer) {\n      return false;\n    }\n    oldContainer.remove();\n    removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['toast-shown'], swalClasses['has-column']]);\n    return true;\n  };\n  var resetValidationMessage$1 = function resetValidationMessage() {\n    globalState.currentInstance.resetValidationMessage();\n  };\n  var addInputChangeListeners = function addInputChangeListeners() {\n    var popup = getPopup();\n    var input = getDirectChildByClass(popup, swalClasses.input);\n    var file = getDirectChildByClass(popup, swalClasses.file);\n    /** @type {HTMLInputElement} */\n    var range = popup.querySelector(\".\".concat(swalClasses.range, \" input\"));\n    /** @type {HTMLOutputElement} */\n    var rangeOutput = popup.querySelector(\".\".concat(swalClasses.range, \" output\"));\n    var select = getDirectChildByClass(popup, swalClasses.select);\n    /** @type {HTMLInputElement} */\n    var checkbox = popup.querySelector(\".\".concat(swalClasses.checkbox, \" input\"));\n    var textarea = getDirectChildByClass(popup, swalClasses.textarea);\n    input.oninput = resetValidationMessage$1;\n    file.onchange = resetValidationMessage$1;\n    select.onchange = resetValidationMessage$1;\n    checkbox.onchange = resetValidationMessage$1;\n    textarea.oninput = resetValidationMessage$1;\n    range.oninput = function () {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n    range.onchange = function () {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n  };\n\n  /**\n   * @param {string | HTMLElement} target\n   * @returns {HTMLElement}\n   */\n  var getTarget = function getTarget(target) {\n    return typeof target === 'string' ? document.querySelector(target) : target;\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  var setupAccessibility = function setupAccessibility(params) {\n    var popup = getPopup();\n    popup.setAttribute('role', params.toast ? 'alert' : 'dialog');\n    popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');\n    if (!params.toast) {\n      popup.setAttribute('aria-modal', 'true');\n    }\n  };\n\n  /**\n   * @param {HTMLElement} targetElement\n   */\n  var setupRTL = function setupRTL(targetElement) {\n    if (window.getComputedStyle(targetElement).direction === 'rtl') {\n      addClass(getContainer(), swalClasses.rtl);\n    }\n  };\n\n  /**\n   * Add modal + backdrop + no-war message for Russians to DOM\n   *\n   * @param {SweetAlertOptions} params\n   */\n  var init = function init(params) {\n    // Clean up the old popup container if it exists\n    var oldContainerExisted = resetOldContainer();\n    if (isNodeEnv()) {\n      error('SweetAlert2 requires document to initialize');\n      return;\n    }\n    var container = document.createElement('div');\n    container.className = swalClasses.container;\n    if (oldContainerExisted) {\n      addClass(container, swalClasses['no-transition']);\n    }\n    setInnerHtml(container, sweetHTML);\n    var targetElement = getTarget(params.target);\n    targetElement.appendChild(container);\n    setupAccessibility(params);\n    setupRTL(targetElement);\n    addInputChangeListeners();\n  };\n\n  /**\n   * @param {HTMLElement | object | string} param\n   * @param {HTMLElement} target\n   */\n  var parseHtmlToContainer = function parseHtmlToContainer(param, target) {\n    // DOM element\n    if (param instanceof HTMLElement) {\n      target.appendChild(param);\n    }\n\n    // Object\n    else if (_typeof(param) === 'object') {\n      handleObject(param, target);\n    }\n\n    // Plain string\n    else if (param) {\n      setInnerHtml(target, param);\n    }\n  };\n\n  /**\n   * @param {any} param\n   * @param {HTMLElement} target\n   */\n  var handleObject = function handleObject(param, target) {\n    // JQuery element(s)\n    if (param.jquery) {\n      handleJqueryElem(target, param);\n    }\n\n    // For other objects use their string representation\n    else {\n      setInnerHtml(target, param.toString());\n    }\n  };\n\n  /**\n   * @param {HTMLElement} target\n   * @param {any} elem\n   */\n  var handleJqueryElem = function handleJqueryElem(target, elem) {\n    target.textContent = '';\n    if (0 in elem) {\n      for (var i = 0; (i in elem); i++) {\n        target.appendChild(elem[i].cloneNode(true));\n      }\n    } else {\n      target.appendChild(elem.cloneNode(true));\n    }\n  };\n\n  /**\n   * @returns {'webkitAnimationEnd' | 'animationend' | false}\n   */\n  var animationEndEvent = function () {\n    // Prevent run in Node env\n    if (isNodeEnv()) {\n      return false;\n    }\n    var testEl = document.createElement('div');\n\n    // Chrome, Safari and Opera\n    if (typeof testEl.style.webkitAnimation !== 'undefined') {\n      return 'webkitAnimationEnd';\n    }\n\n    // Standard syntax\n    if (typeof testEl.style.animation !== 'undefined') {\n      return 'animationend';\n    }\n    return false;\n  }();\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderActions = function renderActions(instance, params) {\n    var actions = getActions();\n    var loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n\n    // Actions (buttons) wrapper\n    if (!params.showConfirmButton && !params.showDenyButton && !params.showCancelButton) {\n      hide(actions);\n    } else {\n      show(actions);\n    }\n\n    // Custom class\n    applyCustomClass(actions, params, 'actions');\n\n    // Render all the buttons\n    renderButtons(actions, loader, params);\n\n    // Loader\n    setInnerHtml(loader, params.loaderHtml || '');\n    applyCustomClass(loader, params, 'loader');\n  };\n\n  /**\n   * @param {HTMLElement} actions\n   * @param {HTMLElement} loader\n   * @param {SweetAlertOptions} params\n   */\n  function renderButtons(actions, loader, params) {\n    var confirmButton = getConfirmButton();\n    var denyButton = getDenyButton();\n    var cancelButton = getCancelButton();\n    if (!confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n\n    // Render buttons\n    renderButton(confirmButton, 'confirm', params);\n    renderButton(denyButton, 'deny', params);\n    renderButton(cancelButton, 'cancel', params);\n    handleButtonsStyling(confirmButton, denyButton, cancelButton, params);\n    if (params.reverseButtons) {\n      if (params.toast) {\n        actions.insertBefore(cancelButton, confirmButton);\n        actions.insertBefore(denyButton, confirmButton);\n      } else {\n        actions.insertBefore(cancelButton, loader);\n        actions.insertBefore(denyButton, loader);\n        actions.insertBefore(confirmButton, loader);\n      }\n    }\n  }\n\n  /**\n   * @param {HTMLElement} confirmButton\n   * @param {HTMLElement} denyButton\n   * @param {HTMLElement} cancelButton\n   * @param {SweetAlertOptions} params\n   */\n  function handleButtonsStyling(confirmButton, denyButton, cancelButton, params) {\n    if (!params.buttonsStyling) {\n      removeClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n      return;\n    }\n    addClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n\n    // Buttons background colors\n    if (params.confirmButtonColor) {\n      confirmButton.style.backgroundColor = params.confirmButtonColor;\n      addClass(confirmButton, swalClasses['default-outline']);\n    }\n    if (params.denyButtonColor) {\n      denyButton.style.backgroundColor = params.denyButtonColor;\n      addClass(denyButton, swalClasses['default-outline']);\n    }\n    if (params.cancelButtonColor) {\n      cancelButton.style.backgroundColor = params.cancelButtonColor;\n      addClass(cancelButton, swalClasses['default-outline']);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} button\n   * @param {'confirm' | 'deny' | 'cancel'} buttonType\n   * @param {SweetAlertOptions} params\n   */\n  function renderButton(button, buttonType, params) {\n    var buttonName = /** @type {'Confirm' | 'Deny' | 'Cancel'} */capitalizeFirstLetter(buttonType);\n    toggle(button, params[\"show\".concat(buttonName, \"Button\")], 'inline-block');\n    setInnerHtml(button, params[\"\".concat(buttonType, \"ButtonText\")] || ''); // Set caption text\n    button.setAttribute('aria-label', params[\"\".concat(buttonType, \"ButtonAriaLabel\")] || ''); // ARIA label\n\n    // Add buttons custom classes\n    button.className = swalClasses[buttonType];\n    applyCustomClass(button, params, \"\".concat(buttonType, \"Button\"));\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderCloseButton = function renderCloseButton(instance, params) {\n    var closeButton = getCloseButton();\n    if (!closeButton) {\n      return;\n    }\n    setInnerHtml(closeButton, params.closeButtonHtml || '');\n\n    // Custom class\n    applyCustomClass(closeButton, params, 'closeButton');\n    toggle(closeButton, params.showCloseButton);\n    closeButton.setAttribute('aria-label', params.closeButtonAriaLabel || '');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderContainer = function renderContainer(instance, params) {\n    var container = getContainer();\n    if (!container) {\n      return;\n    }\n    handleBackdropParam(container, params.backdrop);\n    handlePositionParam(container, params.position);\n    handleGrowParam(container, params.grow);\n\n    // Custom class\n    applyCustomClass(container, params, 'container');\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['backdrop']} backdrop\n   */\n  function handleBackdropParam(container, backdrop) {\n    if (typeof backdrop === 'string') {\n      container.style.background = backdrop;\n    } else if (!backdrop) {\n      addClass([document.documentElement, document.body], swalClasses['no-backdrop']);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['position']} position\n   */\n  function handlePositionParam(container, position) {\n    if (!position) {\n      return;\n    }\n    if (position in swalClasses) {\n      addClass(container, swalClasses[position]);\n    } else {\n      warn('The \"position\" parameter is not valid, defaulting to \"center\"');\n      addClass(container, swalClasses.center);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['grow']} grow\n   */\n  function handleGrowParam(container, grow) {\n    if (!grow) {\n      return;\n    }\n    addClass(container, swalClasses[\"grow-\".concat(grow)]);\n  }\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateProps = {\n    innerParams: new WeakMap(),\n    domCache: new WeakMap()\n  };\n\n  /** @type {InputClass[]} */\n  var inputClasses = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderInput = function renderInput(instance, params) {\n    var popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    var innerParams = privateProps.innerParams.get(instance);\n    var rerender = !innerParams || params.input !== innerParams.input;\n    inputClasses.forEach(function (inputClass) {\n      var inputContainer = getDirectChildByClass(popup, swalClasses[inputClass]);\n      if (!inputContainer) {\n        return;\n      }\n\n      // set attributes\n      setAttributes(inputClass, params.inputAttributes);\n\n      // set class\n      inputContainer.className = swalClasses[inputClass];\n      if (rerender) {\n        hide(inputContainer);\n      }\n    });\n    if (params.input) {\n      if (rerender) {\n        showInput(params);\n      }\n      // set custom class\n      setCustomClass(params);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  var showInput = function showInput(params) {\n    if (!params.input) {\n      return;\n    }\n    if (!renderInputType[params.input]) {\n      error(\"Unexpected type of input! Expected \".concat(Object.keys(renderInputType).join(' | '), \", got \\\"\").concat(params.input, \"\\\"\"));\n      return;\n    }\n    var inputContainer = getInputContainer(params.input);\n    var input = renderInputType[params.input](inputContainer, params);\n    show(inputContainer);\n\n    // input autofocus\n    if (params.inputAutoFocus) {\n      setTimeout(function () {\n        focusInput(input);\n      });\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   */\n  var removeAttributes = function removeAttributes(input) {\n    for (var i = 0; i < input.attributes.length; i++) {\n      var attrName = input.attributes[i].name;\n      if (!['id', 'type', 'value', 'style'].includes(attrName)) {\n        input.removeAttribute(attrName);\n      }\n    }\n  };\n\n  /**\n   * @param {InputClass} inputClass\n   * @param {SweetAlertOptions['inputAttributes']} inputAttributes\n   */\n  var setAttributes = function setAttributes(inputClass, inputAttributes) {\n    var input = getInput$1(getPopup(), inputClass);\n    if (!input) {\n      return;\n    }\n    removeAttributes(input);\n    for (var attr in inputAttributes) {\n      input.setAttribute(attr, inputAttributes[attr]);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  var setCustomClass = function setCustomClass(params) {\n    var inputContainer = getInputContainer(params.input);\n    if (_typeof(params.customClass) === 'object') {\n      addClass(inputContainer, params.customClass.input);\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions} params\n   */\n  var setInputPlaceholder = function setInputPlaceholder(input, params) {\n    if (!input.placeholder || params.inputPlaceholder) {\n      input.placeholder = params.inputPlaceholder;\n    }\n  };\n\n  /**\n   * @param {Input} input\n   * @param {Input} prependTo\n   * @param {SweetAlertOptions} params\n   */\n  var setInputLabel = function setInputLabel(input, prependTo, params) {\n    if (params.inputLabel) {\n      var label = document.createElement('label');\n      var labelClass = swalClasses['input-label'];\n      label.setAttribute('for', input.id);\n      label.className = labelClass;\n      if (_typeof(params.customClass) === 'object') {\n        addClass(label, params.customClass.inputLabel);\n      }\n      label.innerText = params.inputLabel;\n      prependTo.insertAdjacentElement('beforebegin', label);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions['input']} inputType\n   * @returns {HTMLElement}\n   */\n  var getInputContainer = function getInputContainer(inputType) {\n    return getDirectChildByClass(getPopup(), swalClasses[inputType] || swalClasses.input);\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLOutputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions['inputValue']} inputValue\n   */\n  var checkAndSetInputValue = function checkAndSetInputValue(input, inputValue) {\n    if (['string', 'number'].includes(_typeof(inputValue))) {\n      input.value = \"\".concat(inputValue);\n    } else if (!isPromise(inputValue)) {\n      warn(\"Unexpected type of inputValue! Expected \\\"string\\\", \\\"number\\\" or \\\"Promise\\\", got \\\"\".concat(_typeof(inputValue), \"\\\"\"));\n    }\n  };\n\n  /** @type {Record<SweetAlertInput, (input: Input | HTMLElement, params: SweetAlertOptions) => Input>} */\n  var renderInputType = {};\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.text = renderInputType.email = renderInputType.password = renderInputType.number = renderInputType.tel = renderInputType.url = renderInputType.search = renderInputType.date = renderInputType['datetime-local'] = renderInputType.time = renderInputType.week = renderInputType.month = function (input, params) {\n    checkAndSetInputValue(input, params.inputValue);\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    input.type = params.input;\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.file = function (input, params) {\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} range\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.range = function (range, params) {\n    var rangeInput = range.querySelector('input');\n    var rangeOutput = range.querySelector('output');\n    checkAndSetInputValue(rangeInput, params.inputValue);\n    rangeInput.type = params.input;\n    checkAndSetInputValue(rangeOutput, params.inputValue);\n    setInputLabel(rangeInput, range, params);\n    return range;\n  };\n\n  /**\n   * @param {HTMLSelectElement} select\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLSelectElement}\n   */\n  renderInputType.select = function (select, params) {\n    select.textContent = '';\n    if (params.inputPlaceholder) {\n      var placeholder = document.createElement('option');\n      setInnerHtml(placeholder, params.inputPlaceholder);\n      placeholder.value = '';\n      placeholder.disabled = true;\n      placeholder.selected = true;\n      select.appendChild(placeholder);\n    }\n    setInputLabel(select, select, params);\n    return select;\n  };\n\n  /**\n   * @param {HTMLInputElement} radio\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.radio = function (radio) {\n    radio.textContent = '';\n    return radio;\n  };\n\n  /**\n   * @param {HTMLLabelElement} checkboxContainer\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.checkbox = function (checkboxContainer, params) {\n    var checkbox = getInput$1(getPopup(), 'checkbox');\n    checkbox.value = '1';\n    checkbox.checked = Boolean(params.inputValue);\n    var label = checkboxContainer.querySelector('span');\n    setInnerHtml(label, params.inputPlaceholder);\n    return checkbox;\n  };\n\n  /**\n   * @param {HTMLTextAreaElement} textarea\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLTextAreaElement}\n   */\n  renderInputType.textarea = function (textarea, params) {\n    checkAndSetInputValue(textarea, params.inputValue);\n    setInputPlaceholder(textarea, params);\n    setInputLabel(textarea, textarea, params);\n\n    /**\n     * @param {HTMLElement} el\n     * @returns {number}\n     */\n    var getMargin = function getMargin(el) {\n      return parseInt(window.getComputedStyle(el).marginLeft) + parseInt(window.getComputedStyle(el).marginRight);\n    };\n\n    // https://github.com/sweetalert2/sweetalert2/issues/2291\n    setTimeout(function () {\n      // https://github.com/sweetalert2/sweetalert2/issues/1699\n      if ('MutationObserver' in window) {\n        var initialPopupWidth = parseInt(window.getComputedStyle(getPopup()).width);\n        var textareaResizeHandler = function textareaResizeHandler() {\n          // check if texarea is still in document (i.e. popup wasn't closed in the meantime)\n          if (!document.body.contains(textarea)) {\n            return;\n          }\n          var textareaWidth = textarea.offsetWidth + getMargin(textarea);\n          if (textareaWidth > initialPopupWidth) {\n            getPopup().style.width = \"\".concat(textareaWidth, \"px\");\n          } else {\n            applyNumericalStyle(getPopup(), 'width', params.width);\n          }\n        };\n        new MutationObserver(textareaResizeHandler).observe(textarea, {\n          attributes: true,\n          attributeFilter: ['style']\n        });\n      }\n    });\n    return textarea;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderContent = function renderContent(instance, params) {\n    var htmlContainer = getHtmlContainer();\n    if (!htmlContainer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(htmlContainer);\n    applyCustomClass(htmlContainer, params, 'htmlContainer');\n\n    // Content as HTML\n    if (params.html) {\n      parseHtmlToContainer(params.html, htmlContainer);\n      show(htmlContainer, 'block');\n    }\n\n    // Content as plain text\n    else if (params.text) {\n      htmlContainer.textContent = params.text;\n      show(htmlContainer, 'block');\n    }\n\n    // No content\n    else {\n      hide(htmlContainer);\n    }\n    renderInput(instance, params);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderFooter = function renderFooter(instance, params) {\n    var footer = getFooter();\n    if (!footer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(footer);\n    toggle(footer, params.footer, 'block');\n    if (params.footer) {\n      parseHtmlToContainer(params.footer, footer);\n    }\n\n    // Custom class\n    applyCustomClass(footer, params, 'footer');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderIcon = function renderIcon(instance, params) {\n    var innerParams = privateProps.innerParams.get(instance);\n    var icon = getIcon();\n    if (!icon) {\n      return;\n    }\n\n    // if the given icon already rendered, apply the styling without re-rendering the icon\n    if (innerParams && params.icon === innerParams.icon) {\n      // Custom or default content\n      setContent(icon, params);\n      applyStyles(icon, params);\n      return;\n    }\n    if (!params.icon && !params.iconHtml) {\n      hide(icon);\n      return;\n    }\n    if (params.icon && Object.keys(iconTypes).indexOf(params.icon) === -1) {\n      error(\"Unknown icon! Expected \\\"success\\\", \\\"error\\\", \\\"warning\\\", \\\"info\\\" or \\\"question\\\", got \\\"\".concat(params.icon, \"\\\"\"));\n      hide(icon);\n      return;\n    }\n    show(icon);\n\n    // Custom or default content\n    setContent(icon, params);\n    applyStyles(icon, params);\n\n    // Animate icon\n    addClass(icon, params.showClass && params.showClass.icon);\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  var applyStyles = function applyStyles(icon, params) {\n    for (var _i = 0, _Object$entries = Object.entries(iconTypes); _i < _Object$entries.length; _i++) {\n      var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 2),\n        iconType = _Object$entries$_i[0],\n        iconClassName = _Object$entries$_i[1];\n      if (params.icon !== iconType) {\n        removeClass(icon, iconClassName);\n      }\n    }\n    addClass(icon, params.icon && iconTypes[params.icon]);\n\n    // Icon color\n    setColor(icon, params);\n\n    // Success icon background color\n    adjustSuccessIconBackgroundColor();\n\n    // Custom class\n    applyCustomClass(icon, params, 'icon');\n  };\n\n  // Adjust success icon background color to match the popup background color\n  var adjustSuccessIconBackgroundColor = function adjustSuccessIconBackgroundColor() {\n    var popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    var popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');\n    /** @type {NodeListOf<HTMLElement>} */\n    var successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');\n    for (var i = 0; i < successIconParts.length; i++) {\n      successIconParts[i].style.backgroundColor = popupBackgroundColor;\n    }\n  };\n  var successIconHtml = \"\\n  <div class=\\\"swal2-success-circular-line-left\\\"></div>\\n  <span class=\\\"swal2-success-line-tip\\\"></span> <span class=\\\"swal2-success-line-long\\\"></span>\\n  <div class=\\\"swal2-success-ring\\\"></div> <div class=\\\"swal2-success-fix\\\"></div>\\n  <div class=\\\"swal2-success-circular-line-right\\\"></div>\\n\";\n  var errorIconHtml = \"\\n  <span class=\\\"swal2-x-mark\\\">\\n    <span class=\\\"swal2-x-mark-line-left\\\"></span>\\n    <span class=\\\"swal2-x-mark-line-right\\\"></span>\\n  </span>\\n\";\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  var setContent = function setContent(icon, params) {\n    if (!params.icon && !params.iconHtml) {\n      return;\n    }\n    var oldContent = icon.innerHTML;\n    var newContent = '';\n    if (params.iconHtml) {\n      newContent = iconContent(params.iconHtml);\n    } else if (params.icon === 'success') {\n      newContent = successIconHtml;\n      oldContent = oldContent.replace(/ style=\".*?\"/g, ''); // undo adjustSuccessIconBackgroundColor()\n    } else if (params.icon === 'error') {\n      newContent = errorIconHtml;\n    } else if (params.icon) {\n      var defaultIconHtml = {\n        question: '?',\n        warning: '!',\n        info: 'i'\n      };\n      newContent = iconContent(defaultIconHtml[params.icon]);\n    }\n    if (oldContent.trim() !== newContent.trim()) {\n      setInnerHtml(icon, newContent);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  var setColor = function setColor(icon, params) {\n    if (!params.iconColor) {\n      return;\n    }\n    icon.style.color = params.iconColor;\n    icon.style.borderColor = params.iconColor;\n    for (var _i2 = 0, _arr = ['.swal2-success-line-tip', '.swal2-success-line-long', '.swal2-x-mark-line-left', '.swal2-x-mark-line-right']; _i2 < _arr.length; _i2++) {\n      var sel = _arr[_i2];\n      setStyle(icon, sel, 'background-color', params.iconColor);\n    }\n    setStyle(icon, '.swal2-success-ring', 'border-color', params.iconColor);\n  };\n\n  /**\n   * @param {string} content\n   * @returns {string}\n   */\n  var iconContent = function iconContent(content) {\n    return \"<div class=\\\"\".concat(swalClasses['icon-content'], \"\\\">\").concat(content, \"</div>\");\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderImage = function renderImage(instance, params) {\n    var image = getImage();\n    if (!image) {\n      return;\n    }\n    if (!params.imageUrl) {\n      hide(image);\n      return;\n    }\n    show(image, '');\n\n    // Src, alt\n    image.setAttribute('src', params.imageUrl);\n    image.setAttribute('alt', params.imageAlt || '');\n\n    // Width, height\n    applyNumericalStyle(image, 'width', params.imageWidth);\n    applyNumericalStyle(image, 'height', params.imageHeight);\n\n    // Class\n    image.className = swalClasses.image;\n    applyCustomClass(image, params, 'image');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderPopup = function renderPopup(instance, params) {\n    var container = getContainer();\n    var popup = getPopup();\n    if (!container || !popup) {\n      return;\n    }\n\n    // Width\n    // https://github.com/sweetalert2/sweetalert2/issues/2170\n    if (params.toast) {\n      applyNumericalStyle(container, 'width', params.width);\n      popup.style.width = '100%';\n      var loader = getLoader();\n      loader && popup.insertBefore(loader, getIcon());\n    } else {\n      applyNumericalStyle(popup, 'width', params.width);\n    }\n\n    // Padding\n    applyNumericalStyle(popup, 'padding', params.padding);\n\n    // Color\n    if (params.color) {\n      popup.style.color = params.color;\n    }\n\n    // Background\n    if (params.background) {\n      popup.style.background = params.background;\n    }\n    hide(getValidationMessage());\n\n    // Classes\n    addClasses$1(popup, params);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  var addClasses$1 = function addClasses(popup, params) {\n    var showClass = params.showClass || {};\n    // Default Class + showClass when updating Swal.update({})\n    popup.className = \"\".concat(swalClasses.popup, \" \").concat(isVisible$1(popup) ? showClass.popup : '');\n    if (params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['toast-shown']);\n      addClass(popup, swalClasses.toast);\n    } else {\n      addClass(popup, swalClasses.modal);\n    }\n\n    // Custom class\n    applyCustomClass(popup, params, 'popup');\n    if (typeof params.customClass === 'string') {\n      addClass(popup, params.customClass);\n    }\n\n    // Icon class (#1842)\n    if (params.icon) {\n      addClass(popup, swalClasses[\"icon-\".concat(params.icon)]);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderProgressSteps = function renderProgressSteps(instance, params) {\n    var progressStepsContainer = getProgressSteps();\n    if (!progressStepsContainer) {\n      return;\n    }\n    var progressSteps = params.progressSteps,\n      currentProgressStep = params.currentProgressStep;\n    if (!progressSteps || progressSteps.length === 0 || currentProgressStep === undefined) {\n      hide(progressStepsContainer);\n      return;\n    }\n    show(progressStepsContainer);\n    progressStepsContainer.textContent = '';\n    if (currentProgressStep >= progressSteps.length) {\n      warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');\n    }\n    progressSteps.forEach(function (step, index) {\n      var stepEl = createStepElement(step);\n      progressStepsContainer.appendChild(stepEl);\n      if (index === currentProgressStep) {\n        addClass(stepEl, swalClasses['active-progress-step']);\n      }\n      if (index !== progressSteps.length - 1) {\n        var lineEl = createLineElement(params);\n        progressStepsContainer.appendChild(lineEl);\n      }\n    });\n  };\n\n  /**\n   * @param {string} step\n   * @returns {HTMLLIElement}\n   */\n  var createStepElement = function createStepElement(step) {\n    var stepEl = document.createElement('li');\n    addClass(stepEl, swalClasses['progress-step']);\n    setInnerHtml(stepEl, step);\n    return stepEl;\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLLIElement}\n   */\n  var createLineElement = function createLineElement(params) {\n    var lineEl = document.createElement('li');\n    addClass(lineEl, swalClasses['progress-step-line']);\n    if (params.progressStepsDistance) {\n      applyNumericalStyle(lineEl, 'width', params.progressStepsDistance);\n    }\n    return lineEl;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var renderTitle = function renderTitle(instance, params) {\n    var title = getTitle();\n    if (!title) {\n      return;\n    }\n    showWhenInnerHtmlPresent(title);\n    toggle(title, params.title || params.titleText, 'block');\n    if (params.title) {\n      parseHtmlToContainer(params.title, title);\n    }\n    if (params.titleText) {\n      title.innerText = params.titleText;\n    }\n\n    // Custom class\n    applyCustomClass(title, params, 'title');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var render = function render(instance, params) {\n    renderPopup(instance, params);\n    renderContainer(instance, params);\n    renderProgressSteps(instance, params);\n    renderIcon(instance, params);\n    renderImage(instance, params);\n    renderTitle(instance, params);\n    renderCloseButton(instance, params);\n    renderContent(instance, params);\n    renderActions(instance, params);\n    renderFooter(instance, params);\n    var popup = getPopup();\n    if (typeof params.didRender === 'function' && popup) {\n      params.didRender(popup);\n    }\n  };\n\n  /*\n   * Global function to determine if SweetAlert2 popup is shown\n   */\n  var isVisible = function isVisible() {\n    return isVisible$1(getPopup());\n  };\n\n  /*\n   * Global function to click 'Confirm' button\n   */\n  var clickConfirm = function clickConfirm() {\n    var _dom$getConfirmButton;\n    return (_dom$getConfirmButton = getConfirmButton()) === null || _dom$getConfirmButton === void 0 ? void 0 : _dom$getConfirmButton.click();\n  };\n\n  /*\n   * Global function to click 'Deny' button\n   */\n  var clickDeny = function clickDeny() {\n    var _dom$getDenyButton;\n    return (_dom$getDenyButton = getDenyButton()) === null || _dom$getDenyButton === void 0 ? void 0 : _dom$getDenyButton.click();\n  };\n\n  /*\n   * Global function to click 'Cancel' button\n   */\n  var clickCancel = function clickCancel() {\n    var _dom$getCancelButton;\n    return (_dom$getCancelButton = getCancelButton()) === null || _dom$getCancelButton === void 0 ? void 0 : _dom$getCancelButton.click();\n  };\n\n  /** @typedef {'cancel' | 'backdrop' | 'close' | 'esc' | 'timer'} DismissReason */\n\n  /** @type {Record<DismissReason, DismissReason>} */\n  var DismissReason = Object.freeze({\n    cancel: 'cancel',\n    backdrop: 'backdrop',\n    close: 'close',\n    esc: 'esc',\n    timer: 'timer'\n  });\n\n  /**\n   * @param {GlobalState} globalState\n   */\n  var removeKeydownHandler = function removeKeydownHandler(globalState) {\n    if (globalState.keydownTarget && globalState.keydownHandlerAdded) {\n      globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = false;\n    }\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {*} dismissWith\n   */\n  var addKeydownHandler = function addKeydownHandler(globalState, innerParams, dismissWith) {\n    removeKeydownHandler(globalState);\n    if (!innerParams.toast) {\n      globalState.keydownHandler = function (e) {\n        return keydownHandler(innerParams, e, dismissWith);\n      };\n      globalState.keydownTarget = innerParams.keydownListenerCapture ? window : getPopup();\n      globalState.keydownListenerCapture = innerParams.keydownListenerCapture;\n      globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = true;\n    }\n  };\n\n  /**\n   * @param {number} index\n   * @param {number} increment\n   */\n  var setFocus = function setFocus(index, increment) {\n    var _dom$getPopup;\n    var focusableElements = getFocusableElements();\n    // search for visible elements and select the next possible match\n    if (focusableElements.length) {\n      index = index + increment;\n\n      // rollover to first item\n      if (index === focusableElements.length) {\n        index = 0;\n\n        // go to last item\n      } else if (index === -1) {\n        index = focusableElements.length - 1;\n      }\n      focusableElements[index].focus();\n      return;\n    }\n    // no visible focusable elements, focus the popup\n    (_dom$getPopup = getPopup()) === null || _dom$getPopup === void 0 || _dom$getPopup.focus();\n  };\n  var arrowKeysNextButton = ['ArrowRight', 'ArrowDown'];\n  var arrowKeysPreviousButton = ['ArrowLeft', 'ArrowUp'];\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {KeyboardEvent} event\n   * @param {Function} dismissWith\n   */\n  var keydownHandler = function keydownHandler(innerParams, event, dismissWith) {\n    if (!innerParams) {\n      return; // This instance has already been destroyed\n    }\n\n    // Ignore keydown during IME composition\n    // https://developer.mozilla.org/en-US/docs/Web/API/Document/keydown_event#ignoring_keydown_during_ime_composition\n    // https://github.com/sweetalert2/sweetalert2/issues/720\n    // https://github.com/sweetalert2/sweetalert2/issues/2406\n    if (event.isComposing || event.keyCode === 229) {\n      return;\n    }\n    if (innerParams.stopKeydownPropagation) {\n      event.stopPropagation();\n    }\n\n    // ENTER\n    if (event.key === 'Enter') {\n      handleEnter(event, innerParams);\n    }\n\n    // TAB\n    else if (event.key === 'Tab') {\n      handleTab(event);\n    }\n\n    // ARROWS - switch focus between buttons\n    else if ([].concat(arrowKeysNextButton, arrowKeysPreviousButton).includes(event.key)) {\n      handleArrows(event.key);\n    }\n\n    // ESC\n    else if (event.key === 'Escape') {\n      handleEsc(event, innerParams, dismissWith);\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   */\n  var handleEnter = function handleEnter(event, innerParams) {\n    // https://github.com/sweetalert2/sweetalert2/issues/2386\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      return;\n    }\n    var input = getInput$1(getPopup(), innerParams.input);\n    if (event.target && input && event.target instanceof HTMLElement && event.target.outerHTML === input.outerHTML) {\n      if (['textarea', 'file'].includes(innerParams.input)) {\n        return; // do not submit\n      }\n      clickConfirm();\n      event.preventDefault();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   */\n  var handleTab = function handleTab(event) {\n    var targetElement = event.target;\n    var focusableElements = getFocusableElements();\n    var btnIndex = -1;\n    for (var i = 0; i < focusableElements.length; i++) {\n      if (targetElement === focusableElements[i]) {\n        btnIndex = i;\n        break;\n      }\n    }\n\n    // Cycle to the next button\n    if (!event.shiftKey) {\n      setFocus(btnIndex, 1);\n    }\n\n    // Cycle to the prev button\n    else {\n      setFocus(btnIndex, -1);\n    }\n    event.stopPropagation();\n    event.preventDefault();\n  };\n\n  /**\n   * @param {string} key\n   */\n  var handleArrows = function handleArrows(key) {\n    var actions = getActions();\n    var confirmButton = getConfirmButton();\n    var denyButton = getDenyButton();\n    var cancelButton = getCancelButton();\n    if (!actions || !confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n    /** @type HTMLElement[] */\n    var buttons = [confirmButton, denyButton, cancelButton];\n    if (document.activeElement instanceof HTMLElement && !buttons.includes(document.activeElement)) {\n      return;\n    }\n    var sibling = arrowKeysNextButton.includes(key) ? 'nextElementSibling' : 'previousElementSibling';\n    var buttonToFocus = document.activeElement;\n    if (!buttonToFocus) {\n      return;\n    }\n    for (var i = 0; i < actions.children.length; i++) {\n      buttonToFocus = buttonToFocus[sibling];\n      if (!buttonToFocus) {\n        return;\n      }\n      if (buttonToFocus instanceof HTMLButtonElement && isVisible$1(buttonToFocus)) {\n        break;\n      }\n    }\n    if (buttonToFocus instanceof HTMLButtonElement) {\n      buttonToFocus.focus();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  var handleEsc = function handleEsc(event, innerParams, dismissWith) {\n    if (callIfFunction(innerParams.allowEscapeKey)) {\n      event.preventDefault();\n      dismissWith(DismissReason.esc);\n    }\n  };\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateMethods = {\n    swalPromiseResolve: new WeakMap(),\n    swalPromiseReject: new WeakMap()\n  };\n\n  // From https://developer.paciellogroup.com/blog/2018/06/the-current-state-of-modal-dialog-accessibility/\n  // Adding aria-hidden=\"true\" to elements outside of the active modal dialog ensures that\n  // elements not within the active modal dialog will not be surfaced if a user opens a screen\n  // reader’s list of elements (headings, form controls, landmarks, etc.) in the document.\n\n  var setAriaHidden = function setAriaHidden() {\n    var bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(function (el) {\n      if (el === getContainer() || el.contains(getContainer())) {\n        return;\n      }\n      if (el.hasAttribute('aria-hidden')) {\n        el.setAttribute('data-previous-aria-hidden', el.getAttribute('aria-hidden') || '');\n      }\n      el.setAttribute('aria-hidden', 'true');\n    });\n  };\n  var unsetAriaHidden = function unsetAriaHidden() {\n    var bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(function (el) {\n      if (el.hasAttribute('data-previous-aria-hidden')) {\n        el.setAttribute('aria-hidden', el.getAttribute('data-previous-aria-hidden') || '');\n        el.removeAttribute('data-previous-aria-hidden');\n      } else {\n        el.removeAttribute('aria-hidden');\n      }\n    });\n  };\n\n  // @ts-ignore\n  var isSafariOrIOS = typeof window !== 'undefined' && !!window.GestureEvent; // true for Safari desktop + all iOS browsers https://stackoverflow.com/a/70585394\n\n  /**\n   * Fix iOS scrolling\n   * http://stackoverflow.com/q/39626302\n   */\n  var iOSfix = function iOSfix() {\n    if (isSafariOrIOS && !hasClass(document.body, swalClasses.iosfix)) {\n      var offset = document.body.scrollTop;\n      document.body.style.top = \"\".concat(offset * -1, \"px\");\n      addClass(document.body, swalClasses.iosfix);\n      lockBodyScroll();\n    }\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1246\n   */\n  var lockBodyScroll = function lockBodyScroll() {\n    var container = getContainer();\n    if (!container) {\n      return;\n    }\n    /** @type {boolean} */\n    var preventTouchMove;\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchstart = function (event) {\n      preventTouchMove = shouldPreventTouchMove(event);\n    };\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchmove = function (event) {\n      if (preventTouchMove) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    };\n  };\n\n  /**\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  var shouldPreventTouchMove = function shouldPreventTouchMove(event) {\n    var target = event.target;\n    var container = getContainer();\n    var htmlContainer = getHtmlContainer();\n    if (!container || !htmlContainer) {\n      return false;\n    }\n    if (isStylus(event) || isZoom(event)) {\n      return false;\n    }\n    if (target === container) {\n      return true;\n    }\n    if (!isScrollable(container) && target instanceof HTMLElement && target.tagName !== 'INPUT' &&\n    // #1603\n    target.tagName !== 'TEXTAREA' &&\n    // #2266\n    !(isScrollable(htmlContainer) &&\n    // #1944\n    htmlContainer.contains(target))) {\n      return true;\n    }\n    return false;\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1786\n   *\n   * @param {*} event\n   * @returns {boolean}\n   */\n  var isStylus = function isStylus(event) {\n    return event.touches && event.touches.length && event.touches[0].touchType === 'stylus';\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1891\n   *\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  var isZoom = function isZoom(event) {\n    return event.touches && event.touches.length > 1;\n  };\n  var undoIOSfix = function undoIOSfix() {\n    if (hasClass(document.body, swalClasses.iosfix)) {\n      var offset = parseInt(document.body.style.top, 10);\n      removeClass(document.body, swalClasses.iosfix);\n      document.body.style.top = '';\n      document.body.scrollTop = offset * -1;\n    }\n  };\n\n  /**\n   * Measure scrollbar width for padding body during modal show/hide\n   * https://github.com/twbs/bootstrap/blob/master/js/src/modal.js\n   *\n   * @returns {number}\n   */\n  var measureScrollbar = function measureScrollbar() {\n    var scrollDiv = document.createElement('div');\n    scrollDiv.className = swalClasses['scrollbar-measure'];\n    document.body.appendChild(scrollDiv);\n    var scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth;\n    document.body.removeChild(scrollDiv);\n    return scrollbarWidth;\n  };\n\n  /**\n   * Remember state in cases where opening and handling a modal will fiddle with it.\n   * @type {number | null}\n   */\n  var previousBodyPadding = null;\n\n  /**\n   * @param {string} initialBodyOverflow\n   */\n  var replaceScrollbarWithPadding = function replaceScrollbarWithPadding(initialBodyOverflow) {\n    // for queues, do not do this more than once\n    if (previousBodyPadding !== null) {\n      return;\n    }\n    // if the body has overflow\n    if (document.body.scrollHeight > window.innerHeight || initialBodyOverflow === 'scroll' // https://github.com/sweetalert2/sweetalert2/issues/2663\n    ) {\n      // add padding so the content doesn't shift after removal of scrollbar\n      previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));\n      document.body.style.paddingRight = \"\".concat(previousBodyPadding + measureScrollbar(), \"px\");\n    }\n  };\n  var undoReplaceScrollbarWithPadding = function undoReplaceScrollbarWithPadding() {\n    if (previousBodyPadding !== null) {\n      document.body.style.paddingRight = \"\".concat(previousBodyPadding, \"px\");\n      previousBodyPadding = null;\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  function removePopupAndResetState(instance, container, returnFocus, didClose) {\n    if (isToast()) {\n      triggerDidCloseAndDispose(instance, didClose);\n    } else {\n      restoreActiveElement(returnFocus).then(function () {\n        return triggerDidCloseAndDispose(instance, didClose);\n      });\n      removeKeydownHandler(globalState);\n    }\n\n    // workaround for https://github.com/sweetalert2/sweetalert2/issues/2088\n    // for some reason removing the container in Safari will scroll the document to bottom\n    if (isSafariOrIOS) {\n      container.setAttribute('style', 'display:none !important');\n      container.removeAttribute('class');\n      container.innerHTML = '';\n    } else {\n      container.remove();\n    }\n    if (isModal()) {\n      undoReplaceScrollbarWithPadding();\n      undoIOSfix();\n      unsetAriaHidden();\n    }\n    removeBodyClasses();\n  }\n\n  /**\n   * Remove SweetAlert2 classes from body\n   */\n  function removeBodyClasses() {\n    removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['toast-shown']]);\n  }\n\n  /**\n   * Instance method to close sweetAlert\n   *\n   * @param {any} resolveValue\n   */\n  function close(resolveValue) {\n    resolveValue = prepareResolveValue(resolveValue);\n    var swalPromiseResolve = privateMethods.swalPromiseResolve.get(this);\n    var didClose = triggerClosePopup(this);\n    if (this.isAwaitingPromise) {\n      // A swal awaiting for a promise (after a click on Confirm or Deny) cannot be dismissed anymore #2335\n      if (!resolveValue.isDismissed) {\n        handleAwaitingPromise(this);\n        swalPromiseResolve(resolveValue);\n      }\n    } else if (didClose) {\n      // Resolve Swal promise\n      swalPromiseResolve(resolveValue);\n    }\n  }\n  var triggerClosePopup = function triggerClosePopup(instance) {\n    var popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    var innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams || hasClass(popup, innerParams.hideClass.popup)) {\n      return false;\n    }\n    removeClass(popup, innerParams.showClass.popup);\n    addClass(popup, innerParams.hideClass.popup);\n    var backdrop = getContainer();\n    removeClass(backdrop, innerParams.showClass.backdrop);\n    addClass(backdrop, innerParams.hideClass.backdrop);\n    handlePopupAnimation(instance, popup, innerParams);\n    return true;\n  };\n\n  /**\n   * @param {any} error\n   */\n  function rejectPromise(error) {\n    var rejectPromise = privateMethods.swalPromiseReject.get(this);\n    handleAwaitingPromise(this);\n    if (rejectPromise) {\n      // Reject Swal promise\n      rejectPromise(error);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  var handleAwaitingPromise = function handleAwaitingPromise(instance) {\n    if (instance.isAwaitingPromise) {\n      delete instance.isAwaitingPromise;\n      // The instance might have been previously partly destroyed, we must resume the destroy process in this case #2335\n      if (!privateProps.innerParams.get(instance)) {\n        instance._destroy();\n      }\n    }\n  };\n\n  /**\n   * @param {any} resolveValue\n   * @returns {SweetAlertResult}\n   */\n  var prepareResolveValue = function prepareResolveValue(resolveValue) {\n    // When user calls Swal.close()\n    if (typeof resolveValue === 'undefined') {\n      return {\n        isConfirmed: false,\n        isDenied: false,\n        isDismissed: true\n      };\n    }\n    return Object.assign({\n      isConfirmed: false,\n      isDenied: false,\n      isDismissed: false\n    }, resolveValue);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} innerParams\n   */\n  var handlePopupAnimation = function handlePopupAnimation(instance, popup, innerParams) {\n    var container = getContainer();\n    // If animation is supported, animate\n    var animationIsSupported = animationEndEvent && hasCssAnimation(popup);\n    if (typeof innerParams.willClose === 'function') {\n      innerParams.willClose(popup);\n    }\n    if (animationIsSupported) {\n      animatePopup(instance, popup, container, innerParams.returnFocus, innerParams.didClose);\n    } else {\n      // Otherwise, remove immediately\n      removePopupAndResetState(instance, container, innerParams.returnFocus, innerParams.didClose);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  var animatePopup = function animatePopup(instance, popup, container, returnFocus, didClose) {\n    if (!animationEndEvent) {\n      return;\n    }\n    globalState.swalCloseEventFinishedCallback = removePopupAndResetState.bind(null, instance, container, returnFocus, didClose);\n    popup.addEventListener(animationEndEvent, function (e) {\n      if (e.target === popup) {\n        globalState.swalCloseEventFinishedCallback();\n        delete globalState.swalCloseEventFinishedCallback;\n      }\n    });\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} didClose\n   */\n  var triggerDidCloseAndDispose = function triggerDidCloseAndDispose(instance, didClose) {\n    setTimeout(function () {\n      if (typeof didClose === 'function') {\n        didClose.bind(instance.params)();\n      }\n      // instance might have been destroyed already\n      if (instance._destroy) {\n        instance._destroy();\n      }\n    });\n  };\n\n  /**\n   * Shows loader (spinner), this is useful with AJAX requests.\n   * By default the loader be shown instead of the \"Confirm\" button.\n   *\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  var showLoading = function showLoading(buttonToReplace) {\n    var popup = getPopup();\n    if (!popup) {\n      new Swal(); // eslint-disable-line no-new\n    }\n    popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    var loader = getLoader();\n    if (isToast()) {\n      hide(getIcon());\n    } else {\n      replaceButton(popup, buttonToReplace);\n    }\n    show(loader);\n    popup.setAttribute('data-loading', 'true');\n    popup.setAttribute('aria-busy', 'true');\n    popup.focus();\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  var replaceButton = function replaceButton(popup, buttonToReplace) {\n    var actions = getActions();\n    var loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n    if (!buttonToReplace && isVisible$1(getConfirmButton())) {\n      buttonToReplace = getConfirmButton();\n    }\n    show(actions);\n    if (buttonToReplace) {\n      hide(buttonToReplace);\n      loader.setAttribute('data-button-to-replace', buttonToReplace.className);\n      actions.insertBefore(loader, buttonToReplace);\n    }\n    addClass([popup, actions], swalClasses.loading);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var handleInputOptionsAndValue = function handleInputOptionsAndValue(instance, params) {\n    if (params.input === 'select' || params.input === 'radio') {\n      handleInputOptions(instance, params);\n    } else if (['text', 'email', 'number', 'tel', 'textarea'].some(function (i) {\n      return i === params.input;\n    }) && (hasToPromiseFn(params.inputValue) || isPromise(params.inputValue))) {\n      showLoading(getConfirmButton());\n      handleInputValue(instance, params);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} innerParams\n   * @returns {SweetAlertInputValue}\n   */\n  var getInputValue = function getInputValue(instance, innerParams) {\n    var input = instance.getInput();\n    if (!input) {\n      return null;\n    }\n    switch (innerParams.input) {\n      case 'checkbox':\n        return getCheckboxValue(input);\n      case 'radio':\n        return getRadioValue(input);\n      case 'file':\n        return getFileValue(input);\n      default:\n        return innerParams.inputAutoTrim ? input.value.trim() : input.value;\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {number}\n   */\n  var getCheckboxValue = function getCheckboxValue(input) {\n    return input.checked ? 1 : 0;\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {string | null}\n   */\n  var getRadioValue = function getRadioValue(input) {\n    return input.checked ? input.value : null;\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {FileList | File | null}\n   */\n  var getFileValue = function getFileValue(input) {\n    return input.files && input.files.length ? input.getAttribute('multiple') !== null ? input.files : input.files[0] : null;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var handleInputOptions = function handleInputOptions(instance, params) {\n    var popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    /**\n     * @param {Record<string, any>} inputOptions\n     */\n    var processInputOptions = function processInputOptions(inputOptions) {\n      if (params.input === 'select') {\n        populateSelectOptions(popup, formatInputOptions(inputOptions), params);\n      } else if (params.input === 'radio') {\n        populateRadioOptions(popup, formatInputOptions(inputOptions), params);\n      }\n    };\n    if (hasToPromiseFn(params.inputOptions) || isPromise(params.inputOptions)) {\n      showLoading(getConfirmButton());\n      asPromise(params.inputOptions).then(function (inputOptions) {\n        instance.hideLoading();\n        processInputOptions(inputOptions);\n      });\n    } else if (_typeof(params.inputOptions) === 'object') {\n      processInputOptions(params.inputOptions);\n    } else {\n      error(\"Unexpected type of inputOptions! Expected object, Map or Promise, got \".concat(_typeof(params.inputOptions)));\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  var handleInputValue = function handleInputValue(instance, params) {\n    var input = instance.getInput();\n    if (!input) {\n      return;\n    }\n    hide(input);\n    asPromise(params.inputValue).then(function (inputValue) {\n      input.value = params.input === 'number' ? \"\".concat(parseFloat(inputValue) || 0) : \"\".concat(inputValue);\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    })[\"catch\"](function (err) {\n      error(\"Error in inputValue promise: \".concat(err));\n      input.value = '';\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    });\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateSelectOptions(popup, inputOptions, params) {\n    var select = getDirectChildByClass(popup, swalClasses.select);\n    if (!select) {\n      return;\n    }\n    /**\n     * @param {HTMLElement} parent\n     * @param {string} optionLabel\n     * @param {string} optionValue\n     */\n    var renderOption = function renderOption(parent, optionLabel, optionValue) {\n      var option = document.createElement('option');\n      option.value = optionValue;\n      setInnerHtml(option, optionLabel);\n      option.selected = isSelected(optionValue, params.inputValue);\n      parent.appendChild(option);\n    };\n    inputOptions.forEach(function (inputOption) {\n      var optionValue = inputOption[0];\n      var optionLabel = inputOption[1];\n      // <optgroup> spec:\n      // https://www.w3.org/TR/html401/interact/forms.html#h-17.6\n      // \"...all OPTGROUP elements must be specified directly within a SELECT element (i.e., groups may not be nested)...\"\n      // check whether this is a <optgroup>\n      if (Array.isArray(optionLabel)) {\n        // if it is an array, then it is an <optgroup>\n        var optgroup = document.createElement('optgroup');\n        optgroup.label = optionValue;\n        optgroup.disabled = false; // not configurable for now\n        select.appendChild(optgroup);\n        optionLabel.forEach(function (o) {\n          return renderOption(optgroup, o[1], o[0]);\n        });\n      } else {\n        // case of <option>\n        renderOption(select, optionLabel, optionValue);\n      }\n    });\n    select.focus();\n  }\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateRadioOptions(popup, inputOptions, params) {\n    var radio = getDirectChildByClass(popup, swalClasses.radio);\n    if (!radio) {\n      return;\n    }\n    inputOptions.forEach(function (inputOption) {\n      var radioValue = inputOption[0];\n      var radioLabel = inputOption[1];\n      var radioInput = document.createElement('input');\n      var radioLabelElement = document.createElement('label');\n      radioInput.type = 'radio';\n      radioInput.name = swalClasses.radio;\n      radioInput.value = radioValue;\n      if (isSelected(radioValue, params.inputValue)) {\n        radioInput.checked = true;\n      }\n      var label = document.createElement('span');\n      setInnerHtml(label, radioLabel);\n      label.className = swalClasses.label;\n      radioLabelElement.appendChild(radioInput);\n      radioLabelElement.appendChild(label);\n      radio.appendChild(radioLabelElement);\n    });\n    var radios = radio.querySelectorAll('input');\n    if (radios.length) {\n      radios[0].focus();\n    }\n  }\n\n  /**\n   * Converts `inputOptions` into an array of `[value, label]`s\n   *\n   * @param {Record<string, any>} inputOptions\n   * @typedef {string[]} InputOptionFlattened\n   * @returns {InputOptionFlattened[]}\n   */\n  var formatInputOptions = function formatInputOptions(inputOptions) {\n    /** @type {InputOptionFlattened[]} */\n    var result = [];\n    if (inputOptions instanceof Map) {\n      inputOptions.forEach(function (value, key) {\n        var valueFormatted = value;\n        if (_typeof(valueFormatted) === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    } else {\n      Object.keys(inputOptions).forEach(function (key) {\n        var valueFormatted = inputOptions[key];\n        if (_typeof(valueFormatted) === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {string} optionValue\n   * @param {SweetAlertInputValue} inputValue\n   * @returns {boolean}\n   */\n  var isSelected = function isSelected(optionValue, inputValue) {\n    return !!inputValue && inputValue.toString() === optionValue.toString();\n  };\n\n  var _this = undefined;\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  var handleConfirmButtonClick = function handleConfirmButtonClick(instance) {\n    var innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.input) {\n      handleConfirmOrDenyWithInput(instance, 'confirm');\n    } else {\n      confirm(instance, true);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  var handleDenyButtonClick = function handleDenyButtonClick(instance) {\n    var innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.returnInputValueOnDeny) {\n      handleConfirmOrDenyWithInput(instance, 'deny');\n    } else {\n      deny(instance, false);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} dismissWith\n   */\n  var handleCancelButtonClick = function handleCancelButtonClick(instance, dismissWith) {\n    instance.disableButtons();\n    dismissWith(DismissReason.cancel);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {'confirm' | 'deny'} type\n   */\n  var handleConfirmOrDenyWithInput = function handleConfirmOrDenyWithInput(instance, type) {\n    var innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams.input) {\n      error(\"The \\\"input\\\" parameter is needed to be set when using returnInputValueOn\".concat(capitalizeFirstLetter(type)));\n      return;\n    }\n    var input = instance.getInput();\n    var inputValue = getInputValue(instance, innerParams);\n    if (innerParams.inputValidator) {\n      handleInputValidator(instance, inputValue, type);\n    } else if (input && !input.checkValidity()) {\n      instance.enableButtons();\n      instance.showValidationMessage(innerParams.validationMessage);\n    } else if (type === 'deny') {\n      deny(instance, inputValue);\n    } else {\n      confirm(instance, inputValue);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertInputValue} inputValue\n   * @param {'confirm' | 'deny'} type\n   */\n  var handleInputValidator = function handleInputValidator(instance, inputValue, type) {\n    var innerParams = privateProps.innerParams.get(instance);\n    instance.disableInput();\n    var validationPromise = Promise.resolve().then(function () {\n      return asPromise(innerParams.inputValidator(inputValue, innerParams.validationMessage));\n    });\n    validationPromise.then(function (validationMessage) {\n      instance.enableButtons();\n      instance.enableInput();\n      if (validationMessage) {\n        instance.showValidationMessage(validationMessage);\n      } else if (type === 'deny') {\n        deny(instance, inputValue);\n      } else {\n        confirm(instance, inputValue);\n      }\n    });\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  var deny = function deny(instance, value) {\n    var innerParams = privateProps.innerParams.get(instance || _this);\n    if (innerParams.showLoaderOnDeny) {\n      showLoading(getDenyButton());\n    }\n    if (innerParams.preDeny) {\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preDeny's promise is received\n      var preDenyPromise = Promise.resolve().then(function () {\n        return asPromise(innerParams.preDeny(value, innerParams.validationMessage));\n      });\n      preDenyPromise.then(function (preDenyValue) {\n        if (preDenyValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          instance.close({\n            isDenied: true,\n            value: typeof preDenyValue === 'undefined' ? value : preDenyValue\n          });\n        }\n      })[\"catch\"](function (error) {\n        return rejectWith(instance || _this, error);\n      });\n    } else {\n      instance.close({\n        isDenied: true,\n        value: value\n      });\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  var succeedWith = function succeedWith(instance, value) {\n    instance.close({\n      isConfirmed: true,\n      value: value\n    });\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {string} error\n   */\n  var rejectWith = function rejectWith(instance, error) {\n    instance.rejectPromise(error);\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  var confirm = function confirm(instance, value) {\n    var innerParams = privateProps.innerParams.get(instance || _this);\n    if (innerParams.showLoaderOnConfirm) {\n      showLoading();\n    }\n    if (innerParams.preConfirm) {\n      instance.resetValidationMessage();\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preConfirm's promise is received\n      var preConfirmPromise = Promise.resolve().then(function () {\n        return asPromise(innerParams.preConfirm(value, innerParams.validationMessage));\n      });\n      preConfirmPromise.then(function (preConfirmValue) {\n        if (isVisible$1(getValidationMessage()) || preConfirmValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          succeedWith(instance, typeof preConfirmValue === 'undefined' ? value : preConfirmValue);\n        }\n      })[\"catch\"](function (error) {\n        return rejectWith(instance || _this, error);\n      });\n    } else {\n      succeedWith(instance, value);\n    }\n  };\n\n  /**\n   * Hides loader and shows back the button which was hidden by .showLoading()\n   */\n  function hideLoading() {\n    // do nothing if popup is closed\n    var innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      return;\n    }\n    var domCache = privateProps.domCache.get(this);\n    hide(domCache.loader);\n    if (isToast()) {\n      if (innerParams.icon) {\n        show(getIcon());\n      }\n    } else {\n      showRelatedButton(domCache);\n    }\n    removeClass([domCache.popup, domCache.actions], swalClasses.loading);\n    domCache.popup.removeAttribute('aria-busy');\n    domCache.popup.removeAttribute('data-loading');\n    domCache.confirmButton.disabled = false;\n    domCache.denyButton.disabled = false;\n    domCache.cancelButton.disabled = false;\n  }\n  var showRelatedButton = function showRelatedButton(domCache) {\n    var buttonToReplace = domCache.popup.getElementsByClassName(domCache.loader.getAttribute('data-button-to-replace'));\n    if (buttonToReplace.length) {\n      show(buttonToReplace[0], 'inline-block');\n    } else if (allButtonsAreHidden()) {\n      hide(domCache.actions);\n    }\n  };\n\n  /**\n   * Gets the input DOM node, this method works with input parameter.\n   *\n   * @returns {HTMLInputElement | null}\n   */\n  function getInput() {\n    var innerParams = privateProps.innerParams.get(this);\n    var domCache = privateProps.domCache.get(this);\n    if (!domCache) {\n      return null;\n    }\n    return getInput$1(domCache.popup, innerParams.input);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {string[]} buttons\n   * @param {boolean} disabled\n   */\n  function setButtonsDisabled(instance, buttons, disabled) {\n    var domCache = privateProps.domCache.get(instance);\n    buttons.forEach(function (button) {\n      domCache[button].disabled = disabled;\n    });\n  }\n\n  /**\n   * @param {HTMLInputElement | null} input\n   * @param {boolean} disabled\n   */\n  function setInputDisabled(input, disabled) {\n    var popup = getPopup();\n    if (!popup || !input) {\n      return;\n    }\n    if (input.type === 'radio') {\n      /** @type {NodeListOf<HTMLInputElement>} */\n      var radios = popup.querySelectorAll(\"[name=\\\"\".concat(swalClasses.radio, \"\\\"]\"));\n      for (var i = 0; i < radios.length; i++) {\n        radios[i].disabled = disabled;\n      }\n    } else {\n      input.disabled = disabled;\n    }\n  }\n\n  /**\n   * Enable all the buttons\n   * @this {SweetAlert}\n   */\n  function enableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], false);\n  }\n\n  /**\n   * Disable all the buttons\n   * @this {SweetAlert}\n   */\n  function disableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], true);\n  }\n\n  /**\n   * Enable the input field\n   * @this {SweetAlert}\n   */\n  function enableInput() {\n    setInputDisabled(this.getInput(), false);\n  }\n\n  /**\n   * Disable the input field\n   * @this {SweetAlert}\n   */\n  function disableInput() {\n    setInputDisabled(this.getInput(), true);\n  }\n\n  /**\n   * Show block with validation message\n   *\n   * @param {string} error\n   * @this {SweetAlert}\n   */\n  function showValidationMessage(error) {\n    var domCache = privateProps.domCache.get(this);\n    var params = privateProps.innerParams.get(this);\n    setInnerHtml(domCache.validationMessage, error);\n    domCache.validationMessage.className = swalClasses['validation-message'];\n    if (params.customClass && params.customClass.validationMessage) {\n      addClass(domCache.validationMessage, params.customClass.validationMessage);\n    }\n    show(domCache.validationMessage);\n    var input = this.getInput();\n    if (input) {\n      input.setAttribute('aria-invalid', 'true');\n      input.setAttribute('aria-describedby', swalClasses['validation-message']);\n      focusInput(input);\n      addClass(input, swalClasses.inputerror);\n    }\n  }\n\n  /**\n   * Hide block with validation message\n   *\n   * @this {SweetAlert}\n   */\n  function resetValidationMessage() {\n    var domCache = privateProps.domCache.get(this);\n    if (domCache.validationMessage) {\n      hide(domCache.validationMessage);\n    }\n    var input = this.getInput();\n    if (input) {\n      input.removeAttribute('aria-invalid');\n      input.removeAttribute('aria-describedby');\n      removeClass(input, swalClasses.inputerror);\n    }\n  }\n\n  var defaultParams = {\n    title: '',\n    titleText: '',\n    text: '',\n    html: '',\n    footer: '',\n    icon: undefined,\n    iconColor: undefined,\n    iconHtml: undefined,\n    template: undefined,\n    toast: false,\n    animation: true,\n    showClass: {\n      popup: 'swal2-show',\n      backdrop: 'swal2-backdrop-show',\n      icon: 'swal2-icon-show'\n    },\n    hideClass: {\n      popup: 'swal2-hide',\n      backdrop: 'swal2-backdrop-hide',\n      icon: 'swal2-icon-hide'\n    },\n    customClass: {},\n    target: 'body',\n    color: undefined,\n    backdrop: true,\n    heightAuto: true,\n    allowOutsideClick: true,\n    allowEscapeKey: true,\n    allowEnterKey: true,\n    stopKeydownPropagation: true,\n    keydownListenerCapture: false,\n    showConfirmButton: true,\n    showDenyButton: false,\n    showCancelButton: false,\n    preConfirm: undefined,\n    preDeny: undefined,\n    confirmButtonText: 'OK',\n    confirmButtonAriaLabel: '',\n    confirmButtonColor: undefined,\n    denyButtonText: 'No',\n    denyButtonAriaLabel: '',\n    denyButtonColor: undefined,\n    cancelButtonText: 'Cancel',\n    cancelButtonAriaLabel: '',\n    cancelButtonColor: undefined,\n    buttonsStyling: true,\n    reverseButtons: false,\n    focusConfirm: true,\n    focusDeny: false,\n    focusCancel: false,\n    returnFocus: true,\n    showCloseButton: false,\n    closeButtonHtml: '&times;',\n    closeButtonAriaLabel: 'Close this dialog',\n    loaderHtml: '',\n    showLoaderOnConfirm: false,\n    showLoaderOnDeny: false,\n    imageUrl: undefined,\n    imageWidth: undefined,\n    imageHeight: undefined,\n    imageAlt: '',\n    timer: undefined,\n    timerProgressBar: false,\n    width: undefined,\n    padding: undefined,\n    background: undefined,\n    input: undefined,\n    inputPlaceholder: '',\n    inputLabel: '',\n    inputValue: '',\n    inputOptions: {},\n    inputAutoFocus: true,\n    inputAutoTrim: true,\n    inputAttributes: {},\n    inputValidator: undefined,\n    returnInputValueOnDeny: false,\n    validationMessage: undefined,\n    grow: false,\n    position: 'center',\n    progressSteps: [],\n    currentProgressStep: undefined,\n    progressStepsDistance: undefined,\n    willOpen: undefined,\n    didOpen: undefined,\n    didRender: undefined,\n    willClose: undefined,\n    didClose: undefined,\n    didDestroy: undefined,\n    scrollbarPadding: true\n  };\n  var updatableParams = ['allowEscapeKey', 'allowOutsideClick', 'background', 'buttonsStyling', 'cancelButtonAriaLabel', 'cancelButtonColor', 'cancelButtonText', 'closeButtonAriaLabel', 'closeButtonHtml', 'color', 'confirmButtonAriaLabel', 'confirmButtonColor', 'confirmButtonText', 'currentProgressStep', 'customClass', 'denyButtonAriaLabel', 'denyButtonColor', 'denyButtonText', 'didClose', 'didDestroy', 'footer', 'hideClass', 'html', 'icon', 'iconColor', 'iconHtml', 'imageAlt', 'imageHeight', 'imageUrl', 'imageWidth', 'preConfirm', 'preDeny', 'progressSteps', 'returnFocus', 'reverseButtons', 'showCancelButton', 'showCloseButton', 'showConfirmButton', 'showDenyButton', 'text', 'title', 'titleText', 'willClose'];\n\n  /** @type {Record<string, string>} */\n  var deprecatedParams = {};\n  var toastIncompatibleParams = ['allowOutsideClick', 'allowEnterKey', 'backdrop', 'focusConfirm', 'focusDeny', 'focusCancel', 'returnFocus', 'heightAuto', 'keydownListenerCapture'];\n\n  /**\n   * Is valid parameter\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  var isValidParameter = function isValidParameter(paramName) {\n    return Object.prototype.hasOwnProperty.call(defaultParams, paramName);\n  };\n\n  /**\n   * Is valid parameter for Swal.update() method\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  var isUpdatableParameter = function isUpdatableParameter(paramName) {\n    return updatableParams.indexOf(paramName) !== -1;\n  };\n\n  /**\n   * Is deprecated parameter\n   *\n   * @param {string} paramName\n   * @returns {string | undefined}\n   */\n  var isDeprecatedParameter = function isDeprecatedParameter(paramName) {\n    return deprecatedParams[paramName];\n  };\n\n  /**\n   * @param {string} param\n   */\n  var checkIfParamIsValid = function checkIfParamIsValid(param) {\n    if (!isValidParameter(param)) {\n      warn(\"Unknown parameter \\\"\".concat(param, \"\\\"\"));\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  var checkIfToastParamIsValid = function checkIfToastParamIsValid(param) {\n    if (toastIncompatibleParams.includes(param)) {\n      warn(\"The parameter \\\"\".concat(param, \"\\\" is incompatible with toasts\"));\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  var checkIfParamIsDeprecated = function checkIfParamIsDeprecated(param) {\n    var isDeprecated = isDeprecatedParameter(param);\n    if (isDeprecated) {\n      warnAboutDeprecation(param, isDeprecated);\n    }\n  };\n\n  /**\n   * Show relevant warnings for given params\n   *\n   * @param {SweetAlertOptions} params\n   */\n  var showWarningsForParams = function showWarningsForParams(params) {\n    if (params.backdrop === false && params.allowOutsideClick) {\n      warn('\"allowOutsideClick\" parameter requires `backdrop` parameter to be set to `true`');\n    }\n    for (var param in params) {\n      checkIfParamIsValid(param);\n      if (params.toast) {\n        checkIfToastParamIsValid(param);\n      }\n      checkIfParamIsDeprecated(param);\n    }\n  };\n\n  /**\n   * Updates popup parameters.\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function update(params) {\n    var popup = getPopup();\n    var innerParams = privateProps.innerParams.get(this);\n    if (!popup || hasClass(popup, innerParams.hideClass.popup)) {\n      warn(\"You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.\");\n      return;\n    }\n    var validUpdatableParams = filterValidParams(params);\n    var updatedParams = Object.assign({}, innerParams, validUpdatableParams);\n    render(this, updatedParams);\n    privateProps.innerParams.set(this, updatedParams);\n    Object.defineProperties(this, {\n      params: {\n        value: Object.assign({}, this.params, params),\n        writable: false,\n        enumerable: true\n      }\n    });\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  var filterValidParams = function filterValidParams(params) {\n    var validUpdatableParams = {};\n    Object.keys(params).forEach(function (param) {\n      if (isUpdatableParameter(param)) {\n        validUpdatableParams[param] = params[param];\n      } else {\n        warn(\"Invalid parameter to update: \".concat(param));\n      }\n    });\n    return validUpdatableParams;\n  };\n\n  /**\n   * Dispose the current SweetAlert2 instance\n   */\n  function _destroy() {\n    var domCache = privateProps.domCache.get(this);\n    var innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      disposeWeakMaps(this); // The WeakMaps might have been partly destroyed, we must recall it to dispose any remaining WeakMaps #2335\n      return; // This instance has already been destroyed\n    }\n\n    // Check if there is another Swal closing\n    if (domCache.popup && globalState.swalCloseEventFinishedCallback) {\n      globalState.swalCloseEventFinishedCallback();\n      delete globalState.swalCloseEventFinishedCallback;\n    }\n    if (typeof innerParams.didDestroy === 'function') {\n      innerParams.didDestroy();\n    }\n    disposeSwal(this);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  var disposeSwal = function disposeSwal(instance) {\n    disposeWeakMaps(instance);\n    // Unset this.params so GC will dispose it (#1569)\n    delete instance.params;\n    // Unset globalState props so GC will dispose globalState (#1569)\n    delete globalState.keydownHandler;\n    delete globalState.keydownTarget;\n    // Unset currentInstance\n    delete globalState.currentInstance;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  var disposeWeakMaps = function disposeWeakMaps(instance) {\n    // If the current instance is awaiting a promise result, we keep the privateMethods to call them once the promise result is retrieved #2335\n    if (instance.isAwaitingPromise) {\n      unsetWeakMaps(privateProps, instance);\n      instance.isAwaitingPromise = true;\n    } else {\n      unsetWeakMaps(privateMethods, instance);\n      unsetWeakMaps(privateProps, instance);\n      delete instance.isAwaitingPromise;\n      // Unset instance methods\n      delete instance.disableButtons;\n      delete instance.enableButtons;\n      delete instance.getInput;\n      delete instance.disableInput;\n      delete instance.enableInput;\n      delete instance.hideLoading;\n      delete instance.disableLoading;\n      delete instance.showValidationMessage;\n      delete instance.resetValidationMessage;\n      delete instance.close;\n      delete instance.closePopup;\n      delete instance.closeModal;\n      delete instance.closeToast;\n      delete instance.rejectPromise;\n      delete instance.update;\n      delete instance._destroy;\n    }\n  };\n\n  /**\n   * @param {object} obj\n   * @param {SweetAlert} instance\n   */\n  var unsetWeakMaps = function unsetWeakMaps(obj, instance) {\n    for (var i in obj) {\n      obj[i][\"delete\"](instance);\n    }\n  };\n\n  var instanceMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    _destroy: _destroy,\n    close: close,\n    closeModal: close,\n    closePopup: close,\n    closeToast: close,\n    disableButtons: disableButtons,\n    disableInput: disableInput,\n    disableLoading: hideLoading,\n    enableButtons: enableButtons,\n    enableInput: enableInput,\n    getInput: getInput,\n    handleAwaitingPromise: handleAwaitingPromise,\n    hideLoading: hideLoading,\n    rejectPromise: rejectPromise,\n    resetValidationMessage: resetValidationMessage,\n    showValidationMessage: showValidationMessage,\n    update: update\n  });\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  var handlePopupClick = function handlePopupClick(innerParams, domCache, dismissWith) {\n    if (innerParams.toast) {\n      handleToastClick(innerParams, domCache, dismissWith);\n    } else {\n      // Ignore click events that had mousedown on the popup but mouseup on the container\n      // This can happen when the user drags a slider\n      handleModalMousedown(domCache);\n\n      // Ignore click events that had mousedown on the container but mouseup on the popup\n      handleContainerMousedown(domCache);\n      handleModalClick(innerParams, domCache, dismissWith);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  var handleToastClick = function handleToastClick(innerParams, domCache, dismissWith) {\n    // Closing toast by internal click\n    domCache.popup.onclick = function () {\n      if (innerParams && (isAnyButtonShown(innerParams) || innerParams.timer || innerParams.input)) {\n        return;\n      }\n      dismissWith(DismissReason.close);\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  var isAnyButtonShown = function isAnyButtonShown(innerParams) {\n    return !!(innerParams.showConfirmButton || innerParams.showDenyButton || innerParams.showCancelButton || innerParams.showCloseButton);\n  };\n  var ignoreOutsideClick = false;\n\n  /**\n   * @param {DomCache} domCache\n   */\n  var handleModalMousedown = function handleModalMousedown(domCache) {\n    domCache.popup.onmousedown = function () {\n      domCache.container.onmouseup = function (e) {\n        domCache.container.onmouseup = function () {};\n        // We only check if the mouseup target is the container because usually it doesn't\n        // have any other direct children aside of the popup\n        if (e.target === domCache.container) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {DomCache} domCache\n   */\n  var handleContainerMousedown = function handleContainerMousedown(domCache) {\n    domCache.container.onmousedown = function () {\n      domCache.popup.onmouseup = function (e) {\n        domCache.popup.onmouseup = function () {};\n        // We also need to check if the mouseup target is a child of the popup\n        if (e.target === domCache.popup || e.target instanceof HTMLElement && domCache.popup.contains(e.target)) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  var handleModalClick = function handleModalClick(innerParams, domCache, dismissWith) {\n    domCache.container.onclick = function (e) {\n      if (ignoreOutsideClick) {\n        ignoreOutsideClick = false;\n        return;\n      }\n      if (e.target === domCache.container && callIfFunction(innerParams.allowOutsideClick)) {\n        dismissWith(DismissReason.backdrop);\n      }\n    };\n  };\n\n  var isJqueryElement = function isJqueryElement(elem) {\n    return _typeof(elem) === 'object' && elem.jquery;\n  };\n  var isElement = function isElement(elem) {\n    return elem instanceof Element || isJqueryElement(elem);\n  };\n  var argsToParams = function argsToParams(args) {\n    var params = {};\n    if (_typeof(args[0]) === 'object' && !isElement(args[0])) {\n      Object.assign(params, args[0]);\n    } else {\n      ['title', 'html', 'icon'].forEach(function (name, index) {\n        var arg = args[index];\n        if (typeof arg === 'string' || isElement(arg)) {\n          params[name] = arg;\n        } else if (arg !== undefined) {\n          error(\"Unexpected type of \".concat(name, \"! Expected \\\"string\\\" or \\\"Element\\\", got \").concat(_typeof(arg)));\n        }\n      });\n    }\n    return params;\n  };\n\n  /**\n   * Main method to create a new SweetAlert2 popup\n   *\n   * @param  {...SweetAlertOptions} args\n   * @returns {Promise<SweetAlertResult>}\n   */\n  function fire() {\n    var Swal = this; // eslint-disable-line @typescript-eslint/no-this-alias\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return _construct(Swal, args);\n  }\n\n  /**\n   * Returns an extended version of `Swal` containing `params` as defaults.\n   * Useful for reusing Swal configuration.\n   *\n   * For example:\n   *\n   * Before:\n   * const textPromptOptions = { input: 'text', showCancelButton: true }\n   * const {value: firstName} = await Swal.fire({ ...textPromptOptions, title: 'What is your first name?' })\n   * const {value: lastName} = await Swal.fire({ ...textPromptOptions, title: 'What is your last name?' })\n   *\n   * After:\n   * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })\n   * const {value: firstName} = await TextPrompt('What is your first name?')\n   * const {value: lastName} = await TextPrompt('What is your last name?')\n   *\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlert}\n   */\n  function mixin(mixinParams) {\n    var MixinSwal = /*#__PURE__*/function (_this) {\n      _inherits(MixinSwal, _this);\n      function MixinSwal() {\n        _classCallCheck(this, MixinSwal);\n        return _callSuper(this, MixinSwal, arguments);\n      }\n      _createClass(MixinSwal, [{\n        key: \"_main\",\n        value: function _main(params, priorityMixinParams) {\n          return _get(_getPrototypeOf(MixinSwal.prototype), \"_main\", this).call(this, params, Object.assign({}, mixinParams, priorityMixinParams));\n        }\n      }]);\n      return MixinSwal;\n    }(this); // @ts-ignore\n    return MixinSwal;\n  }\n\n  /**\n   * If `timer` parameter is set, returns number of milliseconds of timer remained.\n   * Otherwise, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  var getTimerLeft = function getTimerLeft() {\n    return globalState.timeout && globalState.timeout.getTimerLeft();\n  };\n\n  /**\n   * Stop timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  var stopTimer = function stopTimer() {\n    if (globalState.timeout) {\n      stopTimerProgressBar();\n      return globalState.timeout.stop();\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  var resumeTimer = function resumeTimer() {\n    if (globalState.timeout) {\n      var remaining = globalState.timeout.start();\n      animateTimerProgressBar(remaining);\n      return remaining;\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  var toggleTimer = function toggleTimer() {\n    var timer = globalState.timeout;\n    return timer && (timer.running ? stopTimer() : resumeTimer());\n  };\n\n  /**\n   * Increase timer. Returns number of milliseconds of an updated timer.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @param {number} ms\n   * @returns {number | undefined}\n   */\n  var increaseTimer = function increaseTimer(ms) {\n    if (globalState.timeout) {\n      var remaining = globalState.timeout.increase(ms);\n      animateTimerProgressBar(remaining, true);\n      return remaining;\n    }\n  };\n\n  /**\n   * Check if timer is running. Returns true if timer is running\n   * or false if timer is paused or stopped.\n   * If `timer` parameter isn't set, returns undefined\n   *\n   * @returns {boolean}\n   */\n  var isTimerRunning = function isTimerRunning() {\n    return !!(globalState.timeout && globalState.timeout.isRunning());\n  };\n\n  var bodyClickListenerAdded = false;\n  var clickHandlers = {};\n\n  /**\n   * @param {string} attr\n   */\n  function bindClickHandler() {\n    var attr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'data-swal-template';\n    clickHandlers[attr] = this;\n    if (!bodyClickListenerAdded) {\n      document.body.addEventListener('click', bodyClickListener);\n      bodyClickListenerAdded = true;\n    }\n  }\n  var bodyClickListener = function bodyClickListener(event) {\n    for (var el = event.target; el && el !== document; el = el.parentNode) {\n      for (var attr in clickHandlers) {\n        var template = el.getAttribute(attr);\n        if (template) {\n          clickHandlers[attr].fire({\n            template: template\n          });\n          return;\n        }\n      }\n    }\n  };\n\n  var staticMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    argsToParams: argsToParams,\n    bindClickHandler: bindClickHandler,\n    clickCancel: clickCancel,\n    clickConfirm: clickConfirm,\n    clickDeny: clickDeny,\n    enableLoading: showLoading,\n    fire: fire,\n    getActions: getActions,\n    getCancelButton: getCancelButton,\n    getCloseButton: getCloseButton,\n    getConfirmButton: getConfirmButton,\n    getContainer: getContainer,\n    getDenyButton: getDenyButton,\n    getFocusableElements: getFocusableElements,\n    getFooter: getFooter,\n    getHtmlContainer: getHtmlContainer,\n    getIcon: getIcon,\n    getIconContent: getIconContent,\n    getImage: getImage,\n    getInputLabel: getInputLabel,\n    getLoader: getLoader,\n    getPopup: getPopup,\n    getProgressSteps: getProgressSteps,\n    getTimerLeft: getTimerLeft,\n    getTimerProgressBar: getTimerProgressBar,\n    getTitle: getTitle,\n    getValidationMessage: getValidationMessage,\n    increaseTimer: increaseTimer,\n    isDeprecatedParameter: isDeprecatedParameter,\n    isLoading: isLoading,\n    isTimerRunning: isTimerRunning,\n    isUpdatableParameter: isUpdatableParameter,\n    isValidParameter: isValidParameter,\n    isVisible: isVisible,\n    mixin: mixin,\n    resumeTimer: resumeTimer,\n    showLoading: showLoading,\n    stopTimer: stopTimer,\n    toggleTimer: toggleTimer\n  });\n\n  var Timer = /*#__PURE__*/function () {\n    /**\n     * @param {Function} callback\n     * @param {number} delay\n     */\n    function Timer(callback, delay) {\n      _classCallCheck(this, Timer);\n      this.callback = callback;\n      this.remaining = delay;\n      this.running = false;\n      this.start();\n    }\n\n    /**\n     * @returns {number}\n     */\n    _createClass(Timer, [{\n      key: \"start\",\n      value: function start() {\n        if (!this.running) {\n          this.running = true;\n          this.started = new Date();\n          this.id = setTimeout(this.callback, this.remaining);\n        }\n        return this.remaining;\n      }\n\n      /**\n       * @returns {number}\n       */\n    }, {\n      key: \"stop\",\n      value: function stop() {\n        if (this.started && this.running) {\n          this.running = false;\n          clearTimeout(this.id);\n          this.remaining -= new Date().getTime() - this.started.getTime();\n        }\n        return this.remaining;\n      }\n\n      /**\n       * @param {number} n\n       * @returns {number}\n       */\n    }, {\n      key: \"increase\",\n      value: function increase(n) {\n        var running = this.running;\n        if (running) {\n          this.stop();\n        }\n        this.remaining += n;\n        if (running) {\n          this.start();\n        }\n        return this.remaining;\n      }\n\n      /**\n       * @returns {number}\n       */\n    }, {\n      key: \"getTimerLeft\",\n      value: function getTimerLeft() {\n        if (this.running) {\n          this.stop();\n          this.start();\n        }\n        return this.remaining;\n      }\n\n      /**\n       * @returns {boolean}\n       */\n    }, {\n      key: \"isRunning\",\n      value: function isRunning() {\n        return this.running;\n      }\n    }]);\n    return Timer;\n  }();\n\n  var swalStringParams = ['swal-title', 'swal-html', 'swal-footer'];\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  var getTemplateParams = function getTemplateParams(params) {\n    /** @type {HTMLTemplateElement} */\n    var template = typeof params.template === 'string' ? document.querySelector(params.template) : params.template;\n    if (!template) {\n      return {};\n    }\n    /** @type {DocumentFragment} */\n    var templateContent = template.content;\n    showWarningsForElements(templateContent);\n    var result = Object.assign(getSwalParams(templateContent), getSwalFunctionParams(templateContent), getSwalButtons(templateContent), getSwalImage(templateContent), getSwalIcon(templateContent), getSwalInput(templateContent), getSwalStringParams(templateContent, swalStringParams));\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {SweetAlertOptions}\n   */\n  var getSwalParams = function getSwalParams(templateContent) {\n    var result = {};\n    /** @type {HTMLElement[]} */\n    var swalParams = Array.from(templateContent.querySelectorAll('swal-param'));\n    swalParams.forEach(function (param) {\n      showWarningsForAttributes(param, ['name', 'value']);\n      var paramName = param.getAttribute('name');\n      var value = param.getAttribute('value');\n      if (typeof defaultParams[paramName] === 'boolean') {\n        result[paramName] = value !== 'false';\n      } else if (_typeof(defaultParams[paramName]) === 'object') {\n        result[paramName] = JSON.parse(value);\n      } else {\n        result[paramName] = value;\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {SweetAlertOptions}\n   */\n  var getSwalFunctionParams = function getSwalFunctionParams(templateContent) {\n    var result = {};\n    /** @type {HTMLElement[]} */\n    var swalFunctions = Array.from(templateContent.querySelectorAll('swal-function-param'));\n    swalFunctions.forEach(function (param) {\n      var paramName = param.getAttribute('name');\n      var value = param.getAttribute('value');\n      result[paramName] = new Function(\"return \".concat(value))();\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {SweetAlertOptions}\n   */\n  var getSwalButtons = function getSwalButtons(templateContent) {\n    var result = {};\n    /** @type {HTMLElement[]} */\n    var swalButtons = Array.from(templateContent.querySelectorAll('swal-button'));\n    swalButtons.forEach(function (button) {\n      showWarningsForAttributes(button, ['type', 'color', 'aria-label']);\n      var type = button.getAttribute('type');\n      result[\"\".concat(type, \"ButtonText\")] = button.innerHTML;\n      result[\"show\".concat(capitalizeFirstLetter(type), \"Button\")] = true;\n      if (button.hasAttribute('color')) {\n        result[\"\".concat(type, \"ButtonColor\")] = button.getAttribute('color');\n      }\n      if (button.hasAttribute('aria-label')) {\n        result[\"\".concat(type, \"ButtonAriaLabel\")] = button.getAttribute('aria-label');\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {SweetAlertOptions}\n   */\n  var getSwalImage = function getSwalImage(templateContent) {\n    var result = {};\n    /** @type {HTMLElement} */\n    var image = templateContent.querySelector('swal-image');\n    if (image) {\n      showWarningsForAttributes(image, ['src', 'width', 'height', 'alt']);\n      if (image.hasAttribute('src')) {\n        result.imageUrl = image.getAttribute('src');\n      }\n      if (image.hasAttribute('width')) {\n        result.imageWidth = image.getAttribute('width');\n      }\n      if (image.hasAttribute('height')) {\n        result.imageHeight = image.getAttribute('height');\n      }\n      if (image.hasAttribute('alt')) {\n        result.imageAlt = image.getAttribute('alt');\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {SweetAlertOptions}\n   */\n  var getSwalIcon = function getSwalIcon(templateContent) {\n    var result = {};\n    /** @type {HTMLElement} */\n    var icon = templateContent.querySelector('swal-icon');\n    if (icon) {\n      showWarningsForAttributes(icon, ['type', 'color']);\n      if (icon.hasAttribute('type')) {\n        /** @type {SweetAlertIcon} */\n        // @ts-ignore\n        result.icon = icon.getAttribute('type');\n      }\n      if (icon.hasAttribute('color')) {\n        result.iconColor = icon.getAttribute('color');\n      }\n      result.iconHtml = icon.innerHTML;\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {SweetAlertOptions}\n   */\n  var getSwalInput = function getSwalInput(templateContent) {\n    var result = {};\n    /** @type {HTMLElement} */\n    var input = templateContent.querySelector('swal-input');\n    if (input) {\n      showWarningsForAttributes(input, ['type', 'label', 'placeholder', 'value']);\n      /** @type {SweetAlertInput} */\n      // @ts-ignore\n      result.input = input.getAttribute('type') || 'text';\n      if (input.hasAttribute('label')) {\n        result.inputLabel = input.getAttribute('label');\n      }\n      if (input.hasAttribute('placeholder')) {\n        result.inputPlaceholder = input.getAttribute('placeholder');\n      }\n      if (input.hasAttribute('value')) {\n        result.inputValue = input.getAttribute('value');\n      }\n    }\n    /** @type {HTMLElement[]} */\n    var inputOptions = Array.from(templateContent.querySelectorAll('swal-input-option'));\n    if (inputOptions.length) {\n      result.inputOptions = {};\n      inputOptions.forEach(function (option) {\n        showWarningsForAttributes(option, ['value']);\n        var optionValue = option.getAttribute('value');\n        var optionName = option.innerHTML;\n        result.inputOptions[optionValue] = optionName;\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @param {string[]} paramNames\n   * @returns {SweetAlertOptions}\n   */\n  var getSwalStringParams = function getSwalStringParams(templateContent, paramNames) {\n    var result = {};\n    for (var i in paramNames) {\n      var paramName = paramNames[i];\n      /** @type {HTMLElement} */\n      var tag = templateContent.querySelector(paramName);\n      if (tag) {\n        showWarningsForAttributes(tag, []);\n        result[paramName.replace(/^swal-/, '')] = tag.innerHTML.trim();\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   */\n  var showWarningsForElements = function showWarningsForElements(templateContent) {\n    var allowedElements = swalStringParams.concat(['swal-param', 'swal-function-param', 'swal-button', 'swal-image', 'swal-icon', 'swal-input', 'swal-input-option']);\n    Array.from(templateContent.children).forEach(function (el) {\n      var tagName = el.tagName.toLowerCase();\n      if (!allowedElements.includes(tagName)) {\n        warn(\"Unrecognized element <\".concat(tagName, \">\"));\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} el\n   * @param {string[]} allowedAttributes\n   */\n  var showWarningsForAttributes = function showWarningsForAttributes(el, allowedAttributes) {\n    Array.from(el.attributes).forEach(function (attribute) {\n      if (allowedAttributes.indexOf(attribute.name) === -1) {\n        warn([\"Unrecognized attribute \\\"\".concat(attribute.name, \"\\\" on <\").concat(el.tagName.toLowerCase(), \">.\"), \"\".concat(allowedAttributes.length ? \"Allowed attributes are: \".concat(allowedAttributes.join(', ')) : 'To set the value, use HTML within the element.')]);\n      }\n    });\n  };\n\n  var SHOW_CLASS_TIMEOUT = 10;\n\n  /**\n   * Open popup, add necessary classes and styles, fix scrollbar\n   *\n   * @param {SweetAlertOptions} params\n   */\n  var openPopup = function openPopup(params) {\n    var container = getContainer();\n    var popup = getPopup();\n    if (typeof params.willOpen === 'function') {\n      params.willOpen(popup);\n    }\n    var bodyStyles = window.getComputedStyle(document.body);\n    var initialBodyOverflow = bodyStyles.overflowY;\n    addClasses(container, popup, params);\n\n    // scrolling is 'hidden' until animation is done, after that 'auto'\n    setTimeout(function () {\n      setScrollingVisibility(container, popup);\n    }, SHOW_CLASS_TIMEOUT);\n    if (isModal()) {\n      fixScrollContainer(container, params.scrollbarPadding, initialBodyOverflow);\n      setAriaHidden();\n    }\n    if (!isToast() && !globalState.previousActiveElement) {\n      globalState.previousActiveElement = document.activeElement;\n    }\n    if (typeof params.didOpen === 'function') {\n      setTimeout(function () {\n        return params.didOpen(popup);\n      });\n    }\n    removeClass(container, swalClasses['no-transition']);\n  };\n\n  /**\n   * @param {AnimationEvent} event\n   */\n  var swalOpenAnimationFinished = function swalOpenAnimationFinished(event) {\n    var popup = getPopup();\n    if (event.target !== popup || !animationEndEvent) {\n      return;\n    }\n    var container = getContainer();\n    popup.removeEventListener(animationEndEvent, swalOpenAnimationFinished);\n    container.style.overflowY = 'auto';\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   */\n  var setScrollingVisibility = function setScrollingVisibility(container, popup) {\n    if (animationEndEvent && hasCssAnimation(popup)) {\n      container.style.overflowY = 'hidden';\n      popup.addEventListener(animationEndEvent, swalOpenAnimationFinished);\n    } else {\n      container.style.overflowY = 'auto';\n    }\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {boolean} scrollbarPadding\n   * @param {string} initialBodyOverflow\n   */\n  var fixScrollContainer = function fixScrollContainer(container, scrollbarPadding, initialBodyOverflow) {\n    iOSfix();\n    if (scrollbarPadding && initialBodyOverflow !== 'hidden') {\n      replaceScrollbarWithPadding(initialBodyOverflow);\n    }\n\n    // sweetalert2/issues/1247\n    setTimeout(function () {\n      container.scrollTop = 0;\n    });\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  var addClasses = function addClasses(container, popup, params) {\n    addClass(container, params.showClass.backdrop);\n    if (params.animation) {\n      // this workaround with opacity is needed for https://github.com/sweetalert2/sweetalert2/issues/2059\n      popup.style.setProperty('opacity', '0', 'important');\n      show(popup, 'grid');\n      setTimeout(function () {\n        // Animate popup right after showing it\n        addClass(popup, params.showClass.popup);\n        // and remove the opacity workaround\n        popup.style.removeProperty('opacity');\n      }, SHOW_CLASS_TIMEOUT); // 10ms in order to fix #2062\n    } else {\n      show(popup, 'grid');\n    }\n    addClass([document.documentElement, document.body], swalClasses.shown);\n    if (params.heightAuto && params.backdrop && !params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['height-auto']);\n    }\n  };\n\n  var defaultInputValidators = {\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    email: function email(string, validationMessage) {\n      return /^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z0-9-]{2,24}$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid email address');\n    },\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    url: function url(string, validationMessage) {\n      // taken from https://stackoverflow.com/a/3809435 with a small change from #1306 and #2013\n      return /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-z]{2,63}\\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid URL');\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function setDefaultInputValidators(params) {\n    // Use default `inputValidator` for supported input types if not provided\n    if (params.inputValidator) {\n      return;\n    }\n    if (params.input === 'email') {\n      params.inputValidator = defaultInputValidators['email'];\n    }\n    if (params.input === 'url') {\n      params.inputValidator = defaultInputValidators['url'];\n    }\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function validateCustomTargetElement(params) {\n    // Determine if the custom target element is valid\n    if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {\n      warn('Target parameter is not valid, defaulting to \"body\"');\n      params.target = 'body';\n    }\n  }\n\n  /**\n   * Set type, text and actions on popup\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function setParameters(params) {\n    setDefaultInputValidators(params);\n\n    // showLoaderOnConfirm && preConfirm\n    if (params.showLoaderOnConfirm && !params.preConfirm) {\n      warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\\n' + 'https://sweetalert2.github.io/#ajax-request');\n    }\n    validateCustomTargetElement(params);\n\n    // Replace newlines with <br> in title\n    if (typeof params.title === 'string') {\n      params.title = params.title.split('\\n').join('<br />');\n    }\n    init(params);\n  }\n\n  /** @type {SweetAlert} */\n  var currentInstance;\n  var _promise = /*#__PURE__*/new WeakMap();\n  var SweetAlert = /*#__PURE__*/function () {\n    /**\n     * @param {...any} args\n     * @this {SweetAlert}\n     */\n    function SweetAlert() {\n      _classCallCheck(this, SweetAlert);\n      /**\n       * @type {Promise<SweetAlertResult>}\n       */\n      _classPrivateFieldInitSpec(this, _promise, {\n        writable: true,\n        value: void 0\n      });\n      // Prevent run in Node env\n      if (typeof window === 'undefined') {\n        return;\n      }\n      currentInstance = this;\n\n      // @ts-ignore\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var outerParams = Object.freeze(this.constructor.argsToParams(args));\n\n      /** @type {Readonly<SweetAlertOptions>} */\n      this.params = outerParams;\n\n      /** @type {boolean} */\n      this.isAwaitingPromise = false;\n      _classPrivateFieldSet(this, _promise, this._main(currentInstance.params));\n    }\n    _createClass(SweetAlert, [{\n      key: \"_main\",\n      value: function _main(userParams) {\n        var mixinParams = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        showWarningsForParams(Object.assign({}, mixinParams, userParams));\n        if (globalState.currentInstance) {\n          var swalPromiseResolve = privateMethods.swalPromiseResolve.get(globalState.currentInstance);\n          var isAwaitingPromise = globalState.currentInstance.isAwaitingPromise;\n          globalState.currentInstance._destroy();\n          if (!isAwaitingPromise) {\n            swalPromiseResolve({\n              isDismissed: true\n            });\n          }\n          if (isModal()) {\n            unsetAriaHidden();\n          }\n        }\n        globalState.currentInstance = currentInstance;\n        var innerParams = prepareParams(userParams, mixinParams);\n        setParameters(innerParams);\n        Object.freeze(innerParams);\n\n        // clear the previous timer\n        if (globalState.timeout) {\n          globalState.timeout.stop();\n          delete globalState.timeout;\n        }\n\n        // clear the restore focus timeout\n        clearTimeout(globalState.restoreFocusTimeout);\n        var domCache = populateDomCache(currentInstance);\n        render(currentInstance, innerParams);\n        privateProps.innerParams.set(currentInstance, innerParams);\n        return swalPromise(currentInstance, domCache, innerParams);\n      }\n\n      // `catch` cannot be the name of a module export, so we define our thenable methods here instead\n    }, {\n      key: \"then\",\n      value: function then(onFulfilled) {\n        return _classPrivateFieldGet(this, _promise).then(onFulfilled);\n      }\n    }, {\n      key: \"finally\",\n      value: function _finally(onFinally) {\n        return _classPrivateFieldGet(this, _promise)[\"finally\"](onFinally);\n      }\n    }]);\n    return SweetAlert;\n  }();\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {Promise}\n   */\n  var swalPromise = function swalPromise(instance, domCache, innerParams) {\n    return new Promise(function (resolve, reject) {\n      // functions to handle all closings/dismissals\n      /**\n       * @param {DismissReason} dismiss\n       */\n      var dismissWith = function dismissWith(dismiss) {\n        instance.close({\n          isDismissed: true,\n          dismiss: dismiss\n        });\n      };\n      privateMethods.swalPromiseResolve.set(instance, resolve);\n      privateMethods.swalPromiseReject.set(instance, reject);\n      domCache.confirmButton.onclick = function () {\n        handleConfirmButtonClick(instance);\n      };\n      domCache.denyButton.onclick = function () {\n        handleDenyButtonClick(instance);\n      };\n      domCache.cancelButton.onclick = function () {\n        handleCancelButtonClick(instance, dismissWith);\n      };\n      domCache.closeButton.onclick = function () {\n        dismissWith(DismissReason.close);\n      };\n      handlePopupClick(innerParams, domCache, dismissWith);\n      addKeydownHandler(globalState, innerParams, dismissWith);\n      handleInputOptionsAndValue(instance, innerParams);\n      openPopup(innerParams);\n      setupTimer(globalState, innerParams, dismissWith);\n      initFocus(domCache, innerParams);\n\n      // Scroll container to top on open (#1247, #1946)\n      setTimeout(function () {\n        domCache.container.scrollTop = 0;\n      });\n    });\n  };\n\n  /**\n   * @param {SweetAlertOptions} userParams\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlertOptions}\n   */\n  var prepareParams = function prepareParams(userParams, mixinParams) {\n    var templateParams = getTemplateParams(userParams);\n    var params = Object.assign({}, defaultParams, mixinParams, templateParams, userParams); // precedence is described in #2131\n    params.showClass = Object.assign({}, defaultParams.showClass, params.showClass);\n    params.hideClass = Object.assign({}, defaultParams.hideClass, params.hideClass);\n    if (params.animation === false) {\n      params.showClass = {\n        backdrop: 'swal2-noanimation'\n      };\n      params.hideClass = {};\n    }\n    return params;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @returns {DomCache}\n   */\n  var populateDomCache = function populateDomCache(instance) {\n    var domCache = {\n      popup: getPopup(),\n      container: getContainer(),\n      actions: getActions(),\n      confirmButton: getConfirmButton(),\n      denyButton: getDenyButton(),\n      cancelButton: getCancelButton(),\n      loader: getLoader(),\n      closeButton: getCloseButton(),\n      validationMessage: getValidationMessage(),\n      progressSteps: getProgressSteps()\n    };\n    privateProps.domCache.set(instance, domCache);\n    return domCache;\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  var setupTimer = function setupTimer(globalState, innerParams, dismissWith) {\n    var timerProgressBar = getTimerProgressBar();\n    hide(timerProgressBar);\n    if (innerParams.timer) {\n      globalState.timeout = new Timer(function () {\n        dismissWith('timer');\n        delete globalState.timeout;\n      }, innerParams.timer);\n      if (innerParams.timerProgressBar) {\n        show(timerProgressBar);\n        applyCustomClass(timerProgressBar, innerParams, 'timerProgressBar');\n        setTimeout(function () {\n          if (globalState.timeout && globalState.timeout.running) {\n            // timer can be already stopped or unset at this point\n            animateTimerProgressBar(innerParams.timer);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   */\n  var initFocus = function initFocus(domCache, innerParams) {\n    if (innerParams.toast) {\n      return;\n    }\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      blurActiveElement();\n      return;\n    }\n    if (!focusButton(domCache, innerParams)) {\n      setFocus(-1, 1);\n    }\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  var focusButton = function focusButton(domCache, innerParams) {\n    if (innerParams.focusDeny && isVisible$1(domCache.denyButton)) {\n      domCache.denyButton.focus();\n      return true;\n    }\n    if (innerParams.focusCancel && isVisible$1(domCache.cancelButton)) {\n      domCache.cancelButton.focus();\n      return true;\n    }\n    if (innerParams.focusConfirm && isVisible$1(domCache.confirmButton)) {\n      domCache.confirmButton.focus();\n      return true;\n    }\n    return false;\n  };\n  var blurActiveElement = function blurActiveElement() {\n    if (document.activeElement instanceof HTMLElement && typeof document.activeElement.blur === 'function') {\n      document.activeElement.blur();\n    }\n  };\n\n  // Dear russian users visiting russian sites. Let's have fun.\n  if (typeof window !== 'undefined' && /^ru\\b/.test(navigator.language) && location.host.match(/\\.(ru|su|by|xn--p1ai)$/)) {\n    var now = new Date();\n    var initiationDate = localStorage.getItem('swal-initiation');\n    if (!initiationDate) {\n      localStorage.setItem('swal-initiation', \"\".concat(now));\n    } else if ((now.getTime() - Date.parse(initiationDate)) / (1000 * 60 * 60 * 24) > 3) {\n      setTimeout(function () {\n        document.body.style.pointerEvents = 'none';\n        var ukrainianAnthem = document.createElement('audio');\n        ukrainianAnthem.src = 'https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3';\n        ukrainianAnthem.loop = true;\n        document.body.appendChild(ukrainianAnthem);\n        setTimeout(function () {\n          ukrainianAnthem.play()[\"catch\"](function () {\n            // ignore\n          });\n        }, 2500);\n      }, 500);\n    }\n  }\n\n  // Assign instance methods from src/instanceMethods/*.js to prototype\n  SweetAlert.prototype.disableButtons = disableButtons;\n  SweetAlert.prototype.enableButtons = enableButtons;\n  SweetAlert.prototype.getInput = getInput;\n  SweetAlert.prototype.disableInput = disableInput;\n  SweetAlert.prototype.enableInput = enableInput;\n  SweetAlert.prototype.hideLoading = hideLoading;\n  SweetAlert.prototype.disableLoading = hideLoading;\n  SweetAlert.prototype.showValidationMessage = showValidationMessage;\n  SweetAlert.prototype.resetValidationMessage = resetValidationMessage;\n  SweetAlert.prototype.close = close;\n  SweetAlert.prototype.closePopup = close;\n  SweetAlert.prototype.closeModal = close;\n  SweetAlert.prototype.closeToast = close;\n  SweetAlert.prototype.rejectPromise = rejectPromise;\n  SweetAlert.prototype.update = update;\n  SweetAlert.prototype._destroy = _destroy;\n\n  // Assign static methods from src/staticMethods/*.js to constructor\n  Object.assign(SweetAlert, staticMethods);\n\n  // Proxy to instance methods to constructor, for now, for backwards compatibility\n  Object.keys(instanceMethods).forEach(function (key) {\n    /**\n     * @param {...any} args\n     * @returns {any | undefined}\n     */\n    SweetAlert[key] = function () {\n      if (currentInstance && currentInstance[key]) {\n        var _currentInstance;\n        return (_currentInstance = currentInstance)[key].apply(_currentInstance, arguments);\n      }\n      return null;\n    };\n  });\n  SweetAlert.DismissReason = DismissReason;\n  SweetAlert.version = '11.10.4';\n\n  var Swal = SweetAlert;\n  // @ts-ignore\n  Swal[\"default\"] = Swal;\n\n  return Swal;\n\n}));\nif (typeof this !== 'undefined' && this.Sweetalert2){this.swal = this.sweetAlert = this.Swal = this.SweetAlert = this.Sweetalert2}\n\n\n//# sourceURL=webpack://Vuexy/./node_modules/sweetalert2/dist/sweetalert2.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	!function() {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = function(module) {
/******/ 			var getter = module && module.__esModule ?
/******/ 				function() { return module['default']; } :
/******/ 				function() { return module; };
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./libs/sweetalert2/sweetalert2.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});