@charset "UTF-8";
/*
Project/Template Name:  Homez - Real Estate HTML Template
Template Url:
Author: CreativeLayers
Author Url: https://themeforest.net/user/creativelayers/portfolio
Version: 1.0.0
Description: Zeomart - Multi-Vendor & Marketplace HTML Template
Tag: advanced search, agency, agent, classified, directory, house, listing, property, real estate, real estate agency, real estate agent, realestate, realtor, rental
*/
/* == Table Of Content  == */
/* Variables */
/* Mixins */
/* Typography Styles */
/* Header Styles */
/* All Type Of Footer Styles */
/* All styles about breadcumb */
/* Dashboard Style Code Here */
/* Messages Styles */
/* Listings Styles */
/* About Pages Design Content Styles  */
/* Hero Styles All Type Of */
/* ALl Blogs Styles Here  */
/* Brands */
/* Commons Styles of Template */
/* Contact Styles */
/* All CTA Styles Here */
/* Error Pages Styles */
/* Iconbox Styles */
/* All Type Of Forms Styles */
/* Gallery Styles */
/* Packages And Pricing Tables */
/* Testimonials Styles */
/* Team Styles */
/* Different Styles Of Animations */
/* Different Accordion Styles*/
/* Different Styles of Alart */
/* blockquote Styles*/
/* All kind Of btns Styles */
/* Funfact Styles */
/* Menu Style */
/* Nav & Tabs Styles */
/* Paginations Styles */
/* ProgressBar Styles */
/* Social Styles */
/* Sliders Styles */
/* Sidebar Styles */
/* Table Styles */
/* Essential utility classes */
/*=================================
   Bootstrap Sass
==================================*/
/*=================================
   Theme Based
==================================*/
/* Variables */
:root {
  --title-font-family: 'Montserrat', sans-serif;
  --body-font-family: 'Montserrat', sans-serif;
  --icon-font-family: "Font Awesome 6 Pro";
  --icon-font-family2: "Flaticon";
  --container-width: 1290px;
  --divder-space: 200px;
  --headings-color: #242122;
}

/* Mixins */
/* Typography Styles */
html,
body {
  scroll-behavior: auto !important;
}

body {
  background-color: #ffffff;
  font-family: var(--body-font-family);
  font-size: 14px;
  font-weight: 400;
  color: var(--headings-color);
  line-height: 26px;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
}

a,
.btn {
  color: var(--headings-color);
  text-decoration: none;
  outline: none;
  -webkit-transition: all ease 0.4s;
  -moz-transition: all ease 0.4s;
  -o-transition: all ease 0.4s;
  transition: all ease 0.4s;
}
a:hover,
.btn:hover {
  color: #231f20;
}
a:active, a:focus, a:hover, a:visited,
.btn:active,
.btn:focus,
.btn:hover,
.btn:visited {
  text-decoration: none;
  outline: none;
}

button,
.btn {
  -webkit-transition: all ease 0.4s;
  -moz-transition: all ease 0.4s;
  -o-transition: all ease 0.4s;
  transition: all ease 0.4s;
}

iframe {
  border: none;
  width: 100%;
}

.h1,
h1,
.h2,
h2,
.h3,
h3,
.h4,
h4,
.h5,
h5,
.h6,
h6 {
  font-family: var(--title-font-family);
  color: var(--headings-color);
  font-weight: 600;
  line-height: 1.5;
}

p {
  margin: 0 0 15px 0;
  color: var(--headings-color);
  line-height: 1.85;
  font-weight: 400;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
p a,
span a {
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
}

.h1,
h1 {
  font-size: 36px;
}

.h2,
h2 {
  font-size: 30px;
}

.h3,
h3 {
  font-size: 24px;
}

.h4,
h4 {
  font-size: 20px;
}

.h5,
h5 {
  font-size: 18px;
}

.h6,
h6 {
  font-size: 15px;
}

li {
  list-style-type: none;
}

@media (max-width: 575.98px) {
  .h2,
h2 {
    font-size: 20px;
  }
}
/* Header Styles */
.login-info {
  font-size: 14px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}

/* All Type Of Footer Styles */
.footer-style1 {
  background-color: var(--headings-color);
}
.footer-style1.at-home2 {
  padding-top: 230px;
}
.footer-style1.at-home4 {
  background-color: #ffffff;
}
.footer-style1.at-home6 {
  background-color: transparent;
  background-image: url(../images/home/<USER>
  background-size: cover;
}

.footer-widget {
  position: relative;
}
.footer-widget .info-title {
  color: #BEBDBD;
  font-family: var(--title-font-family);
  margin-bottom: 5px;
}
.footer-widget .info-phone a,
.footer-widget .info-mail a {
  color: #ffffff;
}

.footer-widget.light-style .info-phone a,
.footer-widget.light-style .info-mail a {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}

.app-widget {
  position: relative;
}
.app-widget .app-info {
  background-color: rgba(255, 255, 255, 0.04);
  border-radius: 12px;
  padding: 10px 20px 2px;
  width: 210px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.app-widget .app-info:hover {
  background-color: #EB6753;
}
.app-widget .app-info.light-style {
  background-color: #181A20;
}
.app-widget .app-info.light-style:hover {
  background-color: #EB6753;
}
.app-widget .app-info.light-style:hover .app-text {
  color: #ffffff;
}
.app-widget .app-text {
  color: #BEBDBD;
}
.app-widget.at-home6 .app-info {
  background-color: #0D1C39;
  padding: 8px 20px 10px;
}
.app-widget.at-home6 .app-info .flex-shrink-0 {
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  padding-right: 20px;
}
.app-widget.at-home6 .app-info .flex-grow-1 {
  padding-left: 20px;
}
.app-widget.at-home6 .app-info .app-text {
  color: #ffffff;
}
.app-widget.at-home7 .app-info {
  border-radius: 0;
}

.mailchimp-style1 {
  position: relative;
}
.mailchimp-style1 .form-control {
  background-color: rgba(255, 255, 255, 0.04);
  border: none;
  border-radius: 12px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  height: 70px;
  padding-left: 30px;
}
.mailchimp-style1 .form-control:placeholder {
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
}
.mailchimp-style1 .form-control:focus {
  border: none;
  box-shadow: none;
  outline: none;
}
.mailchimp-style1 button {
  background-color: transparent;
  border: none;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-size: 15px;
  font-weight: 600;
  position: absolute;
  right: 25px;
  top: 20px;
}
.mailchimp-style1.white-version .form-control {
  background-color: #ffffff;
}
.mailchimp-style1.white-version .form-control:placeholder {
  color: var(--headings-color);
  font-size: 14px;
  font-weight: 400;
}
.mailchimp-style1.white-version button {
  font-size: 15px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.mailchimp-style1.at-home4 {
  border-radius: 12px;
  border: 1px solid #DDDDDD;
}
.mailchimp-style1.at-home4 .form-control {
  height: 60px;
}
.mailchimp-style1.at-home4 button {
  background-color: var(--headings-color);
  border-radius: 50%;
  color: #ffffff;
  display: block;
  height: 40px;
  line-height: 35px;
  right: 10px;
  top: 10px;
  width: 40px;
}
.mailchimp-style1.at-home7 {
  border: 1px solid #DDDDDD;
  border-radius: 0;
}
.mailchimp-style1.at-home7 .form-control {
  height: 60px;
}
.mailchimp-style1.at-home7 button {
  background-color: var(--headings-color);
  border-radius: 0;
  color: #ffffff;
  display: block;
  height: 40px;
  line-height: 35px;
  right: 10px;
  top: 10px;
  width: 40px;
}
.mailchimp-style1.at-home9 {
  border: 1px solid #DDDDDD;
  border-radius: 12px;
}
.mailchimp-style1.at-home9 .form-control {
  height: 70px;
}

.mailchimp-style2 {
  position: relative;
}
.mailchimp-style2 .form-control {
  background-color: #ffffff;
  border: none;
  border-radius: 12px;
  color: var(--headings-color);
  font-size: 14px;
  font-weight: 400;
  height: 70px;
  padding-left: 30px;
}
.mailchimp-style2 .form-control:placeholder {
  color: var(--headings-color);
  font-size: 14px;
  font-weight: 400;
}
.mailchimp-style2 .form-control:focus {
  border: none;
  box-shadow: none;
  outline: none;
}
.mailchimp-style2 button {
  height: 50px;
  position: absolute;
  right: 10px;
  top: 10px;
}

.link-style1 a {
  color: #BEBDBD;
  display: block;
  font-family: var(--title-font-family);
  line-height: 40px;
  position: relative;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.link-style1 a:hover {
  color: #ffffff;
  text-decoration: underline;
}
.link-style1.light-style a {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}
.link-style1.at-home9 a:hover {
  color: #EB6753;
}

/* All styles about breadcumb */
.breadcumb-section {
  padding: 70px 0;
}

.breadcumb-section2 {
  align-items: center;
  background-image: url(../images/background/about-page-bg.jpg);
  background-size: cover;
  background-position: center;
  display: flex;
  height: 450px;
}

.breadcumb-section3 {
  align-items: center;
  background-image: url(../images/background/compare-bg.jpg);
  background-size: cover;
  background-position: center;
  display: flex;
  height: 450px;
}
@media (max-width: 991.98px) {
  .breadcumb-section3 {
    height: 250px;
    position: relative;
  }
}

.breadcumb-style1 {
  position: relative;
}
.breadcumb-style1 .title {
  letter-spacing: 0.02em;
}
.breadcumb-style1 .breadcumb-list {
  position: relative;
}
.breadcumb-style1 .breadcumb-list a {
  display: inline-block;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  position: relative;
}
.breadcumb-style1 .breadcumb-list a:first-child {
  margin-right: 10px;
}
.breadcumb-style1 .breadcumb-list a:first-child:after {
  content: "/";
  font-size: 14px;
  position: absolute;
  right: -10px;
}

/* Dashboard Style Code Here */
.dashboard_header {
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.dashboard_header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white;
  z-index: 100;
}

.header_right_widgets {
  position: relative;
}
.header_right_widgets .notif {
  background-color: #F7F7F7;
  border-radius: 50%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  width: 44px;
}
.header_right_widgets a {
  color: var(--headings-color);
  display: block;
  font-size: 20px;
  height: 44px;
  line-height: 44px;
  width: 44px;
}
.header_right_widgets li:last-child a {
  height: auto;
  line-height: initial;
  margin: 0;
  padding: 0 0 0 5px;
  width: auto;
}

.user_setting {
  position: relative;
}
.user_setting .dropdown-menu {
  padding: 0;
}
.user_setting .dropdown-menu.show {
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  margin-top: 0;
  margin-left: -195px !important;
  padding: 20px 30px;
  width: 280px;
  transform: translate(-35px, 66px) !important;
}
.user_setting .dropdown-menu .dropdown-item {
  border-radius: 12px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  overflow: hidden;
  font-size: 14px;
  padding: 14.5px 20px;
  -webkit-transition: all 0.3s ease 0s ease;
  -moz-transition: all 0.3s ease 0s ease;
  -ms-transition: all 0.3s ease 0s ease;
  -o-transition: all 0.3s ease 0s ease;
  transition: all 0.3s ease 0s ease;
  margin-bottom: 5px;
}

.user_setting .dropdown-menu .dropdown-item.active,
.user_setting .dropdown-menu .dropdown-item.active span,
.user_setting .dropdown-menu .dropdown-item:hover,
.user_setting .dropdown-menu .dropdown-item:hover span {
  background-color: #9ac2c9;
  color: #ffffff;
}

.dashboard_sidebar_list .sidebar_list_item a:hover,
.dashboard_sidebar_list .sidebar_list_item a:active,
.dashboard_sidebar_list .sidebar_list_item a:focus,
.dashboard_sidebar_list .sidebar_list_item a.-is-active {
  background-color: #9ac2c9;
  color: #ffffff;
}

.dashboard_sidebar_list .sidebar_list_item a {
  align-items: center;
  display: flex;
  border-radius: 12px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  padding: 14.5px 20px;
  -webkit-transition: all 0.3s ease 0s ease;
  -moz-transition: all 0.3s ease 0s ease;
  -ms-transition: all 0.3s ease 0s ease;
  -o-transition: all 0.3s ease 0s ease;
  transition: all 0.3s ease 0s ease;
}

.dashboard_sidebar_list .sidebar_list_item a i {
  font-size: 20px;
}

.dashboard {
  display: flex;
}
.dashboard.dsh_board_sidebar_hidden .dashboard__sidebar {
  transform: translateX(-100%);
}
@media (max-width: 991.98px) {
  .dashboard.dsh_board_sidebar_hidden .dashboard__sidebar {
    transform: translateX(0);
  }
}
.dashboard.dsh_board_sidebar_hidden .dashboard__main {
  padding-left: 0;
}
@media (max-width: 991.98px) {
  .dashboard.dsh_board_sidebar_hidden .dashboard__main:after {
    background-color: rgba(4, 30, 66, 0.5);
    content: "";
    bottom: 0;
    left: 0;
    opacity: 1;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 0;
    pointer-events: auto;
    transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
}
.dashboard .dashboard__sidebar {
  will-change: transform;
  transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  z-index: 9;
}
@media (max-width: 991.98px) {
  .dashboard .dashboard__sidebar {
    transform: translateX(-100%);
  }
}
.dashboard .dashboard__main {
  will-change: padding-left;
  transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.dashboard__sidebar {
  background-color: #ffffff;
  flex-shrink: 0;
  height: calc(100% - 90px);
  left: 0;
  margin-top: 80px;
  overflow-y: scroll;
  padding: 30px;
  padding-top: 60px;
  padding-bottom: 40px;
  position: fixed;
  width: 300px;
}
.dashboard__sidebar::-webkit-scrollbar {
  height: 4px;
  width: 4px;
}
.dashboard__sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(33, 37, 41, 0.3);
  border-radius: 12px;
}

.dashboard .dashboard__main {
  will-change: padding-left;
  transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.dashboard__main {
  margin-top: 90px;
  padding-left: 300px;
  width: 100%;
}
@media (max-width: 991.98px) {
  .dashboard__main {
    margin-top: 0px;
  }
}

.dashboard__content {
  border-radius: 16px;
  padding: 60px 60px 20px;
  width: 100%;
}
@media (max-width: 767.98px) {
  .dashboard__content {
    padding: 40px 30px;
  }
}
@media (max-width: 1199.98px) {
  .dashboard__content.property-page {
    padding: 60px 30px 20px;
  }
}
@media (max-width: 1399.98px) {
  .dashboard__content.message-page {
    padding: 50px 30px;
  }
}

.dashboard_title_area {
  position: relative;
}

.statistics_funfact {
  background-color: #fff;
  border-radius: 16px;
  margin-bottom: 30px;
  padding: 30px 15px;
  position: relative;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
}
.statistics_funfact .icon {
  background-color: #edf4f5;
  border-radius: 50%;
  color: var(--headings-color);
  font-size: 30px;
  height: 80px;
  line-height: 85px;
  position: relative;
  width: 80px;
}
.statistics_funfact .title {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 36px;
  line-height: 54px;
}

.application_statics,
.dashboard_product_list,
.dashboard_setting_box {
  background-color: #fff;
  border-radius: 16px;
  margin-bottom: 30px;
  padding: 30px 30px 25px 0;
  position: relative;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
}

.recent-activity .icon {
  background-color: rgb(237 244 245);
  border-radius: 50%;
  color: var(--headings-color);
  display: block;
  height: 40px;
  line-height: 45px;
  text-align: center;
  width: 40px;
}

.pending-style {
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 60px;
}
.pending-style.style1 {
  background-color: #FFF8DD;
  color: #E4B303;
}
.pending-style.style2 {
  background-color: #E5F0FD;
  color: #3554D1;
}
.pending-style.style3 {
  background-color: #FFF5F8;
  color: #F1416C;
}

.profile-box .tag-del {
  background-color: #ffffff;
  border-radius: 12px;
  color: var(--headings-color);
  font-size: 18px;
  height: 45px;
  line-height: 45px;
  left: 20px;
  position: absolute;
  top: 20px;
  text-align: center;
  width: 45px;
}

.upload-img {
  border: 1px dashed #EB6753;
  padding-bottom: 50px;
  padding-top: 50px;
}
.upload-img .icon {
  font-size: 120px;
  color: #DDDDDD;
}

/* DashBoard Message Styles */
.message_container {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  height: 100%;
  overflow: hidden;
  position: relative;
}
.message_container .user_heading {
  background-color: #ffffff;
  border-bottom: 1px solid #DDDDDD;
  padding: 30px;
  position: relative;
}
.message_container .user_heading img {
  float: left;
  margin-right: 10px;
  border-radius: 50%;
}
.message_container .user_heading .preview {
  color: #717171;
  font-size: 13px;
  font-family: var(--title-font-family);
  font-weight: 400;
  margin-bottom: 0;
}
.message_container .user_heading .name {
  font-size: 14px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.message_container .message_input {
  background-color: #f9fafc;
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  text-align: center;
}
.message_container .message_input input.form-control {
  color: #626974;
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: 1px solid #f3f5f6;
  height: 100px;
  padding: 20px 30px;
  width: 100%;
}
.message_container .message_input button.btn {
  flex: 1;
  height: 50px;
  position: absolute;
  right: 20px;
  top: 20px;
}

.inbox_chatting_box {
  height: auto;
  margin-right: 10px;
  max-height: 575px !important;
  overflow-y: scroll;
  overflow-x: hidden;
  position: relative;
}
.inbox_chatting_box::-webkit-scrollbar {
  background: transparent;
  border-radius: 10px;
  padding-right: 10px;
  width: 4px;
}
.inbox_chatting_box::-webkit-scrollbar-thumb {
  background-color: #F1FCFA;
  border-radius: 10px;
}
.inbox_chatting_box .chatting_content {
  display: inline-block;
  padding: 15px 30px 0 30px;
  position: relative;
  width: 100%;
}
.inbox_chatting_box .chatting_content li {
  display: block;
  padding: 25px 0 5px;
  margin-bottom: 0;
  clear: both;
}
.inbox_chatting_box .chatting_content li p {
  background-color: #F1FCFA;
  border-radius: 8px;
  color: var(--headings-color);
  font-size: 15px;
  padding: 15px 20px;
  max-width: 456px;
}
.inbox_chatting_box .chatting_content li.reply {
  margin: -15px 0;
}
.inbox_chatting_box .chatting_content li.reply p {
  background-color: rgba(235, 103, 83, 0.07);
  border-radius: 8px;
  color: var(--headings-color);
  max-width: 450px;
  padding: 15px 30px;
  text-align: right;
}
.inbox_chatting_box .title {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.inbox_chatting_box small {
  color: #717171;
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0em;
}

.inbox_user_list {
  display: inline-block;
  padding: 30px 10px 25px 30px;
  width: 100%;
}
.inbox_user_list .iu_heading {
  padding-bottom: 25px;
}
.inbox_user_list .preview {
  font-size: 13px;
  font-family: var(--title-font-family);
  font-weight: 400;
  line-height: 18px;
  margin-bottom: 0;
}
.inbox_user_list .m_notif {
  background-color: #1967D2;
  border: 1px solid #ffffff;
  border-radius: 50%;
  color: #ffffff;
  font-size: 8px;
  font-weight: bold;
  line-height: 15px;
  height: 16px;
  position: absolute;
  right: 10px;
  text-align: center;
  top: 33px;
  width: 16px;
}
@media (max-width: 575.98px) {
  .inbox_user_list .m_notif {
    position: relative;
    right: auto;
    top: 0;
  }
}
.inbox_user_list .m_notif.online {
  background-color: #34A853;
}
.inbox_user_list .m_notif.away {
  background-color: #F9AB00;
}
.inbox_user_list .m_notif.busy {
  background-color: #e74c3c;
}

.chat-member-list {
  height: 620px;
  overflow-y: scroll;
}
.chat-member-list::-webkit-scrollbar {
  background: transparent;
  border-radius: 10px;
  padding-right: 10px;
  width: 4px;
}
.chat-member-list::-webkit-scrollbar-thumb {
  background-color: #F1FCFA;
  border-radius: 10px;
  height: 250px;
}
.chat-member-list img {
  width: 50px;
}
.chat-member-list .list-item {
  padding: 15px 0;
}

.chat_user_search {
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}
.chat_user_search button {
  background-color: #F7F7F7;
  border-radius: 0;
  border: none;
  color: var(--headings-color);
  height: 50px;
  line-height: 45px;
  width: 50px;
}
.chat_user_search input.form-control {
  background-color: #F7F7F7;
  border: none;
  border-radius: 0;
  height: 50px;
  margin-right: 0 !important;
  position: absolute;
  left: 50px;
  padding-left: 0;
  padding-right: 0;
  right: 30px;
  width: 100%;
}

.iul_notific small {
  display: block;
  font-size: 13px;
  font-family: var(--title-font-family);
  font-weight: 400;
  position: absolute;
  right: 10px;
  top: 10px;
}
@media (max-width: 575.98px) {
  .iul_notific small {
    position: relative;
    right: auto;
    top: 0;
  }
}

/* Messages Styles */
/* Listings Styles */
.listing-style1 {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  margin-bottom: 30px;
  overflow: hidden;
  position: relative;
}
.listing-style1 .list-thumb {
  overflow: hidden;
  position: relative;
}
.listing-style1 img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style1 .list-agent {
  position: absolute;
  right: 30px;
  top: -40px;
}
.listing-style1 .list-tag {
  background-color: #ffcb46;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 20px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style1 .tag-del {
  background-color: #ffffff;
  border-radius: 4px;
  color: var(--headings-color);
  font-size: 10px;
  height: 40px;
  line-height: 40px;
  position: absolute;
  right: 20px;
  text-align: center;
  top: 20px;
  width: 40px;
  z-index: 1;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style1 .list-price {
  background-color: #ffffff;
  border-radius: 6px;
  bottom: 20px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  left: 20px;
  padding: 3px 12px;
  position: absolute;
}
.listing-style1 .list-price span {
  font-weight: normal;
}
.listing-style1 .list-meta a {
  font-family: var(--title-font-family);
  font-size: 13px;
  margin-right: 25px;
}
.listing-style1 .list-meta a:last-child {
  margin-right: 0;
}
@media (max-width: 320px) {
  .listing-style1 .list-meta a {
    margin-right: 15px;
  }
}
.listing-style1 .list-meta span {
  margin-right: 6px;
}
.listing-style1 .list-content {
  padding: 20px;
  position: relative;
}
.listing-style1 .list-title {
  margin-bottom: 2px;
}
.listing-style1 .list-title a {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style1 .list-title a:hover {
  color: var(--headings-color);
}
.listing-style1 .list-text {
  color: #717171;
  margin-bottom: 10px;
}
.listing-style1 .list-meta2 a {
  background-color: #ffffff;
  border-radius: 6px;
  height: 35px;
  line-height: 40px;
  text-align: center;
  width: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  margin-right: 2px;
}
.listing-style1 .list-meta2 a:hover {
  background-color: #F7F7F7;
  color: var(--headings-color);
}
.listing-style1 .for-what {
  font-size: 13px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}
.listing-style1 hr {
  background-color: #DDDDDD;
  opacity: 1;
}
.listing-style1:hover .list-title {
  text-decoration: underline;
}
.listing-style1:hover .list-tag {
  opacity: 0;
  visibility: hidden;
  transform: translateY(50px);
}
.listing-style1:hover .list-thumb img {
  transform: scale(1.1) rotate(-1deg);
}
.listing-style1.style2 {
  box-shadow: none;
  border: 1px solid #DDDDDD;
}
.listing-style1.style3 {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.listing-style1.style4 {
  box-shadow: none;
}
.listing-style1.style4 .list-thumb {
  border-radius: 6px;
  overflow: hidden;
}
.listing-style1.style4 .list-content {
  padding-left: 0;
  padding-right: 0;
}
.listing-style1.listing-type {
  border-radius: 12px;
  display: flex;
}
@media (max-width: 767.98px) {
  .listing-style1.listing-type {
    display: block;
  }
}
.listing-style1.listing-type .list-content {
  padding: 30px;
}
.listing-style1.listing-type .list-text2 {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-size: 13px;
  font-weight: normal;
}
.listing-style1.listing-type img {
  height: 100%;
}
.listing-style1.style12 {
  border-radius: 12px;
}
@media (max-width: 991.98px) {
  .listing-style1.style12 .list-agent {
    right: 0px;
  }
  .listing-style1.style12 .list-agent img {
    width: 70px;
  }
}
.listing-style1.sidebar-style1 {
  box-shadow: none;
}
.listing-style1.sidebar-style1 .list-thumb {
  border-radius: 6px;
}
.listing-style1.sidebar-style1 .list-meta a {
  margin-right: 10px;
}
.listing-style1.mini-style {
  border-radius: 12px;
  -webkit-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  left: 40px;
  max-width: 305px;
  position: absolute;
  top: -223px;
  width: 100%;
}
@media (max-width: 991.98px) {
  .listing-style1.mini-style {
    position: relative;
    top: 0;
  }
}
@media (max-width: 425px) {
  .listing-style1.mini-style {
    left: 0;
  }
}
.listing-style1.mini-style .btn {
  padding: 0;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.listing-style1.mini-style .btn i {
  margin-left: 10px;
  transform: rotate(-30deg);
}
.listing-style1.mini-style .list-content {
  padding: 30px;
}
.listing-style1.mini-style {
  border-radius: 12px;
  -webkit-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  left: 40px;
  max-width: 305px;
  position: absolute;
  top: -223px;
  width: 100%;
}
@media (max-width: 991.98px) {
  .listing-style1.mini-style {
    position: relative;
    top: 0;
  }
}
@media (max-width: 425px) {
  .listing-style1.mini-style {
    left: 0;
  }
}
.listing-style1.mini-style .btn {
  padding: 0;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.listing-style1.mini-style .btn i {
  margin-left: 10px;
  transform: rotate(-30deg);
}
.listing-style1.mini-style .list-content {
  padding: 30px;
}
.listing-style1.at-home9-hero .list-price {
  background-color: #EB6753;
  bottom: 17px;
  color: #ffffff;
  display: inline-block;
  left: 0;
  max-width: 127px;
  position: relative;
}
.listing-style1.at-home9-hero .list-price span {
  font-weight: normal;
}
.listing-style1.at-home9-hero .list-content {
  padding: 35px 20px 20px;
}
.listing-style1.dashboard-style {
  box-shadow: none;
}
.listing-style1.dashboard-style .list-thumb {
  border-radius: 12px;
  height: 90px;
  max-width: 110px;
}
.listing-style1.dashboard-style img {
  border-radius: 12px;
}
.listing-style1.dashboard-style .list-text {
  margin-bottom: 0px;
}
.listing-style1.dashboard-style .list-price {
  bottom: 0;
  left: 0;
  position: relative;
  padding: 0;
}

.listing-style5 {
  margin-bottom: 30px;
  position: relative;
}
.listing-style5 .list-thumb {
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}
.listing-style5 img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style5 .list-tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 20px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style5 .list-price {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
}
.listing-style5 .list-price span {
  font-weight: normal;
}
.listing-style5 .list-meta a {
  font-family: var(--title-font-family);
  font-size: 13px;
  margin-right: 12px;
}
.listing-style5 .list-meta a:last-child {
  margin-right: 0;
}
.listing-style5 .list-meta span {
  margin-right: 6px;
}
.listing-style5 .list-meta2 {
  bottom: 10px;
  position: absolute;
  right: 20px;
  transform: translateY(125px);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style5 .list-meta2 a {
  background-color: rgba(24, 26, 32, 0.9);
  border-radius: 6px;
  color: #ffffff;
  display: inline-block;
  height: 35px;
  line-height: 38px;
  text-align: center;
  width: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  margin-bottom: 10px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style5 .list-meta2 a:last-child {
  margin-bottom: 0;
}
.listing-style5 .list-meta2 a:hover {
  background-color: rgba(235, 103, 83, 0.9);
  color: #ffffff;
}
.listing-style5 .list-content {
  padding: 20px 0 0;
}
.listing-style5 .list-title {
  margin-bottom: 2px;
}
.listing-style5 .list-title a {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style5 .list-title a:hover {
  color: var(--headings-color);
}
.listing-style5 .list-text {
  color: #717171;
  margin-bottom: 0;
}
.listing-style5:hover .list-tag {
  opacity: 0;
  visibility: hidden;
  transform: translateY(50px);
}
.listing-style5:hover .list-thumb img {
  transform: scale(1.1) rotate(-1deg);
}
.listing-style5:hover .list-meta2 {
  transform: translateY(0px);
}

.listing-style6 {
  margin-bottom: 30px;
  position: relative;
}
.listing-style6 .list-thumb {
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}
.listing-style6 img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style6 img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style6 .list-tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 20px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style6 .list-tag2 {
  background-color: #181A20;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 135px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style6 .list-meta {
  position: absolute;
  right: 10px;
  top: 20px;
  transform: translateX(45px);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style6 .list-meta a {
  background-color: rgba(24, 26, 32, 0.9);
  border: 1px solid var(--headings-color);
  border-radius: 6px;
  color: #ffffff;
  display: block;
  height: 35px;
  line-height: 40px;
  text-align: center;
  width: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  margin-bottom: 10px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style6 .list-meta a:last-child {
  margin-bottom: 0;
}
.listing-style6 .list-meta a:hover {
  background-color: rgba(235, 103, 83, 0.9);
  border: 1px solid #EB6753;
  color: #ffffff;
}
.listing-style6 .list-price {
  background-color: #ffffff;
  border: 1px solid #181A20;
  border-radius: 6px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  right: 20px;
  top: 18px;
  padding: 8px 15px 8px 15px;
  position: absolute;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .listing-style6 .list-price {
    display: inline-block;
    left: 0;
    position: relative;
    top: 0;
  }
}
.listing-style6 .list-content {
  background-color: #ffffff;
  border-radius: 12px;
  bottom: 10px;
  left: 10px;
  padding: 20px 20px 10px;
  position: absolute;
  right: 10px;
}
@media (max-width: 360px) {
  .listing-style6 .list-content {
    border: 1px solid #DDDDDD;
    left: 0;
    position: relative;
    right: 0;
  }
}
.listing-style6 .list-title {
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.listing-style6 .list-title a {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style6 .list-title a:hover {
  color: var(--headings-color);
}
.listing-style6 .list-text {
  color: #717171;
  margin-bottom: 0;
}
.listing-style6:hover .list-tag {
  opacity: 0;
  visibility: hidden;
  transform: translateY(50px);
}
.listing-style6:hover .list-meta {
  transform: translateX(0px);
}
.listing-style6:hover .list-price {
  background-color: var(--headings-color);
  color: #ffffff;
}
.listing-style6:hover .list-thumb img {
  transform: scale(1.1) rotate(-1deg);
}

.listing-style7 {
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  border-radius: 12px;
  margin-bottom: 30px;
  overflow: hidden;
  position: relative;
}
.listing-style7 .list-thumb {
  overflow: hidden;
  position: relative;
}
.listing-style7 img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style7 .list-tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 20px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style7 .list-tag2 {
  background-color: #181A20;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 135px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style7 .list-meta {
  bottom: 10px;
  position: absolute;
  right: 20px;
  transform: translateY(125px);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style7 .list-meta a {
  background-color: #ffffff;
  border-radius: 6px;
  color: var(--headings-color);
  display: inline-block;
  height: 35px;
  line-height: 38px;
  text-align: center;
  width: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  margin-bottom: 10px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style7 .list-meta a:last-child {
  margin-bottom: 0;
}
.listing-style7 .list-meta a:hover {
  background-color: rgba(235, 103, 83, 0.9);
  color: #ffffff;
}
.listing-style7 .list-meta2 a {
  font-family: var(--title-font-family);
  font-size: 13px;
  font-weight: normal;
}
.listing-style7 .list-price {
  color: #EB6753;
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style7 .list-price span {
  font-weight: normal;
}
.listing-style7 .list-content {
  background-color: #ffffff;
  padding: 20px;
}
.listing-style7 .list-title {
  margin-bottom: 10px;
}
.listing-style7 .list-title a {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style7 .list-title a:hover {
  color: var(--headings-color);
  text-decoration: underline;
}
.listing-style7 .list-text {
  color: #717171;
  margin-bottom: 0;
}
.listing-style7:hover .list-tag {
  opacity: 0;
  visibility: hidden;
  transform: translateY(50px);
}
.listing-style7:hover .list-meta {
  transform: translateY(0px);
}
.listing-style7:hover .list-thumb img {
  transform: scale(1.1) rotate(-1deg);
}
.listing-style7.at-home10 .list-price {
  color: var(--headings-color);
}

.listing-style8 {
  margin-bottom: 30px;
  overflow: hidden;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style8 .list-thumb {
  overflow: hidden;
  position: relative;
}
.listing-style8 img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style8 .list-tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 20px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style8 .list-tag2 {
  background-color: #181A20;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 135px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style8 .list-meta {
  bottom: 10px;
  position: absolute;
  right: 20px;
  transform: translateY(125px);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style8 .list-meta a {
  background-color: rgba(24, 26, 32, 0.9);
  border-radius: 6px;
  color: #ffffff;
  display: inline-block;
  height: 35px;
  line-height: 38px;
  text-align: center;
  width: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  margin-bottom: 10px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style8 .list-meta a:last-child {
  margin-bottom: 0;
}
.listing-style8 .list-meta a:hover {
  background-color: rgba(235, 103, 83, 0.9);
  color: #ffffff;
}
.listing-style8 .list-meta2 a {
  font-size: 13px;
  font-weight: normal;
  font-family: var(--title-font-family);
}
.listing-style8 .list-price {
  background-color: #ffffff;
  border: 1px solid #181A20;
  border-radius: 0;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  padding: 8px 15px 8px 15px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style8 .list-content {
  background-color: #ffffff;
  padding: 20px;
  position: relative;
}
.listing-style8 .list-title {
  margin-bottom: 2px;
}
.listing-style8 .list-title a {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style8 .list-title a:hover {
  color: var(--headings-color);
  text-decoration: underline;
}
.listing-style8 .list-text {
  color: #717171;
  margin-bottom: 0;
}
.listing-style8:hover {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.listing-style8:hover .list-tag,
.listing-style8:hover .list-tag2 {
  opacity: 0;
  visibility: hidden;
  transform: translateY(50px);
}
.listing-style8:hover .list-price {
  background-color: #181A20;
  color: #ffffff;
}
.listing-style8:hover .list-meta {
  transform: translateY(0px);
}
.listing-style8:hover .list-thumb img {
  transform: scale(1.1) rotate(-1deg);
}

.listing-style9 {
  border-radius: 12px;
  margin-bottom: 30px;
  overflow: hidden;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style9 .list-thumb {
  overflow: hidden;
  position: relative;
}
.listing-style9 img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style9 .list-tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 20px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style9 .list-tag2 {
  background-color: #181A20;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 135px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style9 .list-meta {
  position: absolute;
  right: 10px;
  top: 20px;
  transform: translateX(45px);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style9 .list-meta a {
  background-color: rgba(24, 26, 32, 0.9);
  border: 1px solid var(--headings-color);
  border-radius: 6px;
  color: #ffffff;
  display: block;
  height: 35px;
  line-height: 40px;
  text-align: center;
  width: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  margin-bottom: 10px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style9 .list-meta a:last-child {
  margin-bottom: 0;
}
.listing-style9 .list-meta a:hover {
  background-color: rgba(235, 103, 83, 0.9);
  border: 1px solid #EB6753;
  color: #ffffff;
}
.listing-style9 .list-meta2 a {
  color: #ffffff;
  font-size: 13px;
  font-weight: normal;
  font-family: var(--title-font-family);
}
.listing-style9 .list-price {
  color: #ffffff;
  font-size: 15px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style9 .list-content {
  bottom: 0;
  padding: 30px;
  position: absolute;
  width: 100%;
}
.listing-style9 .list-title {
  margin-bottom: 2px;
  color: #ffffff;
}
.listing-style9 .list-title a {
  color: #ffffff;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style9 .list-title a:hover {
  color: #ffffff;
}
.listing-style9:hover {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.listing-style9:hover .list-tag,
.listing-style9:hover .list-tag2 {
  opacity: 0;
  visibility: hidden;
  transform: translateY(50px);
}
.listing-style9:hover .list-meta {
  transform: translateY(0px);
}
.listing-style9:hover .list-thumb img {
  transform: scale(1.1) rotate(-1deg);
}

.listing-style10 {
  border-radius: 12px;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  margin-bottom: 30px;
  overflow: hidden;
  position: relative;
}
.listing-style10 .list-thumb {
  overflow: hidden;
  position: relative;
}
.listing-style10 .list-thumb:before {
  background: linear-gradient(180deg, rgba(24, 26, 32, 0) 0%, #181A20 100%);
  border-radius: 12px 12px 0px 0px;
  bottom: 0;
  content: "";
  left: 0;
  opacity: 0.7;
  position: absolute;
  right: 0;
  top: 0;
}
.listing-style10 .list-tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 20px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style10 .list-tag2 {
  background-color: #181A20;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  left: 135px;
  opacity: 1;
  padding: 2px 12px;
  position: absolute;
  top: 20px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style10 .list-meta {
  bottom: 10px;
  position: absolute;
  right: 20px;
  transform: translateY(125px);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style10 .list-meta a {
  background-color: rgba(24, 26, 32, 0.9);
  border-radius: 6px;
  color: #ffffff;
  display: inline-block;
  height: 35px;
  line-height: 38px;
  text-align: center;
  width: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  margin-bottom: 10px;
  background-color: rgba(24, 26, 32, 0.7);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style10 .list-meta a:last-child {
  margin-bottom: 0;
}
.listing-style10 .list-meta a:hover {
  background-color: rgba(235, 103, 83, 0.9);
  color: #ffffff;
}
.listing-style10 .list-meta2 a {
  font-size: 13px;
  font-weight: normal;
  font-family: var(--title-font-family);
}
.listing-style10 .list-price {
  bottom: 20px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  left: 20px;
  position: absolute;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style10 .list-price span {
  font-weight: normal;
}
.listing-style10 .list-content {
  padding: 20px;
}
.listing-style10 .list-title {
  margin-bottom: 2px;
}
.listing-style10 .list-title a {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style10 .list-title a:hover {
  color: var(--headings-color);
}
.listing-style10 .list-text {
  color: #717171;
  margin-bottom: 10px;
}
.listing-style10:hover .list-title {
  text-decoration: underline;
}
.listing-style10:hover .list-tag,
.listing-style10:hover .list-tag2 {
  opacity: 0;
  visibility: hidden;
  transform: translateY(50px);
}
.listing-style10:hover .list-meta {
  transform: translateY(0px);
}

.listing-style11 {
  background-color: #F4EFF7;
  border-radius: 12px;
  margin: 50px 0;
  padding: 70px 60px;
  position: relative;
}
@media (max-width: 991.98px) {
  .listing-style11 {
    padding: 40px 30px;
  }
}
.listing-style11 .list-thumb .img-1 {
  border-radius: 12px;
  position: absolute;
  right: 0;
  top: -50px;
  max-width: 560px;
  max-height: 610px;
}
@media (max-width: 1199.98px) {
  .listing-style11 .list-thumb .img-1 {
    position: relative;
    top: auto;
  }
}
.listing-style11 .list-tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  opacity: 1;
  padding: 2px 12px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style11 .list-tag2 {
  background-color: #181A20;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  opacity: 1;
  padding: 2px 12px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style11 .list-price span {
  font-weight: normal;
}
.listing-style11 .list-meta a {
  font-size: 15px;
  margin-right: 25px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
.listing-style11 .list-meta a:last-child {
  margin-right: 0;
}
@media (max-width: 320px) {
  .listing-style11 .list-meta a {
    margin-right: 15px;
  }
}
.listing-style11 .list-meta span {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -moz-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -o-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  color: var(--headings-color);
  display: block;
  font-size: 17px;
  height: 50px;
  line-height: 50px;
  margin-right: 10px;
  text-align: center;
  width: 50px;
}
.listing-style11 .list-meta2 a {
  background-color: #ffffff;
  border-radius: 50%;
  color: var(--headings-color);
  height: 40px;
  line-height: 45px;
  margin-right: 10px;
  text-align: center;
  width: 40px;
}
.listing-style11 .list-meta2 a:last-child {
  margin-right: 0;
}
.listing-style11 .list-title {
  margin-bottom: 8px;
}
.listing-style11 .list-title a {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style11 .list-title a:hover {
  color: var(--headings-color);
}
.listing-style11 .list-text {
  color: #717171;
  margin-bottom: 10px;
}
.listing-style11 .for-what {
  font-size: 13px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}

.listing-style13 {
  margin-bottom: 30px;
  overflow: hidden;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style13 .list-thumb {
  overflow: hidden;
  position: relative;
}
.listing-style13 .list-thumb:before {
  background: linear-gradient(180deg, rgba(24, 26, 32, 0) 0%, #181A20 100%);
  bottom: 0;
  content: "";
  left: 0;
  opacity: 0.5;
  position: absolute;
  right: 0;
  top: 0;
}
.listing-style13 img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 320px) {
  .listing-style13 img {
    height: 250px;
  }
}
.listing-style13 .list-title {
  font-size: 28px;
}
@media (max-width: 767.98px) {
  .listing-style13 .list-title {
    font-size: 20px;
  }
}
.listing-style13 .list-meta a {
  color: #ffffff;
  font-size: 15px;
  font-weight: normal;
  font-family: var(--title-font-family);
}
@media (max-width: 767.98px) {
  .listing-style13 .list-meta a {
    font-size: 13px;
  }
}
.listing-style13 .list-price {
  color: #ffffff;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 767.98px) {
  .listing-style13 .list-price {
    font-size: 16px;
  }
}
.listing-style13 .list-content {
  bottom: 0;
  padding-bottom: 60px;
  padding-left: 60px;
  position: absolute;
  width: 100%;
}
@media (max-width: 767.98px) {
  .listing-style13 .list-content {
    padding: 30px;
  }
}
@media (max-width: 375px) {
  .listing-style13 .list-content {
    padding: 30px 15px;
  }
}
.listing-style13 .list-content .ud-btn {
  border-radius: 0;
  position: absolute;
  right: 30px;
  top: 30px;
}
@media (max-width: 767.98px) {
  .listing-style13 .list-content .ud-btn {
    position: relative;
    right: auto;
    top: 15px;
  }
}
.listing-style13 .list-title {
  color: #ffffff;
}
.listing-style13 .list-title a {
  color: #ffffff;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style13 .list-title a:hover {
  color: #ffffff;
}
.listing-style13:hover .list-thumb img {
  transform: scale(1.1) rotate(-1deg);
}

.listing-style14 {
  border-radius: 12px;
  padding: 10px 80px 90px 0;
  position: relative;
}
.listing-style14 .list-thumb .img-1 {
  border-radius: 12px;
  position: absolute;
  right: 0;
  top: -50px;
  max-width: 560px;
  max-height: 610px;
}
@media (max-width: 1199.98px) {
  .listing-style14 .list-thumb .img-1 {
    position: relative;
    top: auto;
  }
}
.listing-style14 .list-tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  opacity: 1;
  padding: 2px 12px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style14 .list-tag2 {
  background-color: #181A20;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  opacity: 1;
  padding: 2px 12px;
  transform: translateY(0px);
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style14 .list-price span {
  font-weight: normal;
}
.listing-style14 .list-meta a {
  font-size: 15px;
  margin-right: 25px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
.listing-style14 .list-meta a:last-child {
  margin-right: 0;
}
@media (max-width: 320px) {
  .listing-style14 .list-meta a {
    margin-right: 15px;
  }
}
.listing-style14 .list-meta span {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -moz-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -o-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  color: var(--headings-color);
  display: block;
  font-size: 17px;
  height: 50px;
  line-height: 50px;
  margin-right: 10px;
  text-align: center;
  width: 50px;
}
.listing-style14 .list-meta2 a {
  background-color: #ffffff;
  border-radius: 50%;
  color: var(--headings-color);
  height: 40px;
  line-height: 45px;
  margin-right: 10px;
  text-align: center;
  width: 40px;
}
.listing-style14 .list-meta2 a:last-child {
  margin-right: 0;
}
.listing-style14 .list-title {
  margin-bottom: 8px;
}
.listing-style14 .list-title a {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.listing-style14 .list-title a:hover {
  color: var(--headings-color);
}
.listing-style14 .list-text {
  color: #717171;
  margin-bottom: 10px;
}
.listing-style14 .for-what {
  font-size: 13px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}

.apartment-style1 {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  margin-bottom: 30px;
  overflow: hidden;
  position: relative;
}
.apartment-style1 .apartment-content {
  padding: 20px;
}

.apartment-style2 {
  position: relative;
}
.apartment-style2 .apartment-content {
  bottom: 0;
  left: 0;
  position: absolute;
  padding-top: 40px;
  right: 0;
  top: 0;
}

.pagination_page_count {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}

.page_control_shorting {
  position: relative;
}
.page_control_shorting .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: auto;
}
.page_control_shorting .dropdown-item.active, .page_control_shorting .dropdown-item:focus {
  background-color: rgba(235, 103, 83, 0.07);
  color: var(--headings-color);
}
.page_control_shorting .pcs_dropdown span {
  color: #717171;
  margin-right: -8px;
}
.page_control_shorting .btn.btn-light {
  background-color: transparent;
  border: none;
  height: auto;
  width: auto;
  outline: none !important;
}
.page_control_shorting .btn.btn-light:focus {
  box-shadow: none;
  outline: none !important;
}
.page_control_shorting a {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.page_control_shorting a:hover {
  font-weight: 500;
  text-decoration: underline;
}

.half_map_area_content {
  height: 850px;
  overflow-x: hidden;
  overflow-y: scroll;
  padding: 0 15px 20px;
}
.half_map_area_content::-webkit-scrollbar {
  background: transparent;
  border-radius: 3px;
  padding-right: 10px;
  width: 5px;
}
.half_map_area_content::-webkit-scrollbar-thumb {
  background-color: #F7F7F7;
  border-radius: 3px;
  height: 200px;
}

.map-canvas.half_style {
  bottom: 0;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
}
@media (max-width: 1199.98px) {
  .map-canvas.half_style {
    height: 400px;
    position: relative;
  }
}

/* Property Signle CSS */
.property-action .icon {
  border: 1px solid #DDDDDD;
  border-radius: 6px;
  display: block;
  height: 35px;
  line-height: 35px;
  text-align: center;
  width: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.property-action .icon:hover {
  border: 1px solid #181A20;
  color: #181A20;
}
.property-action .price {
  font-size: 26px;
  letter-spacing: 0.02em;
}
.property-action .space {
  letter-spacing: 0.02em;
}
.property-action.dark-version .icon {
  color: #ffffff;
}
.property-action.dark-version .icon:hover {
  background-color: #ffffff;
  color: var(--headings-color);
}

.sp-img-content {
  position: relative;
}
.sp-img-content .preview-img-1 {
  border-radius: 12px 0 0 12px;
  height: 558px;
}
@media (max-width: 1199.98px) {
  .sp-img-content .preview-img-1 {
    height: 430px;
  }
}
@media (max-width: 991.98px) {
  .sp-img-content .preview-img-1 {
    height: 320px;
  }
}
@media (max-width: 767.98px) {
  .sp-img-content .preview-img-1 {
    height: 240px;
  }
}
@media (max-width: 575.98px) {
  .sp-img-content .preview-img-1 {
    border-radius: 12px 12px 0 0;
    height: auto;
  }
}
@media (max-width: 991.98px) {
  .sp-img-content.at-sp-v2 .preview-img-1 {
    height: 300px;
  }
}
@media (max-width: 767.98px) {
  .sp-img-content.at-sp-v2 .preview-img-1 {
    height: 215px;
  }
}
@media (max-width: 575.98px) {
  .sp-img-content.at-sp-v2 .preview-img-1 {
    height: auto;
  }
}
@media (max-width: 575.98px) {
  .sp-img-content.at-sp-v2 .preview-img-5 {
    border-radius: 0 0 12px 12px;
  }
}
@media (max-width: 575.98px) {
  .sp-img-content.at-sp-v3 .preview-img-4 {
    border-radius: 0;
  }
}
.sp-img-content.at-sp-v10 .preview-img-1 {
  height: 680px;
}
@media (max-width: 991.98px) {
  .sp-img-content.at-sp-v10 .preview-img-1 {
    height: 455px;
  }
}
@media (max-width: 767.98px) {
  .sp-img-content.at-sp-v10 .preview-img-1 {
    height: 330px;
  }
}
@media (max-width: 575.98px) {
  .sp-img-content.at-sp-v10 .preview-img-1 {
    border-radius: 12px 12px 0 0;
    height: auto;
  }
}
.sp-img-content.at-sp-v10 .preview-img-2 {
  border-radius: 0 12px 0 0;
  height: 220px;
}
@media (max-width: 991.98px) {
  .sp-img-content.at-sp-v10 .preview-img-2 {
    height: auto;
  }
}
@media (max-width: 575.98px) {
  .sp-img-content.at-sp-v10 .preview-img-2 {
    border-radius: 0;
  }
}
.sp-img-content.at-sp-v10 .preview-img-3 {
  height: 220px;
  border-radius: 0;
}
@media (max-width: 991.98px) {
  .sp-img-content.at-sp-v10 .preview-img-3 {
    height: auto;
  }
}
.sp-img-content.at-sp-v10 .preview-img-4 {
  height: 220px;
  border-radius: 0 0 12px 0;
}
@media (max-width: 991.98px) {
  .sp-img-content.at-sp-v10 .preview-img-4 {
    height: auto;
  }
}
@media (max-width: 575.98px) {
  .sp-img-content.at-sp-v10 .preview-img-4 {
    border-radius: 0 0 12px 12px;
  }
}
.sp-img-content .preview-img-3 {
  border-radius: 0 12px 0 0;
}
@media (max-width: 575.98px) {
  .sp-img-content .preview-img-3 {
    border-radius: 0;
  }
}
@media (max-width: 575.98px) {
  .sp-img-content .preview-img-4 {
    border-radius: 0 0 0 12px;
  }
}
.sp-img-content .preview-img-5 {
  border-radius: 0 0 12px 0;
}
.sp-img-content .all-tag {
  background-color: #ffffff;
  border-radius: 6px;
  bottom: 20px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  display: block;
  padding: 7px 20px;
  position: absolute;
  right: 20px;
}
@media (max-width: 575.98px) {
  .sp-img-content .all-tag {
    padding: 6px 8px;
    font-size: 11px;
  }
}
.sp-img-content .all-tag.style2 {
  border-radius: 50%;
  height: 65px;
  line-height: 55px;
  left: 0;
  margin: 0 auto;
  right: 0;
  top: 40%;
  width: 65px;
}
.sp-img-content .sp-img {
  display: block;
  overflow: hidden;
  position: relative;
}
.sp-img-content .sp-img img {
  height: 100%;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.sp-img-content .sp-img:hover img {
  transform: scale(1.1) rotate(-1deg);
}

.overview-element {
  position: relative;
}
.overview-element .icon {
  border: 1px solid #DDDDDD;
  border-radius: 12px;
  color: var(--headings-color);
  display: block;
  font-size: 20px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  width: 50px;
}
.overview-element.dark-version .icon {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
}
.overview-element.dark-version .icon:hover {
  background-color: #ffffff;
  color: var(--headings-color);
}

.property_video {
  background-image: url(../images/listings/listing-single-6.jpg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  align-items: center;
  display: flex;
  height: 350px;
  position: relative;
}
.property_video .video_popup_btn {
  background-color: #ffffff;
  border-radius: 50%;
  color: var(--headings-color);
  display: block;
  height: 54px;
  line-height: 54px;
  outline: 1px solid #ffffff;
  outline-offset: 15px;
  text-align: center;
  width: 54px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.property_video .video_popup_btn:hover {
  outline-offset: 7px;
}

.nearby {
  position: relative;
}
.nearby .rating {
  background-color: #ffffff;
  border-radius: 50%;
  display: block;
  height: 44px;
  line-height: 44px;
  outline: 3px solid #C4C640;
  text-align: center;
  width: 44px;
}
.nearby .rating.style2 {
  outline: 3px solid #EB6753;
}
.nearby .blog-single-review {
  top: 13px;
}
@media (max-width: 767.98px) {
  .nearby .blog-single-review {
    top: 0;
  }
}

.walkscore {
  position: relative;
}
.walkscore .icon {
  background-color: rgba(235, 103, 83, 0.07);
  border-radius: 50%;
  color: #EB6753;
  display: block;
  font-size: 30px;
  height: 70px;
  line-height: 80px;
  text-align: center;
  width: 70px;
}

.calculator-chart-percent {
  border-radius: 12px;
  height: 12px;
  overflow: hidden;
}
.calculator-chart-percent .principal-interest-st,
.calculator-chart-percent .property-tax-st,
.calculator-chart-percent .home-insurance-st {
  height: 12px;
}
.calculator-chart-percent .principal-interest-st {
  background-color: #82DDD0;
  width: 56.2748%;
}
.calculator-chart-percent .property-tax-st {
  background-color: #80A1CC;
  width: 32.7942%;
}
.calculator-chart-percent .home-insurance-st {
  background-color: #F5DD86;
  width: 10.931%;
}

.list-result-calculator li {
  width: calc(50% - 30px);
  margin-bottom: 15px;
}
.list-result-calculator li:before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #82DDD0;
  margin-right: 12px;
}
@media (max-width: 767.98px) {
  .list-result-calculator li {
    width: auto;
  }
}
.list-result-calculator .name-result + span {
  color: var(--headings-color);
  margin-left: auto;
}

.chart-container {
  position: relative;
  margin: auto;
  height: 100%;
  width: 100%;
}

.sp-v5-property-details {
  margin-top: -180px;
  position: relative;
  z-index: 1;
}
.sp-v5-property-details .sp-lg-title,
.sp-v5-property-details .text,
.sp-v5-property-details .price,
.sp-v5-property-details a {
  color: #ffffff;
}
@media (max-width: 1199.98px) {
  .sp-v5-property-details .sp-lg-title,
.sp-v5-property-details .text,
.sp-v5-property-details .price,
.sp-v5-property-details a {
    color: inherit;
  }
}
.sp-v5-property-details .icon:hover {
  background-color: #EB6753;
  border-color: #EB6753;
  color: #ffffff;
}
@media (max-width: 1199.98px) {
  .sp-v5-property-details {
    margin-top: 0;
  }
}

.tab-map-sp-v5 {
  position: relative;
}
.tab-map-sp-v5:before {
  background-color: rgba(24, 26, 32, 0.3);
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}
@media (max-width: 1199.98px) {
  .tab-map-sp-v5:before {
    display: none;
  }
}

.sp-v9-forn {
  left: 20px;
  max-width: 390px;
  position: absolute;
  top: 20px;
  width: 100%;
}

/* Hero Styles All Type Of */
.home-banner-style1 {
  background-image: url(../images/home/<USER>
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
}
.home-banner-style1 .home-style1 {
  align-items: center;
  display: flex;
  height: 860px;
}
@media (max-width: 991.98px) {
  .home-banner-style1 .home-style1 {
    height: 500px;
  }
}

.inner-banner-style1 .hero-sub-title {
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 400;
}
.inner-banner-style1 .hero-title {
  color: #ffffff;
  font-size: 52px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
@media (max-width: 767.98px) {
  .inner-banner-style1 .hero-title {
    font-size: 36px;
  }
}
.inner-banner-style1 .hero-text {
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 400;
}

.advance-content-style1 {
  position: relative;
}
.advance-content-style1 .advance-search-btn {
  background-color: transparent;
  border: none;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.advance-content-style1 .advance-search-btn span {
  padding-right: 10px;
  vertical-align: middle;
}
.advance-content-style1 .advance-search-icon {
  border-radius: 50%;
  display: block;
  height: 55px;
  line-height: 55px;
  padding: 0;
  text-align: center;
  width: 55px;
}
.advance-content-style1.at-home8 .dropdown-menu li:first-child {
  display: none;
}

.advance-content-style1 .form-control,
.advance-content-style3 .form-control {
  border: none;
}
.advance-content-style1 .form-control:focus,
.advance-content-style3 .form-control:focus {
  background-color: #F7F7F7;
  border: 1px solid transparent;
  outline: none;
  box-shadow: none;
}

/*Home 2 Banner Content*/
.home2-hero-banner {
  background-image: url(../images/home/<USER>
  background-position: center center;
  background-repeat: no-repeat;
  height: 650px;
  max-width: 1600px;
  position: absolute;
  top: 200px;
  width: 100%;
}
@media (max-width: 991.98px) {
  .home2-hero-banner {
    top: 100px;
  }
}

.inner-banner-style2 .hero-title {
  color: #ffffff;
  font-size: 46px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
@media (max-width: 767.98px) {
  .inner-banner-style2 .hero-title {
    font-size: 36px;
  }
}
.inner-banner-style2 .hero-text {
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 400;
}

.home-banner-style2 {
  background-color: #181A20;
  position: relative;
}
.home-banner-style2 .home-style2 {
  align-items: start;
  display: flex;
  height: 650px;
}
@media (max-width: 991.98px) {
  .home-banner-style2 .home-style2 {
    height: 500px;
  }
}

.advance-content-style2 {
  position: relative;
}
.advance-content-style2 .form-control {
  border: none;
}
.advance-content-style2 .form-control:focus {
  border: none;
  outline: none;
  box-shadow: none;
}
.advance-content-style2 .bootselect-multiselect .btn {
  background-color: transparent;
  border: none;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-size: 14px;
}
.advance-content-style2 .bootselect-multiselect .btn:hover, .advance-content-style2 .bootselect-multiselect .btn:focus {
  border: none;
  color: var(--headings-color);
}
.advance-content-style2 .bootselect-multiselect .btn::placehoder {
  color: var(--headings-color);
}
.advance-content-style2 .bootselect-multiselect .dropdown-menu {
  padding: 0;
}
.advance-content-style2 .advance-search-btn {
  background-color: transparent;
  border: none;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.advance-content-style2 .advance-search-btn span {
  padding-right: 10px;
  vertical-align: middle;
}
.advance-content-style2 .advance-search-icon {
  border-radius: 50%;
  display: block;
  height: 55px;
  line-height: 50px;
  padding: 0;
  text-align: center;
  width: 55px;
}

/*Home 3 Banner Content*/
.home-banner-style3 {
  background-image: url(../images/home/<USER>
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
}
.home-banner-style3 .home-style3 {
  align-items: center;
  display: flex;
  height: 760px;
}
@media (max-width: 991.98px) {
  .home-banner-style3 .home-style3 {
    height: 650px;
  }
}

.advance-content-style3 {
  position: relative;
}
.advance-content-style3 .bootselect-multiselect .btn,
.advance-content-style3 .bootselect-multiselect .btn-light {
  background-color: #F7F7F7;
  border: transparent;
  color: var(--headings-color);
}
.advance-content-style3 .bootselect-multiselect .btn:hover,
.advance-content-style3 .bootselect-multiselect .btn-light:hover {
  background-color: #F7F7F7;
  border: transparent;
  color: var(--headings-color);
}
.advance-content-style3 .bootselect-multiselect .btn:focus,
.advance-content-style3 .bootselect-multiselect .btn-light:focus {
  background-color: #DDDDDD;
}
.advance-content-style3 .bootselect-multiselect .dropdown-menu {
  border-radius: 6px;
  padding: 0;
}
.advance-content-style3 .bootselect-multiselect .dropdown-menu li {
  border-bottom: 1px solid #DDDDDD;
  font-size: 14px;
}
.advance-content-style3 .bootselect-multiselect .dropdown-menu li:last-child {
  border-bottom: transparent;
}
.advance-content-style3 .bootselect-multiselect .dropdown-menu li:first-child {
  display: none;
}
.advance-content-style3 .bootselect-multiselect .dropdown-menu .dropdown-item {
  color: var(--headings-color);
  padding-left: 20px;
  line-height: 40px;
}
.advance-content-style3 .bootselect-multiselect .dropdown-menu .inner {
  padding: 0;
}
.advance-content-style3 .advance-search-btn {
  background-color: transparent;
  border: none;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.advance-content-style3 .advance-search-btn span {
  padding-right: 10px;
  vertical-align: middle;
}
.advance-content-style3 .advance-search-icon {
  border-radius: 12px;
  display: block;
  height: 55px;
  line-height: 55px;
  padding: 0;
  text-align: center;
  width: 55px;
}
.advance-content-style3 .form-control:focus {
  background-color: #DDDDDD;
}
.advance-content-style3.at-home5 .form-control {
  background-color: #ffffff;
  height: auto;
}
.advance-content-style3.at-home5 .form-control:focus {
  border: none;
  outline: none;
}
.advance-content-style3.at-home5 .bootselect-multiselect .btn {
  background-color: #ffffff;
  border: none;
  font-size: 14px;
  height: auto;
  padding: 0;
}
.advance-content-style3.at-home7 .form-control:focus {
  background-color: #ffffff;
}
.advance-content-style3.at-home7 .bootselect-multiselect .btn {
  background-color: #ffffff;
  border: none;
  font-size: 14px;
  height: auto;
  padding: 0;
}

.inner-banner-style3 .hero-title {
  color: var(--headings-color);
  font-size: 40px;
  font-family: var(--title-font-family);
  font-weight: 600;
  max-width: 493px;
}
@media (max-width: 767.98px) {
  .inner-banner-style3 .hero-title {
    font-size: 30px;
  }
}
.inner-banner-style3 .hero-text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}

.home3-img-box1 {
  position: relative;
}
.home3-img-box1 .img-1 {
  bottom: auto;
  position: absolute;
  right: -10px;
  z-index: 1;
  top: -35px;
}
.home3-img-box1 .img-2 {
  bottom: auto;
  left: -300px;
  opacity: 0.1;
  position: absolute;
  right: auto;
  top: 550px;
  z-index: 1;
}
.home3-img-box1 .img-3 {
  bottom: auto;
  left: 330px;
  opacity: 0.1;
  position: absolute;
  right: auto;
  top: -80px;
}
.home3-img-box1 .img-4 {
  bottom: auto;
  left: 0;
  position: absolute;
  right: auto;
  top: 470px;
  z-index: 1;
}

/*Home 4 Banner Content*/
.home-style4 {
  background-image: url(../images/home/<USER>
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  align-items: center;
  display: flex;
  height: 660px;
}
@media (max-width: 991.98px) {
  .home-style4 {
    height: 620px;
  }
}

.inner-banner-style4 .hero-sub-title {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}
.inner-banner-style4 .hero-title {
  color: var(--headings-color);
  font-size: 40px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
@media (max-width: 991.98px) {
  .inner-banner-style4 .hero-title {
    font-size: 30px;
  }
}
.inner-banner-style4 .hero-text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}

.home4-icon-style i.icon {
  background-color: #ffffff;
  border-radius: 50%;
  color: var(--headings-color);
  display: block;
  font-size: 13px;
  height: 30px;
  line-height: 33px;
  text-align: center;
  width: 30px;
}

.home4-floatin-img {
  position: relative;
}
.home4-floatin-img .img-1 {
  position: absolute;
  right: 50px;
  top: -200px;
}
.home4-floatin-img .img-2 {
  position: absolute;
  right: -160px;
  top: -100px;
}
.home4-floatin-img .popup-iframe {
  position: absolute;
  right: -280px;
  top: -50px;
}
@media (max-width: 1199.98px) {
  .home4-floatin-img .popup-iframe {
    position: relative;
    right: auto;
    top: 10px;
  }
}
@media (max-width: 767.98px) {
  .home4-floatin-img .popup-iframe {
    top: 20px;
  }
}

/*Home 5 Slider Content*/
.slider-slide-item {
  align-items: center;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  height: 900px;
  position: relative;
}
.slider-slide-item:before {
  background-color: rgba(24, 26, 32, 0.6);
  bottom: 0;
  content: "";
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
}
@media (max-width: 1199.98px) {
  .slider-slide-item {
    height: 500px;
  }
}

.thumbimg-countnumber-carousel .slider-subtitle {
  font-family: var(--title-font-family);
  font-weight: 600;
  margin: 0 0 10px;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
@media (max-width: 991.98px) {
  .thumbimg-countnumber-carousel .slider-subtitle {
    font-size: 26px;
  }
}
.thumbimg-countnumber-carousel .slider-title {
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 52px;
  margin: 0;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
@media (max-width: 991.98px) {
  .thumbimg-countnumber-carousel .slider-title {
    font-size: 36px;
  }
}
.thumbimg-countnumber-carousel .slider-text {
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 15px;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
.thumbimg-countnumber-carousel .slider-btn {
  border-color: transparent;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
.thumbimg-countnumber-carousel .active .slider-subtitle {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
.thumbimg-countnumber-carousel .active .slider-title {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}
.thumbimg-countnumber-carousel .active .slider-text {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
.thumbimg-countnumber-carousel .active .slider-btn {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
  -webkit-animation-delay: 1.5s;
  animation-delay: 1.5s;
}

/*Home 6 Banner*/
.home-banner-style6 {
  background-image: url(../images/home/<USER>
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
}
.home-banner-style6 .home-style1 {
  align-items: center;
  display: flex;
  height: 760px;
}
@media (max-width: 991.98px) {
  .home-banner-style6 .home-style1 {
    height: 600px;
  }
}

.inner-banner-style6 .hero-sub-title {
  font-family: var(--title-font-family);
  font-weight: 400;
}
.inner-banner-style6 .hero-title {
  font-size: 46px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
@media (max-width: 991.98px) {
  .inner-banner-style6 .hero-title {
    font-size: 30px;
  }
}
.inner-banner-style6 .hero-text {
  font-family: var(--title-font-family);
  font-weight: 400;
}

/*Home 7 Banner*/
.home-banner-style7 {
  position: relative;
}
.home-banner-style7:before {
  background-color: #181A20;
  bottom: 0;
  content: "";
  height: 100%;
  max-width: 745px;
  max-height: 426px;
  position: absolute;
  right: 0;
  width: 100%;
}
@media (max-width: 1399.98px) {
  .home-banner-style7:before {
    max-width: 545px;
  }
}
@media (max-width: 1199.98px) {
  .home-banner-style7:before {
    max-width: 745px;
  }
}
@media (max-width: 520px) {
  .home-banner-style7:before {
    max-height: 226px;
  }
}

.home-style7 {
  position: relative;
  align-items: center;
  display: flex;
}

.inner-banner-style7 .hero-title {
  color: var(--headings-color);
  font-size: 40px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
@media (max-width: 991.98px) {
  .inner-banner-style7 .hero-title {
    font-size: 30px;
  }
}

.home7-main-slider .swiper-pagination-progressbar {
  background-color: rgba(255, 255, 255, 0.3);
}
.home7-main-slider .swiper-pagination-progressbar.swiper-pagination-horizontal {
  height: 1px;
  max-width: 287px;
}
@media (max-width: 375px) {
  .home7-main-slider .swiper-pagination-progressbar.swiper-pagination-horizontal {
    max-width: 230px;
  }
}
.home7-main-slider .swpr_paginations {
  left: 0;
  margin: 0 auto;
  right: 0;
  position: absolute;
  width: 100%;
  max-width: 330px;
}
.home7-main-slider .swpr_paginations .slideactive {
  left: 0;
}
.home7-main-slider .swpr_paginations .slidetotal {
  right: 0;
}
.home7-main-slider .swpr_paginations .slideactive,
.home7-main-slider .swpr_paginations .slidetotal {
  bottom: 0;
  font-family: var(--title-font-family);
  color: #ffffff;
  position: absolute;
}
.home7-main-slider .swiper-pagination-progressbar-fill {
  background-color: #ffffff;
  height: 3px;
  top: -1px;
}

.swpr_custom_prgrsba {
  background-color: #CEE2FF;
  bottom: 14px;
  height: 1px;
  left: 35px;
  max-width: 287px;
  position: absolute;
  top: auto;
}

.swpr_custom_prgrsba {
  left: 0;
  margin: 0 auto;
  max-width: 287px;
  position: absolute;
  right: 0;
}

/*Home 1 Banner Content*/
.home-banner-style8 {
  background-image: url(../images/home/<USER>
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}
.home-banner-style8 .home-style8 {
  align-items: center;
  display: flex;
  height: 860px;
}
@media (max-width: 1199.98px) {
  .home-banner-style8 .home-style8 {
    height: 600px;
  }
}

.inner-banner-style8 .hero-sub-title {
  font-family: var(--title-font-family);
  font-weight: 400;
}
.inner-banner-style8 .hero-title {
  font-size: 52px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
@media (max-width: 767.98px) {
  .inner-banner-style8 .hero-title {
    font-size: 36px;
  }
}
.inner-banner-style8 .hero-text {
  font-family: var(--title-font-family);
  font-weight: 400;
}

.home8-sidebar-wrapper {
  background-color: #ffffff;
  height: 100vh;
  overflow: auto;
  padding: 0;
  position: fixed;
  scrollbar-width: thin;
  top: 0;
  width: 70px;
  z-index: 99;
}

.property-banner-style1 {
  background-image: url(../images/listings/listing-banner-1.jpg);
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
}
.property-banner-style1 .inner-style1 {
  align-items: center;
  display: flex;
  height: 600px;
}

.property-banner-style2 {
  background-image: url(../images/listings/listing-banner-2.jpg);
  background-position: center center;
  background-repeat: no-repeat;
}
.property-banner-style2 .inner-style1 {
  align-items: center;
  display: flex;
  height: 500px;
}

/* About Pages Design Content Styles  */
.about-box-1 {
  position: relative;
}
.about-box-1 .list-style1 li {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  padding-left: 5px;
}
.about-box-1 .list-style1 i {
  height: 25px;
  line-height: 25px;
  width: 25px;
}

.about-box2 {
  background-color: rgba(235, 103, 83, 0.07);
  border-radius: 12px;
  margin-bottom: 30px;
  overflow: hidden;
  padding: 60px 60px 160px;
  position: relative;
}
@media (max-width: 991.98px) {
  .about-box2 {
    padding: 40px 30px;
  }
}
.about-box2 .title {
  font-size: 22px;
  line-height: 33px;
  letter-spacing: 0.02em;
}
.about-box2 .img-1 {
  position: absolute;
  right: 0;
  bottom: 0;
}
@media (max-width: 991.98px) {
  .about-box2 .img-1 {
    display: none;
  }
}

.exclusive-agent-widget {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  max-width: 345px;
  padding: 30px;
  position: relative;
}
.exclusive-agent-widget .title {
  font-family: var(--title-font-family);
  font-weight: 600;
}

.exclusive-agent-single {
  background-color: #ffffff;
  bottom: -90px;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  max-width: 250px;
  padding: 20px;
  position: absolute;
  right: 70px;
  bottom: -60px;
  right: 30px;
}
.exclusive-agent-single .agent-img {
  border-radius: 12px;
  overflow: hidden;
}
@media (max-width: 991.98px) {
  .exclusive-agent-single {
    bottom: -20px;
  }
}
@media (max-width: 767.98px) {
  .exclusive-agent-single {
    bottom: 0;
    position: relative;
    right: 0;
  }
}

.img-box-1 {
  vertical-align: bottom;
}
@media (max-width: 767.98px) {
  .img-box-1 {
    display: block;
    margin-bottom: 20px;
  }
}

.img-box-1 img,
.img-box-2 img,
.img-box-3 img,
.img-box-4 img {
  border-radius: 12px;
}

.img-box-2 {
  margin-left: 30px;
  margin-bottom: 50px;
}
@media (max-width: 767.98px) {
  .img-box-2 {
    display: block;
    margin-left: 0;
    margin-bottom: 0px;
  }
}

.img-box-3 {
  position: absolute;
  right: 0;
  left: 0;
  margin: 0 auto;
  text-align: center;
  bottom: -35px;
}
@media (max-width: 767.98px) {
  .img-box-3 {
    bottom: auto;
    top: 30%;
  }
}
.img-box-3 .img-1 {
  -webkit-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
}

.img-box-4 {
  position: absolute;
  right: -70px;
  bottom: 0;
  z-index: -1;
}

.img-box-5 {
  position: absolute;
  right: -70px;
  top: -70px;
  z-index: -1;
}

.img-box-6 {
  bottom: -70px;
  left: -70px;
  position: absolute;
  z-index: -1;
}

.img-box-7 img {
  border-radius: 12px;
}
.img-box-7 .img-1 {
  margin-top: -80px;
}
@media (max-width: 991.98px) {
  .img-box-7 .img-1 {
    margin-top: 0;
  }
}

.img-box-8 .img-1 {
  left: 130px;
  position: absolute;
  top: -150px;
  z-index: -1;
}
@media (max-width: 991.98px) {
  .img-box-8 .img-1 {
    left: 30px;
    top: -210px;
  }
}

.img-box-9 {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  margin-left: auto;
  max-width: 210px;
  padding: 20px;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 1;
}
.img-box-9 i {
  border-radius: 50%;
  display: block;
  font-size: 46px;
  width: 60px;
  height: 60px;
  line-height: 60px;
  outline: 1px solid var(--headings-color);
  outline-offset: 5px;
  text-align: center;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.img-box-9:hover i {
  outline-offset: 8px;
}

.img-box-10 .img-1 {
  bottom: 300px;
  opacity: 0.2;
  position: absolute;
  right: -100px;
}
.img-box-10 .img-2 {
  left: 230px;
  opacity: 0.2;
  position: absolute;
  top: 40px;
}
.img-box-10 .img-3 {
  left: -100px;
  opacity: 0.2;
  position: absolute;
}
.img-box-10 .img-4 {
  bottom: 300px;
  left: -100px;
  position: absolute;
}
.img-box-10 .img-5 {
  position: absolute;
  right: 0;
  top: 90px;
}

.img-box-11 .img-1 {
  bottom: -20px;
  left: -20px;
  opacity: 0.2;
  position: absolute;
  z-index: -1;
}
.img-box-11 .img-2 {
  opacity: 0.2;
  position: absolute;
  right: 0;
  top: 0;
}
.img-box-11 .img-3 {
  bottom: 100px;
  left: -100px;
  position: absolute;
}

.img-box-12 .img-1 {
  bottom: -20px;
  left: -20px;
  position: absolute;
}
.img-box-12 .img-2 {
  position: absolute;
  right: 0;
  top: 0;
}
.img-box-12 .img-3 {
  bottom: 100px;
  left: -100px;
  position: absolute;
}

/* ALl Blogs Styles Here  */
.blog-style1 {
  margin-bottom: 30px;
  position: relative;
}
.blog-style1 .blog-img {
  border-radius: 12px;
  overflow: hidden;
}
.blog-style1 .blog-img img {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.blog-style1 .blog-content {
  padding: 17px 20px 0 0;
  position: relative;
}
.blog-style1 .tag {
  color: #717171;
  font-size: 13px;
  font-family: var(--title-font-family);
  font-weight: 400;
  letter-spacing: 0em;
}
.blog-style1 .date {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -moz-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -o-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  height: 70px;
  line-height: 25px;
  padding-top: 10px;
  position: absolute;
  right: 20px;
  text-align: center;
  top: -50px;
  width: 70px;
}
.blog-style1 .day {
  font-size: 20px;
  display: block;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.blog-style1 .month {
  font-size: 13px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}
.blog-style1:hover .blog-img img {
  transform: scale(1.1) rotate(-1deg);
}
.blog-style1.large-size {
  border-radius: 12px;
  overflow: hidden;
}
.blog-style1.large-size .blog-img {
  border-radius: 0;
}
.blog-style1.list-style {
  border-radius: 12px;
  overflow: hidden;
}
.blog-style1.list-style .blog-img {
  border-radius: 0;
  position: relative;
}
.blog-style1.list-style img {
  height: 100%;
}
.blog-style1.list-style .date {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -moz-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -o-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  bottom: 10px;
  height: 70px;
  line-height: 25px;
  padding-top: 10px;
  position: absolute;
  left: 10px;
  top: auto;
  text-align: center;
  width: 70px;
}

.custom_bsp_grid {
  position: relative;
}
.custom_bsp_grid li {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 14px;
  line-height: 46px;
  list-style-type: disc;
}

.blog_post_share span {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  line-height: 23px;
}
.blog_post_share a {
  font-family: "Font Awesome 5 Brands";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #181A20;
}

.bsp_tags a {
  background-color: rgba(235, 103, 83, 0.07);
  border-radius: 60px;
  color: var(--headings-color);
  display: block;
  font-family: var(--body-font-family);
  font-weight: 500;
  font-size: 13px;
  line-height: 28px;
  padding: 6px 17px;
}

.blog-single-review {
  position: absolute;
  right: 0;
  top: 5px;
}
@media (max-width: 767.98px) {
  .blog-single-review {
    position: relative;
  }
}

.review_cansel_btns a {
  color: #717171;
  font-family: var(--title-font-family);
  line-height: 26px;
  letter-spacing: 0em;
  margin-right: 30px;
}
.review_cansel_btns a:last-child {
  margin-right: 0;
}
.review_cansel_btns a:hover {
  color: var(--headings-color);
}
.review_cansel_btns i {
  padding-right: 10px;
}

/* Brands */
/* Commons Styles of Template */
section {
  padding: 120px 0;
  position: relative;
}
@media (max-width: 991.98px) {
  section {
    padding: 60px 0;
  }
}

.main-title,
.main-title2 {
  position: relative;
  margin-bottom: 60px;
}
@media (max-width: 991.98px) {
  .main-title,
.main-title2 {
    margin-bottom: 30px;
  }
}
.main-title .title,
.main-title2 .title {
  font-style: normal;
  letter-spacing: 0.02em;
  margin-bottom: 0;
}
.main-title .paragraph,
.main-title2 .paragraph {
  color: var(--headings-color);
  font-family: var(--title-font-family);
}

@media (max-width: 991.98px) {
  .main-title2 {
    margin-bottom: 30px;
  }
}

.ui-content {
  position: relative;
  margin-bottom: 30px;
}
.ui-content .title {
  margin-bottom: 30px;
}

.mouse_scroll {
  bottom: 0;
  position: absolute;
  right: 50px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .mouse_scroll {
    right: 15px;
  }
}
@media (max-width: 575.98px) {
  .mouse_scroll {
    display: none;
  }
}
.mouse_scroll.at-home8 {
  bottom: 110px;
  right: 0;
  left: 0;
}

.list-style1 {
  position: relative;
}
.list-style1 li {
  align-items: center;
  display: flex;
  list-style-type: none;
  margin-bottom: 20px;
}
.list-style1 i {
  border-radius: 50%;
  font-size: 8px;
  height: 18px;
  left: 0;
  line-height: 18px;
  position: absolute;
  text-align: center;
  width: 18px;
}

/* Custom Search Sugguestions Code Start */
.advance-search-field .box-search .icon {
  bottom: 13px;
  color: var(--headings-color);
  font-size: 15px;
  left: 20px;
  position: absolute;
}
.advance-search-field .box-search input {
  border: transparent;
  border-radius: 12px;
  font-size: 14px;
  font-family: var(--title-font-family);
  padding-left: 50px;
}
.advance-search-field .box-search input::placeholder {
  color: var(--headings-color);
}
.advance-search-field .search-suggestions {
  background-color: #ffffff;
  border-radius: 12px;
  left: 0;
  overflow: hidden;
  opacity: 0;
  position: absolute;
  top: 50px;
  visibility: hidden;
  width: 100%;
  z-index: 99;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.advance-search-field .search-suggestions.show {
  opacity: 1;
  visibility: visible;
}
.advance-search-field .box-suggestions {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  width: 100%;
}
.advance-search-field .box-suggestions li {
  border-bottom: 1px solid #D9D9D9;
  overflow: hidden;
  padding: 18px 20px;
}
.advance-search-field .box-suggestions li:hover {
  background-color: rgba(235, 103, 83, 0.07);
}
.advance-search-field .box-suggestions li:last-child {
  border-bottom: none;
}
.advance-search-field .item_title {
  align-items: center;
  display: flex;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}

/* Custom Search Sugguestions Code End */
.preloader {
  background-color: #eaeaea;
  background-image: url("../images/preloader.gif");
  background-position: center center;
  background-repeat: no-repeat;
  height: 100%;
  left: 0px;
  position: fixed;
  top: 0px;
  width: 100%;
  z-index: 99999;
}

.scrollToHome {
  background-color: transparent;
  border: 1px solid #ffcb46;
  border-radius: 50%;
  bottom: -45px;
  color: #ffcb46;
  display: block;
  height: 50px;
  line-height: 50px;
  opacity: 0;
  position: fixed;
  right: 45px;
  text-align: center;
  text-decoration: none;
  width: 50px;
  z-index: 9;
  -webkit-transform: scale(0.3);
  -moz-transform: scale(0.3);
  -ms-transform: scale(0.3);
  -o-transform: scale(0.3);
  transform: scale(0.3);
  -webkit-box-shadow: 0px 1px 4px 0px rgba(36, 65, 231, 0.3);
  -moz-box-shadow: 0px 1px 4px 0px rgba(36, 65, 231, 0.3);
  -o-box-shadow: 0px 1px 4px 0px rgba(36, 65, 231, 0.3);
  box-shadow: 0px 1px 4px 0px rgba(36, 65, 231, 0.3);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.scrollToHome:hover {
  color: #ffffff;
}

.scrollToHome:hover {
  background-color: #EB6753;
  color: #ffffff;
}

.scrollToHome.show {
  bottom: 45px;
  right: 45px;
  opacity: 1;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.animate-up-1,
.animate-up-2,
.animate-up-3,
.animate-up-4,
.animate-up-5 {
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

.animate-up-1 {
  -webkit-animation-duration: 0.5s;
  animation-duration: 0.5s;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}

.animate-up-2 {
  -webkit-animation-duration: 0.7s;
  animation-duration: 0.7s;
  -webkit-animation-delay: 0.7s;
  animation-delay: 0.7s;
}

.animate-up-3 {
  -webkit-animation-duration: 0.9s;
  animation-duration: 0.9s;
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}

.animate-up-4 {
  -webkit-animation-duration: 1.1s;
  animation-duration: 1.1s;
  -webkit-animation-delay: 1.1s;
  animation-delay: 1.1s;
}

.animate-up-5 {
  -webkit-animation-duration: 1.3s;
  animation-duration: 1.3s;
  -webkit-animation-delay: 1.3s;
  animation-delay: 1.3s;
}

.line-clamp {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

/* Contact Styles */
.home8-map {
  bottom: 0;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
}
.home8-map.contact-page {
  height: 550px;
}
@media (max-width: 991.98px) {
  .home8-map.contact-page {
    height: 350px;
    position: relative;
  }
}

.home10-map {
  height: 700px;
}
@media (max-width: 991.98px) {
  .home10-map {
    height: 350px;
  }
}

/* All CTA Styles Here */
.cta-style1 {
  position: relative;
}
.cta-style1 .cta-title {
  letter-spacing: 0.02em;
}
.cta-style1 .cta-text {
  font-size: 15px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}
@media (max-width: 991.98px) {
  .cta-style1 {
    margin-bottom: 20px;
  }
}

@media (max-width: 575.98px) {
  .cta-btns-style1 .ud-btn {
    width: 100%;
  }
  .cta-btns-style1 .ud-btn:first-child {
    margin-bottom: 12px;
  }
}

.our-cta2 {
  margin-bottom: -150px;
  z-index: 1;
}

.cta-banner2 {
  position: relative;
}

.cta-banner3:before {
  background-image: url(../images/about/cta-side-bg-1.jpg);
  background-size: cover;
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  width: 42%;
}
@media (max-width: 767.98px) {
  .cta-banner3:before {
    display: none;
  }
}

.cta-style2 {
  position: relative;
}
.cta-style2 .cta-title {
  letter-spacing: 0.02em;
}
.cta-style2 .cta-title,
.cta-style2 .cta-text {
  color: #ffffff;
}
.cta-style2 .cta-text {
  font-size: 15px;
  font-family: var(--title-font-family);
  font-weight: 400;
}
.cta-style2 img {
  left: auto;
  right: 0;
  position: absolute;
  top: -65px;
}

.cta-style3 .cta-title {
  font-family: var(--body-font-family);
}
.cta-style3 .cta-text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}

.cta-banner4 {
  background-attachment: fixed;
  background-image: url(../images/background/cta-bg-1.jpg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 600px;
}

@media (max-width: 575.98px) {
  .cta-style4 .cta-title {
    font-size: 30px;
  }
}

.cta-img {
  bottom: 0;
  position: absolute;
}

.our-cta3 {
  background-color: #f8f8ff;
}

.cta-banner5:before {
  background-image: url(../images/about/cta-side-bg-2.jpg);
  background-size: cover;
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  width: 51%;
}
@media (max-width: 767.98px) {
  .cta-banner5:before {
    display: none;
  }
}

.cta-style5 .app-tag {
  background-color: #ffffff;
  border-radius: 60px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  display: block;
  height: 34px;
  text-align: center;
  line-height: 34px;
  width: 128px;
}
.cta-style5 .cta-title {
  color: #0D1C39;
  letter-spacing: 0.02em;
}
.cta-style5 .cta-text {
  color: #0D1C39;
  font-family: var(--title-font-family);
  font-weight: 400;
}

.cta-banner6:before {
  background-image: url(../images/about/cta-side-bg-3.jpg);
  background-size: cover;
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  width: 51%;
}
@media (max-width: 1199.98px) {
  .cta-banner6:before {
    display: none;
  }
}

.cta-style6 .cta-title {
  color: #222222;
  font-family: var(--body-font-family);
  font-size: 32px;
}
.cta-style6 .cta-text {
  color: #1F4B3F;
}

/* Error Pages Styles */
.login-bg-icon {
  position: absolute;
  right: 0;
  bottom: 0;
}

.error_page_content .erro_code {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  line-height: 289px;
  font-size: 200px;
}
@media (max-width: 575.98px) {
  .error_page_content .erro_code {
    font-size: 150px;
  }
}

/* Iconbox Styles */
.iconbox-style1 {
  background-color: #F7F7F7;
  border-radius: 12px;
  margin-bottom: 30px;
  padding: 30px;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style1 .icon {
  background-color: #ffffff;
  border-radius: 50%;
  color: var(--headings-color);
  display: block;
  font-size: 30px;
  height: 70px;
  line-height: 75px;
  margin-bottom: 65px;
  text-align: center;
  width: 70px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style1 .title {
  margin-bottom: 5px;
}
.iconbox-style1 .title,
.iconbox-style1 .text,
.iconbox-style1 .icon {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style1:hover {
  background-color: #181A20;
}
.iconbox-style1:hover .title,
.iconbox-style1:hover .text,
.iconbox-style1:hover .icon {
  color: #ffffff;
}
.iconbox-style1:hover .icon {
  background-color: rgba(255, 255, 255, 0.1);
}
.iconbox-style1.dark-style {
  background-color: #181A20;
}
.iconbox-style1.dark-style .icon {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  margin-bottom: 35px;
}
.iconbox-style1.dark-style .title,
.iconbox-style1.dark-style .text {
  color: #ffffff;
}
.iconbox-style1.dark-style:hover {
  background-color: #ffffff;
}
.iconbox-style1.dark-style:hover .icon {
  background-color: #F7F7F7;
}
.iconbox-style1.dark-style:hover .title,
.iconbox-style1.dark-style:hover .text,
.iconbox-style1.dark-style:hover .icon {
  color: #181A20;
}
.iconbox-style1.theme-style {
  background-color: #EB6753;
}
.iconbox-style1.theme-style .icon {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  margin-bottom: 35px;
}
.iconbox-style1.theme-style .title,
.iconbox-style1.theme-style .text {
  color: #ffffff;
}
.iconbox-style1.theme-style:hover {
  background-color: #ffffff;
}
.iconbox-style1.theme-style:hover .icon {
  background-color: rgba(235, 103, 83, 0.07);
  color: #EB6753;
}
.iconbox-style1.theme-style:hover .title,
.iconbox-style1.theme-style:hover .text {
  color: #181A20;
}

.iconbox-style2 {
  background-color: #ffffff;
  border-radius: 12px;
  margin-bottom: 30px;
  padding: 60px 35px 20px;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .iconbox-style2 {
    padding: 60px 25px 20px;
  }
}
.iconbox-style2.active, .iconbox-style2:hover {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.iconbox-style2 .icon {
  margin-bottom: 25px;
}
.iconbox-style2 .title {
  margin-bottom: 12px;
}
.iconbox-style2 .text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  margin-bottom: 25px;
}

.iconbox-style3 {
  background-color: #ffffff;
  border-radius: 12px;
  margin-bottom: 30px;
  padding: 60px 35px 20px;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .iconbox-style3 {
    padding: 60px 25px 20px;
  }
}
.iconbox-style3.active, .iconbox-style3:hover {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.iconbox-style3 img {
  max-height: 150px;
}
.iconbox-style3 .icon {
  margin-bottom: 25px;
}
.iconbox-style3 .title {
  margin-bottom: 12px;
}
.iconbox-style3 .text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  margin-bottom: 25px;
}

.iconbox-style4 {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  margin-bottom: 30px;
  padding: 30px;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style4 .icon {
  background-color: rgba(235, 103, 83, 0.07);
  border-radius: 50%;
  color: var(--headings-color);
  display: block;
  font-size: 30px;
  height: 70px;
  line-height: 75px;
  margin-bottom: 15px;
  text-align: center;
  width: 70px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style4 .title {
  margin-bottom: 5px;
}
.iconbox-style4:hover {
  background-color: #EB6753;
}
.iconbox-style4:hover .title,
.iconbox-style4:hover .text,
.iconbox-style4:hover .icon {
  color: #ffffff;
}
.iconbox-style4:hover .icon {
  background-color: rgba(255, 255, 255, 0.1);
}

.iconbox-style5 {
  background-color: #ffffff;
  bottom: 20px;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  max-width: 210px;
  padding: 20px;
  position: absolute;
  right: -50px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .iconbox-style5 {
    right: -10px;
  }
}
.iconbox-style5 .icon {
  background-color: #EB6753;
  border-radius: 50%;
  color: #ffffff;
  display: block;
  font-size: 24px;
  height: 60px;
  line-height: 65px;
  text-align: center;
  width: 60px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style5 .title {
  margin-bottom: 5px;
}
.iconbox-style5:hover {
  background-color: #EB6753;
}
.iconbox-style5:hover .title,
.iconbox-style5:hover .text,
.iconbox-style5:hover .icon {
  color: #ffffff;
}
.iconbox-style5:hover .icon {
  background-color: rgba(255, 255, 255, 0.1);
}

.iconbox-style6 {
  border: 1px solid #DDDDDD;
  border-radius: 12px;
  margin-bottom: 30px;
  padding: 45px 30px 20px;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style6 .title {
  color: #EB6753;
}
.iconbox-style6 .icon {
  color: #EB6753;
  font-size: 36px;
  position: absolute;
  right: 30px;
  top: 17px;
}
.iconbox-style6 .iconbox-text {
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 13px;
}
.iconbox-style6:hover {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}

.iconbox-style7 {
  background-color: #ffffff;
  bottom: 20px;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  max-width: 210px;
  padding: 20px;
  position: absolute;
  right: -50px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .iconbox-style7 {
    right: -10px;
  }
}
.iconbox-style7 .icon {
  background-color: #181A20;
  border-radius: 50%;
  color: #ffffff;
  display: block;
  font-size: 24px;
  height: 60px;
  line-height: 65px;
  text-align: center;
  width: 60px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style7 .title {
  margin-bottom: 5px;
}
.iconbox-style7:hover {
  background-color: #EB6753;
}
.iconbox-style7:hover .title,
.iconbox-style7:hover .text,
.iconbox-style7:hover .icon {
  color: #ffffff;
}
.iconbox-style7:hover .icon {
  background-color: rgba(255, 255, 255, 0.1);
}

.iconbox-style8 {
  background-color: #ffffff;
  border-radius: 12px;
  margin-bottom: 30px;
  padding: 60px 35px 65px;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .iconbox-style8 {
    padding: 60px 25px 20px;
  }
}
.iconbox-style8.active, .iconbox-style8:hover {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.iconbox-style8 .icon {
  margin-bottom: 60px;
}
.iconbox-style8 .title {
  margin-bottom: 12px;
}
.iconbox-style8 .text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  margin-bottom: 25px;
}

.iconbox-style9 {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style9 .icon {
  color: #181A20;
  display: block;
  font-size: 30px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.iconbox-style9:hover {
  background-color: #EB6753;
}
.iconbox-style9:hover .iconbox-title,
.iconbox-style9:hover .text,
.iconbox-style9:hover .icon {
  color: #ffffff;
}

.apartment-category {
  background-color: #F7F7F7;
  border: 1px solid #F7F7F7;
  border-radius: 70px;
  margin-bottom: 30px;
  padding: 10px 30px 10px 10px;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.apartment-category .icon {
  background-color: #ffffff;
  border-radius: 50%;
  color: var(--headings-color);
  font-size: 20px;
  height: 50px;
  line-height: 55px;
  text-align: center;
  width: 50px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.apartment-category .title {
  margin-left: 12px;
}
.apartment-category:hover {
  background-color: #ffffff;
  border: 1px solid #DDDDDD;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.apartment-category:hover .icon {
  background-color: #EB6753;
  color: #ffffff;
}

.feature-style1 {
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}
.feature-style1 .feature-img {
  position: relative;
}
.feature-style1 img {
  transform: scale(1);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.feature-style1 .feature-content {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.feature-style1 .title,
.feature-style1 .text,
.feature-style1 .ud-btn2 {
  color: #ffffff;
}
.feature-style1 .ud-btn2 {
  font-size: 14px;
  font-weight: 400;
}
.feature-style1 .bottom-area {
  transform: translateY(50px);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.feature-style1 .top-area {
  padding-left: 30px;
  position: absolute;
  top: 30px;
}
.feature-style1 .bottom-area {
  left: 30px;
  bottom: 30px;
  position: absolute;
}
.feature-style1:hover .feature-content {
  background-color: rgba(24, 26, 32, 0.6);
}
.feature-style1:hover .bottom-area {
  transform: translateY(0px);
}
.feature-style1:hover img {
  transform: scale(1.1) rotate(-1deg);
}

.feature-style2 {
  position: relative;
}
.feature-style2 .feature-img {
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}
.feature-style2 img {
  transform: scale(1);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.feature-style2 .title,
.feature-style2 .text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
}
.feature-style2:hover img {
  transform: scale(1.1) rotate(-1deg);
}

.feature-style3 {
  position: relative;
}
.feature-style3 .feature-img {
  overflow: hidden;
  position: relative;
}
.feature-style3 img {
  transform: scale(1);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.feature-style3 .feature-content {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.feature-style3:hover img {
  transform: scale(1.1) rotate(-1deg);
}

.why-chose-list {
  position: relative;
}
.why-chose-list .list-one .list-icon {
  background-color: rgba(235, 103, 83, 0.07);
  border-radius: 50%;
  color: #EB6753;
  display: block;
  font-size: 30px;
  height: 70px;
  line-height: 80px;
  text-align: center;
  width: 70px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.why-chose-list .list-one:hover .list-icon {
  background-color: #EB6753;
  color: #ffffff;
}
.why-chose-list.style2 .list-one:hover .list-icon {
  background-color: rgba(235, 103, 83, 0.6);
  color: #ffffff;
}
.why-chose-list.style2 .list-icon {
  color: var(--headings-color);
}
.why-chose-list.style3 .list-icon {
  background-color: #F7F7F7;
}
.why-chose-list.style3 .list-icon {
  color: var(--headings-color);
}

.home9-city-style {
  position: relative;
  padding: 20px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.home9-city-style:hover {
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}

/* All Type Of Forms Styles */
.form-style1 {
  position: relative;
}
.form-style1 .form-control {
  border-radius: 8px;
  border: 1px solid #DDDDDD;
  box-shadow: none;
  font-size: 14px;
  height: 55px;
  outline: none;
  padding-left: 15px;
}
.form-style1 .form-control::placeholder {
  color: #717171;
  font-family: var(--title-font-family);
}
.form-style1 .form-control.active, .form-style1 .form-control:focus {
  border: 1px solid transparent;
  outline: 2px solid var(--headings-color);
  color: var(--headings-color);
}
.form-style1 .form-label {
  font-family: var(--title-font-family);
}
.form-style1 .custom_checkbox {
  line-height: 26px;
}
.form-style1 .custom_checkbox .checkmark {
  top: 7px;
}
.form-style1 .form-select {
  height: 50px;
}
.form-style1 .form-select option {
  font-size: 14px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  height: 50px;
  border-bottom: 1px solid #DDDDDD;
}
.form-style1 .form-select option:hover {
  background-color: rgba(235, 103, 83, 0.07);
}
.form-style1 .form-select:before {
  background-color: #DDDDDD;
  content: "";
  position: absolute;
  height: 50px;
  right: 30px;
  width: 1px;
}

.form-style2 {
  position: relative;
}
.form-style2 .form-control {
  border-radius: 8px;
  border: 1px solid #DDDDDD;
  box-shadow: none;
  height: 50px;
  outline: none;
  padding-left: 15px;
}
.form-style2 .form-control:placeholder {
  color: #717171;
  font-family: var(--title-font-family);
}
.form-style2 .form-control.active, .form-style2 .form-control:focus {
  border: 1px solid transparent;
  outline: 2px solid var(--headings-color);
}
.form-style2 .form-label {
  font-family: var(--title-font-family);
}

textarea {
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  font-family: var(--title-font-family);
  height: auto;
  width: 100%;
  padding: 25px 20px;
}
textarea:focus {
  border: 1px solid transparent;
  outline: 2px solid var(--headings-color);
  box-shadow: none;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #DDDDDD;
  box-shadow: none;
  font-size: 14px;
  font-family: var(--title-font-family);
  height: 55px;
  outline: none;
  padding-left: 15px;
}
.form-control:placeholder {
  color: #717171;
  font-family: var(--title-font-family);
}
.form-control:focus {
  border: 1px solid transparent;
  outline: 2px solid var(--headings-color);
  box-shadow: none;
}

.bootselect-multiselect {
  position: relative;
}
.bootselect-multiselect .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100%;
}
.bootselect-multiselect .btn {
  border-radius: 12px;
  border: 1px solid #DDDDDD;
  height: 55px;
  line-height: 40px;
  overflow: hidden;
  padding-left: 15px;
  width: 100%;
}
.bootselect-multiselect .btn:focus {
  border: 2px solid var(--headings-color);
  outline: none;
}
.bootselect-multiselect .btn.dropdown-toggle {
  width: 100%;
}
.bootselect-multiselect .btn.dropdown-toggle:focus {
  box-shadow: none;
  outline: none !important;
}
.bootselect-multiselect .bootstrap-select.dropdown-toggle.bs-placeholder {
  color: var(--headings-color);
}
.bootselect-multiselect .dropdown-menu {
  padding: 10px 15px;
}
.bootselect-multiselect .dropdown-menu .inner {
  padding: 10px;
}
.bootselect-multiselect .dropdown-menu .dropdown-item {
  padding: 10px;
}
.bootselect-multiselect .dropdown-menu .dropdown-item.active, .bootselect-multiselect .dropdown-menu .dropdown-item:hover, .bootselect-multiselect .dropdown-menu .dropdown-item:focus {
  background-color: rgba(235, 103, 83, 0.07);
  color: var(--headings-color);
  outline: none;
}
.bootselect-multiselect .btn-light {
  background-color: #ffffff;
}
.bootselect-multiselect .btn-light:hover, .bootselect-multiselect .btn-light:focus {
  background-color: #ffffff;
  border: 1px solid #DDDDDD;
  outline: none;
}
.bootselect-multiselect .btn-light::placeholder {
  color: var(--headings-color);
}

.inquiry-form .form-control {
  font-size: 14px;
  font-family: var(--title-font-family);
  font-weight: 400;
}
.inquiry-form .dropdown-menu {
  padding: 0;
}

.agent-single-form {
  margin-top: -150px;
}
@media (max-width: 991.98px) {
  .agent-single-form {
    margin-top: 30px;
  }
}

.dropdown-item.active {
  background-color: rgba(235, 103, 83, 0.07);
  color: var(--headings-color);
}

.advance-feature-modal button,
.sideborder-dropdown button {
  font-size: 14px;
}
.advance-feature-modal button.dropdown-toggle,
.sideborder-dropdown button.dropdown-toggle {
  background-color: transparent;
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  height: 50px;
  line-height: 35px;
  padding-left: 20px;
  width: 100%;
}
.advance-feature-modal button.dropdown-toggle:before,
.sideborder-dropdown button.dropdown-toggle:before {
  background-color: #DDDDDD;
  bottom: 1px;
  content: "";
  height: 46px;
  position: absolute;
  right: 40px;
  position: absolute;
  top: 1px;
  width: 1px;
}
.advance-feature-modal button.dropdown-toggle:after,
.sideborder-dropdown button.dropdown-toggle:after {
  margin-right: 5px;
}
.advance-feature-modal .form-style2 .bootstrap-select .dropdown-toggle:focus,
.sideborder-dropdown .form-style2 .bootstrap-select .dropdown-toggle:focus {
  outline: none !important;
}
.advance-feature-modal .form-style2 .bootstrap-select .dropdown-menu,
.sideborder-dropdown .form-style2 .bootstrap-select .dropdown-menu {
  transform: translate(0px, 0);
}
.advance-feature-modal .form-style2 .bootstrap-select .dropdown-item,
.sideborder-dropdown .form-style2 .bootstrap-select .dropdown-item {
  line-height: 35px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 14px;
}
.advance-feature-modal.psp-review button.dropdown-toggle,
.sideborder-dropdown.psp-review button.dropdown-toggle {
  color: #717171;
  font-family: var(--title-font-family);
}
.advance-feature-modal.psp-review .dropdown-menu li:first-child,
.sideborder-dropdown.psp-review .dropdown-menu li:first-child {
  display: none;
}

/* Gallery Styles */
/* Packages And Pricing Tables */
.pricing_table_switch_slide {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(235, 103, 83, 0.07);
  -webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
}
.pricing_table_switch_slide:before {
  bottom: 5px;
  content: "";
  height: 20px;
  left: 5px;
  position: absolute;
  width: 20px;
  background-color: white;
  -webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
}
.pricing_table_switch_slide.round {
  border-radius: 30px;
}
.pricing_table_switch_slide.round:before {
  border-radius: 50%;
}

.pricing_packages_top .switch input:checked + .pricing_table_switch_slide {
  background-color: #EB6753;
}

.pricing_packages_top .toggle-btn .switch input:focus + .pricing_table_switch_slide {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}

.pricing_packages_top .switch input:checked + .pricing_table_switch_slide:before {
  -webkit-transform: translateX(30px);
  -ms-transform: translateX(30px);
  transform: translateX(30px);
}

.pricing_packages_top .toggle-btn input,
.pricing_packages_top .toggle-btn label {
  display: inline-block;
  vertical-align: middle;
}
.pricing_packages_top .toggle-btn .switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
}
.pricing_packages_top .toggle-btn .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.pricing_packages_top .pricing_save1,
.pricing_packages_top .pricing_save2 {
  margin: 12px;
}
.pricing_packages_top .pricing_save3 {
  background-color: rgba(235, 103, 83, 0.07);
  border-radius: 60px;
  color: #EB6753;
  font-family: var(--body-font-family);
  font-size: 13px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0em;
  padding: 8px 17px;
}

.pricing_packages {
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  margin-bottom: 30px;
  padding: 50px;
  position: relative;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .pricing_packages {
    padding: 40px 30px;
  }
}
.pricing_packages.active, .pricing_packages:hover {
  background-color: rgba(235, 103, 83, 0.07);
}
.pricing_packages.active .package_title, .pricing_packages:hover .package_title {
  color: #EB6753;
}
.pricing_packages .heading {
  position: relative;
}
.pricing_packages .price-icon {
  position: absolute;
  right: 0;
  top: -10px;
}
.pricing_packages .text1,
.pricing_packages .text2 {
  color: #0D1C39;
  letter-spacing: 0.02em;
  margin-bottom: 0;
}
.pricing_packages .package_title {
  margin-bottom: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

/* Testimonials Styles */
.testimonial-style1 {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  margin-bottom: 15px;
  padding: 30px 10px 30px 30px;
}
.testimonial-style1 .testimonial-content {
  border-bottom: 1px solid #DDDDDD;
  margin-bottom: 20px;
  padding-bottom: 20px;
}
.testimonial-style1 .icon {
  color: rgba(235, 103, 83, 0.15);
  font-size: 36px;
  position: absolute;
  right: 60px;
  top: 30px;
}
.testimonial-style1 .title {
  font-size: 16px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  margin-bottom: 25px;
}
.testimonial-style1 .text {
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}
.testimonial-style1 .testimonial-review a {
  color: #E59819;
  font-size: 10px;
}

.testimonial-style2 {
  position: relative;
}
.testimonial-style2 .tab-content {
  height: auto;
}
.testimonial-style2 .testi-text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 22px;
  line-height: 45px;
  margin-bottom: 30px;
}
.testimonial-style2 .icon {
  color: #DDDDDD;
  font-size: 60px;
  margin-bottom: 23px;
}
.testimonial-style2 .design {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 13px;
}
.testimonial-style2 .nav-link {
  opacity: 0.2;
}
@media (max-width: 575.98px) {
  .testimonial-style2 .nav-link {
    padding-right: 0;
  }
}
@media (max-width: 575.98px) {
  .testimonial-style2 .nav-link img {
    max-height: 50px;
  }
}
@media (max-width: 339px) {
  .testimonial-style2 .nav-link img {
    max-width: 45px;
  }
}
.testimonial-style2 .nav-link.active {
  background-color: transparent;
  opacity: 1;
}

.testimonial-style3 {
  background-color: #ffffff;
  outline: 1px solid #DDDDDD;
  border-radius: 12px;
  margin-bottom: 30px;
  overflow: hidden;
  padding: 60px 60px 55px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 991.98px) {
  .testimonial-style3 {
    padding: 40px 30px 35px;
  }
}
.testimonial-style3 .icon {
  color: #F7F7F7;
  font-size: 100px;
  line-height: initial;
  position: absolute;
  right: 60px;
  top: 15px;
}
.testimonial-style3 .text {
  font-size: 15px;
}
.testimonial-style3:before {
  background-color: #EB6753;
  bottom: 0;
  content: "";
  height: 12px;
  position: absolute;
  left: 50%;
  width: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.testimonial-style3:hover:before {
  left: 0;
  right: 0;
  width: 100%;
}

.testimonial-style4 {
  padding: 55px 0 30px 60px;
}
@media (max-width: 1399.98px) {
  .testimonial-style4 {
    padding: 25px 0 30px 40px;
  }
}
@media (max-width: 991.98px) {
  .testimonial-style4 {
    padding: 25px 0 30px 0;
  }
}
.testimonial-style4 .icon {
  color: #F7F7F7;
  font-size: 36px;
  position: absolute;
  right: 60px;
  top: 50px;
}
.testimonial-style4 .title {
  line-height: 40px;
}
.testimonial-style4 .desig {
  color: #717171;
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0em;
}

/* Team Styles */
.team-style1 {
  position: relative;
}
.team-style1 .team-img {
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}
.team-style1 img {
  transform: scale(1);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.team-style1:hover img {
  transform: scale(1.1) rotate(-1deg);
}

.agency-style1 {
  position: relative;
}
.agency-style1 .tag {
  background-color: #EB6753;
  border-radius: 6px;
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 12px;
  left: 20px;
  padding: 0 6px;
  position: absolute;
  top: 20px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.agency-style1:hover .tag {
  transform: translateY(30px);
  opacity: 0;
}

/* Different Styles Of Animations */
.scale-infini {
  animation-name: myanimation;
  animation-duration: 5s;
  animation-iteration-count: infinite;
}

@keyframes myanimation {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    -o-transform: scale(2);
    -ms-transform: scale(2);
    transform: scale(2);
  }
  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}
.bounce-y {
  transform: translatey(0px);
  animation: bounceY 6s ease-in-out infinite;
}

@keyframes bounceY {
  0% {
    transform: translatey(0px);
  }
  50% {
    transform: translatey(-20px);
  }
  100% {
    transform: translatey(0px);
  }
}
.bounce-x {
  transform: translatey(0px);
  animation: bounceX 6s ease-in-out infinite;
}

@keyframes bounceX {
  0% {
    transform: translatex(0px);
  }
  50% {
    transform: translatex(-20px);
  }
  100% {
    transform: translatex(0px);
  }
}
.spin-right {
  animation: spin-right 14s infinite linear;
}

@keyframes spin-right {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.spin-left {
  animation: spin-left 14s infinite linear;
}

@keyframes spin-left {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
/* Different Accordion Styles*/
.accordion-style1 {
  position: relative;
  margin-bottom: 30px;
}
.accordion-style1 .accordion-item {
  border: 1px solid #DDDDDD;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 10px;
  padding: 4px 30px;
}
.accordion-style1 .accordion-item.active {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.accordion-style1 .accordion-item.active .accordion-button:before {
  content: "";
}
.accordion-style1 .accordion-header {
  border: none;
}
.accordion-style1 .accordion-button {
  background-color: transparent;
  box-shadow: none;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 14px;
  padding-left: 20px;
}
.accordion-style1 .accordion-button:before {
  color: #041e42;
  content: "";
  font-family: var(--icon-font-family);
  font-size: 8px;
  font-weight: bold;
  left: 0px;
  position: absolute;
}
.accordion-style1 .accordion-button:after {
  display: none;
}
.accordion-style1 .accordion-body {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  padding: 20px 0 35px;
  max-width: 410px;
}
.accordion-style1.faq-page .accordion-body {
  max-width: initial;
}
.accordion-style1.style2 .accordion-item {
  border: none;
  padding: 0;
}
.accordion-style1.style2 .accordion-item.active {
  box-shadow: none;
}
.accordion-style1.style2 .accordion-header {
  border: none;
  border-bottom: 1px solid #DDDDDD;
}
.accordion-style1.style2 .accordion-body {
  max-width: initial;
  padding: 0;
}

.feature-accordion {
  position: relative;
}
.feature-accordion .accordion-button {
  background-color: transparent;
  box-shadow: none;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  outline: none;
}

.agent-single-accordion .accordion-button {
  background-color: transparent;
  box-shadow: none;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 14px;
  text-decoration-line: underline;
}
.agent-single-accordion .accordion-button:after {
  display: none;
}

.accordion-style2 {
  position: relative;
  margin-bottom: 30px;
}
.accordion-style2 .accordion-item {
  padding: 0;
}
.accordion-style2 .accordion-item.active .accordion-button:before {
  content: "";
}
.accordion-style2 .accordion-header {
  border: none;
}
.accordion-style2 .accordion-button {
  background-color: transparent;
  box-shadow: none;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 17px;
  padding-left: 0;
  padding-bottom: 0;
  padding-top: 0;
}
.accordion-style2 .accordion-button:before {
  color: #041e42;
  content: "";
  font-family: var(--icon-font-family);
  font-size: 14px;
  font-weight: bold;
  right: 10px;
  position: absolute;
}
.accordion-style2 .accordion-button:after {
  display: none;
}
.accordion-style2 .accordion-body {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  padding: 20px 0 0;
}

/* Different Styles of Alart */
.message-alart-style1 {
  position: relative;
}
.message-alart-style1 .alert {
  border-radius: 4px;
  font-family: var(--title-font-family);
  font-weight: 600;
  padding: 23px 30px;
  position: relative;
}
.message-alart-style1 .btn-close {
  cursor: pointer;
  right: 12px;
  top: 5px;
  font-size: 16px;
  opacity: 1;
  background: transparent;
}
.message-alart-style1 .alart_style_one {
  background-color: rgba(205, 233, 246, 0.5);
  color: #4780AA;
}
.message-alart-style1 .alart_style_one .btn-close {
  color: #4780AA;
}
.message-alart-style1 .alart_style_two {
  background-color: rgba(247, 243, 215, 0.5);
  color: #927238;
}
.message-alart-style1 .alart_style_two .btn-close {
  color: #927238;
}
.message-alart-style1 .alart_style_three {
  background-color: rgba(236, 200, 197, 0.5);
  color: #AB3331;
}
.message-alart-style1 .alart_style_three .btn-close {
  color: #AB3331;
}
.message-alart-style1 .alart_style_four {
  background-color: rgba(222, 242, 215, 0.5);
  color: #5B7052;
}
.message-alart-style1 .alart_style_four .btn-close {
  color: #5B7052;
}

.advance-feature-modal .modal-header,
.signup-modal .modal-header {
  padding: 30px;
}
.advance-feature-modal .btn-close,
.signup-modal .btn-close {
  background-color: #F7F7F7;
  border-radius: 50%;
  height: 40px;
  opacity: 1;
  padding: 0;
  text-align: center;
  width: 40px;
}
.advance-feature-modal .btn-close:focus,
.signup-modal .btn-close:focus {
  box-shadow: none;
  outline: none;
}
.advance-feature-modal .modal-content,
.signup-modal .modal-content {
  border: none;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.advance-feature-modal .modal-body,
.signup-modal .modal-body {
  padding: 15px 30px 30px;
}

.log-reg-form .hr_content {
  overflow: hidden;
  position: relative;
}
.log-reg-form .hr_top_text {
  background-color: #ffffff;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-size: 16px;
  height: 40px;
  left: 0;
  letter-spacing: 0em;
  line-height: 40px;
  margin: 0 auto;
  right: 0;
  text-align: center;
  top: -5px;
  width: 40px;
  position: absolute;
}

.modal-backdrop {
  z-index: 0;
}

/* blockquote Styles*/
.blockquote-style1 {
  background-color: rgba(235, 103, 83, 0.07);
  border-left: 3px solid var(--headings-color);
  padding: 45px 60px 20px;
  position: relative;
}
.blockquote-style1 .fst-italic {
  max-width: 655px;
  width: 100%;
}

/* All kind Of btns Styles */
.ud-btn {
  border-radius: 12px;
  display: inline-block;
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  font-style: normal;
  letter-spacing: 0em;
  padding: 13px 30px;
  position: relative;
  overflow: hidden;
  text-align: center;
  z-index: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.ud-btn i {
  margin-left: 10px;
  font-size: 16px;
  transform: rotate(-45deg);
}
.ud-btn:hover, .ud-btn:focus, .ud-btn:active {
  outline: none;
  box-shadow: none;
}
@media (max-width: 339px) {
  .ud-btn {
    padding: 13px 25px;
  }
}

.ud-btn2 {
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  position: relative;
  text-align: center;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.ud-btn2 i {
  margin-left: 10px;
  font-size: 16px;
  transform: rotate(-45deg);
}
.ud-btn2:hover {
  outline: none;
}

.btn-thm {
  background-color: #ffcb46;
  border: 1px solid #ffcb46;
  color: #ffffff;
}

.btn-dark {
  background-color: #9ac2c9;
  border: 1px solid #9ac2c9;
  color: #ffffff;
}

.ud-btn:before,
.btn-gray:before,
.btn-dark:before,
.btn-thm:before,
.btn-thm2:before,
.btn-thm3:before,
.btn-thm-border:before,
.btn-white:before,
.btn-light-gray:before,
.btn-transparent:before,
.btn-transparent2:before {
  background-color: #ffcb46;
  content: "";
  height: 100%;
  left: -100%;
  position: absolute;
  top: 0;
  width: 0;
  z-index: -1;
  -webkit-transform: skew(50deg);
  -moz-transform: skew(50deg);
  -o-transform: skew(50deg);
  transform: skew(50deg);
  -webkit-transition: width 0.6s;
  -moz-transition: width 0.6s;
  -o-transition: width 0.6s;
  transition: width 0.6s;
  transform-origin: top left;
}
.ud-btn:hover,
.btn-gray:hover,
.btn-dark:hover,
.btn-thm:hover,
.btn-thm2:hover,
.btn-thm3:hover,
.btn-thm-border:hover,
.btn-white:hover,
.btn-light-gray:hover,
.btn-transparent:hover,
.btn-transparent2:hover {
  color: #ffffff;
}
.ud-btn:hover:before,
.btn-gray:hover:before,
.btn-dark:hover:before,
.btn-thm:hover:before,
.btn-thm2:hover:before,
.btn-thm3:hover:before,
.btn-thm-border:hover:before,
.btn-white:hover:before,
.btn-light-gray:hover:before,
.btn-transparent:hover:before,
.btn-transparent2:hover:before {
  height: 100%;
  width: 200%;
}

.btn-gray {
  background-color: #f7f7f7;
  border: 1px solid transparent;
  color: var(--headings-color);
  font-size: 13px;
  font-weight: 400;
  height: 50px;
  padding: 10px 30px;
}

.btn-dark:hover {
  background-color: #ffcb46;
  border: 1px solid #ffcb46;
}

.btn-thm2 {
  background-color: rgba(235, 103, 83, 0.1);
  color: #EB6753;
}

.btn-thm3 {
  background-color: #ffffff;
  border: 1px solid #EB6753;
  color: #EB6753;
}

.btn-thm-border {
  background-color: #ffffff;
  border: 1px solid #EB6753;
  color: var(--headings-color);
}

.btn-white,
.btn-white2 {
  background-color: #ffffff;
  border: 1px solid var(--headings-color);
}
.btn-white:hover,
.btn-white2:hover {
  border: 1px solid #ffcb46;
}

.btn-white2:before {
  background-color: #ffcb46;
}
.btn-white2:hover {
  border: 1px solid #ffcb46;
}

.btn-light-gray,
.btn-transparent {
  background-color: #F7F7F7;
  border: 1px solid var(--headings-color);
}
.btn-light-gray:hover,
.btn-transparent:hover {
  border: 1px solid #EB6753;
}

.btn-transparent,
.btn-transparent2 {
  background-color: transparent;
}

.btn-transparent2 {
  border: 1px solid #ffffff;
  color: #ffffff;
}
.btn-transparent2:hover, .btn-transparent2:focus, .btn-transparent2:active {
  border: 1px solid #ffffff;
  color: #EB6753;
}
.btn-transparent2:hover:before, .btn-transparent2:focus:before, .btn-transparent2:active:before {
  background-color: #ffffff;
}

.add-property {
  font-size: 14px;
  height: 47px;
  padding: 9px 22px;
}
.add-property:hover {
  color: #ffffff;
}

.menu-btn {
  background-color: transparent;
  border: 1px solid #ffffff;
  color: #ffffff;
}
.menu-btn:hover {
  background-color: #EB6753;
  border: 1px solid #EB6753;
}

.signup-modal .btn-white i,
.signup-modal .btn-fb i,
.signup-modal .btn-apple i {
  transform: rotate(0deg);
  position: absolute;
  left: 10px;
  top: 12px;
}

.btn-fb {
  background-color: #3A77EA;
  border: 1px solid #3A77EA;
  color: #ffffff;
}
.btn-fb:hover {
  border-color: #ffcb46;
}

.btn-apple {
  background-color: #181818;
  border: 1px solid #181818;
  color: #ffffff;
}
.btn-apple:hover {
  border-color: #ffcb46;
}

.radio-element {
  position: relative;
}
.radio-element .form-check-input {
  border: 1px solid var(--headings-color);
  height: 16px;
  margin-top: 0;
  width: 16px;
}
.radio-element .form-check-input:focus {
  border: 3px solid var(--headings-color);
  box-shadow: none;
  outline: none;
}
.radio-element .form-check-input:checked {
  background-color: #ffffff;
}
.radio-element .form-check-label {
  color: #222222;
  font-size: 15px;
  padding-left: 10px;
}

.checkbox-style1 {
  position: relative;
}

.custom_checkbox {
  display: block;
  cursor: pointer;
  font-size: 15px;
  font-weight: 400;
  line-height: 40px;
  letter-spacing: 0em;
  position: relative;
  padding-left: 28px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.custom_checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.custom_checkbox input:checked ~ .checkmark {
  background-color: #041e42;
}
.custom_checkbox .checkmark {
  border: 1px solid #041e42;
  border-radius: 4px;
  position: absolute;
  top: 10px;
  left: 0;
  height: 16px;
  width: 16px;
}
.custom_checkbox .checkmark:after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.switch-style1 input.form-check-input {
  background-color: rgba(235, 103, 83, 0.07);
  border: none;
  height: 30px;
  width: 55px;
}
.switch-style1 .form-check-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
  background-position: left center;
  transition: background-position 0.15s ease-in-out;
}
.switch-style1 .form-check-input:checked {
  background-color: #041e42;
  box-shadow: none;
}
.switch-style1 .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
  box-shadow: none;
  outline: none;
}
.switch-style1 label.form-check-label {
  color: var(--headings-color);
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0em;
  line-height: 30px;
  margin-left: 17px;
}

.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100%;
}
.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) .dropdown-toggle:focus {
  box-shadow: none;
  outline: none;
}

.video-icon {
  background-color: #ffffff;
  border-radius: 50%;
  color: var(--headings-color);
  display: block;
  height: 54px;
  line-height: 54px;
  outline: 1px solid #ffffff;
  outline-offset: 7px;
  text-align: center;
  width: 54px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.video-icon:hover {
  background-color: #EB6753;
  color: #ffffff;
  outline-offset: 10px;
  outline: 1px solid #ffffff;
}
@media (max-width: 767.98px) {
  .video-icon {
    height: 40px;
    line-height: 40px;
    width: 40px;
    top: 0px;
  }
}

.btn:focus {
  box-shadow: none;
  outline: none;
}

/* Funfact Styles */
.funfact-floating-img1 {
  bottom: 0;
  position: absolute;
  right: 0;
}

.funfact-floating-img2 {
  position: absolute;
  left: 0;
  top: 0;
}

.funfact_one .timer,
.funfact_one span {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 42px;
  line-height: 63px;
}

.funfact-style1 {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  margin-bottom: 30px;
  max-width: 280px;
  padding: 60px 15px 35px;
  position: relative;
}
@media (max-width: 991.98px) {
  .funfact-style1 {
    padding: 30px 15px 25px;
  }
}
.funfact-style1 .title,
.funfact-style1 span {
  color: var(--headings-color);
  font-family: var(--body-font-family);
  font-size: 45px;
  font-weight: 700;
  line-height: 59px;
  letter-spacing: 0em;
}
@media (max-width: 991.98px) {
  .funfact-style1 .title,
.funfact-style1 span {
    font-size: 35px;
  }
}

/* Menu Style */
header.nav-innerpage-style {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  padding: 20px 0;
}
header.nav-innerpage-style .ace-responsive-menu .sub-menu {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  padding: 15px 0;
  z-index: 9999;
  width: 260px;
}
header.nav-innerpage-style .ace-responsive-menu .sub-menu li {
  padding: 0 30px;
  width: 260px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
header.nav-innerpage-style .ace-responsive-menu .sub-menu li:hover {
  background-color: rgba(235, 103, 83, 0.07);
}
header.nav-innerpage-style .ace-responsive-menu .sub-menu a {
  line-height: 40px;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style .mega_menu_list {
  padding-left: 0;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style .sub-menu li {
  padding-left: 0;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style .sub-menu li:hover {
  background-color: transparent;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style .sub-menu a:hover {
  text-decoration: underline;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style:hover .dropdown-megamenu {
  border-radius: 12px;
  left: auto;
  max-width: 600px;
  overflow: hidden;
  padding: 30px;
  width: 100%;
  z-index: 99999;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style .dropdown-megamenu .sub-menu {
  border: none;
  box-shadow: none;
  display: block !important;
  float: left;
  left: auto !important;
  margin: 0;
  padding: 0;
  position: relative;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style .mega_menu_list {
  float: left;
  width: 33.33%;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style a.list-item,
header.nav-innerpage-style .ace-responsive-menu .visible_list a.list-item {
  border-radius: 60px;
  text-align: center;
  padding: 7px 22px;
}
header.nav-innerpage-style .ace-responsive-menu .megamenu_style a.list-item:hover,
header.nav-innerpage-style .ace-responsive-menu .visible_list a.list-item:hover {
  background-color: rgba(235, 103, 83, 0.07);
}
@media (max-width: 1399.98px) {
  header.nav-innerpage-style .ace-responsive-menu .megamenu_style a.list-item,
header.nav-innerpage-style .ace-responsive-menu .visible_list a.list-item {
    padding: 7px 17px;
  }
}
header.nav-innerpage-style .ace-responsive-menu a {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
header.nav-innerpage-style.menu-home4 {
  box-shadow: none;
  border-bottom: none;
}
header.nav-innerpage-style.style2 {
  box-shadow: none;
}
header.menu-home10 {
  box-shadow: none;
  border-bottom: none;
  padding: 0;
}
header.menu-home10 .ace-responsive-menu .sub-menu {
  margin-top: 1px;
}
header.menu-home10 .ace-responsive-menu .megamenu_style a.list-item,
header.menu-home10 .ace-responsive-menu .visible_list a.list-item {
  border-radius: 0px;
  line-height: 72px;
  position: relative;
}
header.menu-home10 .ace-responsive-menu .megamenu_style a.list-item:hover,
header.menu-home10 .ace-responsive-menu .visible_list a.list-item:hover {
  background-color: transparent;
}
header.menu-home10 .ace-responsive-menu .megamenu_style a.list-item:hover:before,
header.menu-home10 .ace-responsive-menu .visible_list a.list-item:hover:before {
  width: 100%;
}
header.menu-home10 .ace-responsive-menu .megamenu_style a.list-item:before,
header.menu-home10 .ace-responsive-menu .visible_list a.list-item:before {
  background-color: #EB6753;
  content: "";
  position: absolute;
  height: 2px;
  left: 0;
  width: 0;
  top: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
header.menu-home10.stricky.stricky-fixed .ace-responsive-menu .megamenu_style a.list-item:before,
header.menu-home10.stricky.stricky-fixed .ace-responsive-menu .visible_list a.list-item:before {
  background-color: #181A20;
}
header.nav-homepage-style {
  background-color: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.07);
  padding: 20px 0;
  position: fixed;
  width: 100%;
  z-index: 3;
}
header.nav-homepage-style .ace-responsive-menu .sub-menu {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  padding: 15px 0;
  z-index: 9999;
  width: 260px;
}
header.nav-homepage-style .ace-responsive-menu .sub-menu li {
  padding: 0 30px;
  width: 260px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
header.nav-homepage-style .ace-responsive-menu .sub-menu li:hover {
  background-color: rgba(235, 103, 83, 0.07);
}
header.nav-homepage-style .ace-responsive-menu .sub-menu a {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  line-height: 40px;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style .mega_menu_list {
  padding-left: 0;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style .sub-menu li {
  padding-left: 0;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style .sub-menu li:hover {
  background-color: transparent;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style .sub-menu a:hover {
  text-decoration: underline;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style:hover .dropdown-megamenu {
  border-radius: 12px;
  left: auto;
  max-width: 600px;
  overflow: hidden;
  padding: 30px;
  width: 100%;
  z-index: 99999;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style .dropdown-megamenu .sub-menu {
  border: none;
  box-shadow: none;
  display: block !important;
  float: left;
  left: auto !important;
  margin: 0;
  padding: 0;
  position: relative;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style .mega_menu_list {
  float: left;
  width: 33.33%;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style a.list-item,
header.nav-homepage-style .ace-responsive-menu .visible_list a.list-item {
  border-radius: 60px;
  text-align: center;
  padding: 7px 22px;
}
header.nav-homepage-style .ace-responsive-menu .megamenu_style a.list-item:hover,
header.nav-homepage-style .ace-responsive-menu .visible_list a.list-item:hover {
  background-color: rgba(255, 255, 255, 0.07);
}
@media (max-width: 1399.98px) {
  header.nav-homepage-style .ace-responsive-menu .megamenu_style a.list-item,
header.nav-homepage-style .ace-responsive-menu .visible_list a.list-item {
    padding: 7px 17px;
  }
}
header.nav-homepage-style .ace-responsive-menu a {
  color: #ffffff;
  font-family: var(--title-font-family);
  font-weight: 600;
}
header.nav-homepage-style .header-logo.logo2 {
  display: none;
}
header.nav-homepage-style .sidemenu-btn,
header.nav-homepage-style .login-info {
  color: #ffffff;
}
header.nav-homepage-style .sidemenu-btn .img-2 {
  display: none;
}
header.nav-homepage-style.stricky.stricky-fixed {
  background-color: #ffffff;
  border-bottom: 1px solid #DDDDDD;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  z-index: 9;
}
header.nav-homepage-style.stricky.stricky-fixed .ace-responsive-menu a {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
header.nav-homepage-style.stricky.stricky-fixed .sidemenu-btn,
header.nav-homepage-style.stricky.stricky-fixed .login-info {
  color: var(--headings-color);
}
header.nav-homepage-style.stricky.stricky-fixed .sidemenu-btn .img-1 {
  display: none;
}
header.nav-homepage-style.stricky.stricky-fixed .sidemenu-btn .img-2 {
  display: block;
}
header.nav-homepage-style.stricky.stricky-fixed .header-logo.logo1 {
  display: none;
}
header.nav-homepage-style.stricky.stricky-fixed .header-logo.logo2 {
  display: block;
}
header.nav-homepage-style.stricky.stricky-fixed .menu-btn,
header.nav-homepage-style.stricky.stricky-fixed .btn-transparent2 {
  border: 1px solid var(--headings-color);
  color: var(--headings-color);
}
header.nav-homepage-style.stricky.stricky-fixed .menu-btn:hover {
  background-color: #EB6753;
  border: 1px solid #EB6753;
  color: #ffffff;
}
header.nav-homepage-style.at-home2, header.nav-homepage-style.at-home5 {
  border-bottom: none;
}
header.nav-homepage-style.at-home3 .ace-responsive-menu a {
  color: var(--headings-color);
}
header.nav-homepage-style.at-home3 .sidemenu-btn,
header.nav-homepage-style.at-home3 .login-info {
  color: var(--headings-color);
}
header.nav-homepage-style.at-home5 .ace-responsive-menu .sub-menu {
  margin-top: 1px;
}
header.nav-homepage-style.at-home5 .ace-responsive-menu .megamenu_style a.list-item,
header.nav-homepage-style.at-home5 .ace-responsive-menu .visible_list a.list-item {
  border-radius: 0px;
  position: relative;
}
header.nav-homepage-style.at-home5 .ace-responsive-menu .megamenu_style a.list-item:hover,
header.nav-homepage-style.at-home5 .ace-responsive-menu .visible_list a.list-item:hover {
  background-color: transparent;
}
header.nav-homepage-style.at-home5 .ace-responsive-menu .megamenu_style a.list-item:hover:before,
header.nav-homepage-style.at-home5 .ace-responsive-menu .visible_list a.list-item:hover:before {
  width: 100%;
}
header.nav-homepage-style.at-home5 .ace-responsive-menu .megamenu_style a.list-item:before,
header.nav-homepage-style.at-home5 .ace-responsive-menu .visible_list a.list-item:before {
  background-color: #ffffff;
  bottom: 0;
  content: "";
  position: absolute;
  height: 2px;
  left: 0;
  width: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
header.nav-homepage-style.at-home5.stricky.stricky-fixed .ace-responsive-menu .megamenu_style a.list-item:before,
header.nav-homepage-style.at-home5.stricky.stricky-fixed .ace-responsive-menu .visible_list a.list-item:before {
  background-color: #181A20;
}

/*== Mobile Menu Css ==*/
.header.innerpage-style {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  height: 65px;
  line-height: 65px;
}

.mm-panel {
  background-color: #ffffff;
  background: #ffffff;
}

.mm-listitem:after {
  display: none;
}

.mm-navbar {
  background-color: rgba(235, 103, 83, 0.07);
  color: var(--headings-color);
  font-weight: 500;
  font-size: 20px;
  line-height: 29px;
  height: 60px;
}

.mm-navbar__title {
  color: var(--headings-color);
}

.mm-wrapper__blocker {
  background-color: rgba(24, 26, 32, 0.5);
}

.mm-panel ul.mm-listview li:first-child.mm-listitem {
  margin-top: 30px;
}

.mm-panel ul.mm-listview li.mm-listitem.cl_btn {
  border-bottom: none;
  margin: 50px auto;
  max-width: 90%;
}

.mm-panel ul.mm-listview li.mm-listitem.cl_btn a.btn {
  color: #ffffff;
  font-family: var(--title-font-family);
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
}

a.mm-listitem__text,
a.mm-btn.mm-btn_next.mm-listitem__btn.mm-listitem__text {
  color: var(--headings-color);
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
}

a.mm-listitem__text:hover,
a.mm-btn.mm-btn_next.mm-listitem__btn.mm-listitem__text:hover {
  background-color: rgba(235, 103, 83, 0.07);
  border-left: 2px solid #EB6753;
  color: #EB6753;
}

li.mm-listitem:hover,
li.mm-listitem:active,
li.mm-listitem:focus {
  color: #ffffff;
}

.mm-btn_prev:before {
  background-color: transparent;
}

.hsidebar-content {
  padding: 30px 0;
  position: relative;
}

.modal-lg {
  max-width: 662px;
}

.modal:before {
  background: rgba(24, 26, 32, 0.5);
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0px;
  top: 0px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.hiddenbar-body-ovelay {
  background: rgba(24, 26, 32, 0.5);
  height: 100%;
  opacity: 0;
  position: fixed;
  right: 0px;
  top: 0px;
  width: 100%;
  visibility: hidden;
  z-index: 9990;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.menu-hidden-sidebar-content .hiddenbar-body-ovelay {
  opacity: 1;
  visibility: visible;
}
.menu-hidden-sidebar-content .lefttside-hidden-bar {
  opacity: 1;
  left: 0px;
  visibility: visible;
}

.signin-hidden-sidebar-content .rightside-hidden-bar {
  opacity: 1;
  right: 0px;
  visibility: visible;
}
.signin-hidden-sidebar-content .hiddenbar-body-ovelay {
  opacity: 1;
  visibility: visible;
}

.rightside-hidden-bar {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: fixed;
  right: -400px;
  top: 0px;
  width: 400px;
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 575.98px) {
  .rightside-hidden-bar {
    max-width: 300px;
  }
}
.rightside-hidden-bar .hsidebar-header {
  border-bottom: 1px solid #DDDDDD;
  padding: 25px 20px 15px 30px;
  display: block;
  position: relative;
}
.rightside-hidden-bar .hsidebar-header .title {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
}
.rightside-hidden-bar .hsidebar-header .sidebar-close-icon {
  background-color: #F7F7F7;
  border-radius: 50%;
  cursor: pointer;
  color: var(--headings-color);
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  position: absolute;
  right: 20px;
  text-align: center;
  top: 20px;
  width: 40px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 575.98px) {
  .rightside-hidden-bar .hsidebar-header .sidebar-close-icon {
    right: 10px;
  }
}
.rightside-hidden-bar::-webkit-scrollbar {
  width: 8px;
}
.rightside-hidden-bar::-webkit-scrollbar-track {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  border-radius: 12px;
}
.rightside-hidden-bar::-webkit-scrollbar-thumb {
  background-color: #DDDDDD;
  border-radius: 12px;
}

.lefttside-hidden-bar {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  height: 100%;
  opacity: 0;
  position: fixed;
  right: -350px;
  top: 0px;
  visibility: hidden;
  width: 350px;
  z-index: 99999;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.lefttside-hidden-bar .hsidebar-header {
  padding: 25px 20px 25px 30px;
  display: block;
  position: relative;
}
.lefttside-hidden-bar .sidebar-close-icon {
  background-color: #F7F7F7;
  border-radius: 50%;
  cursor: pointer;
  color: var(--headings-color);
  font-size: 12px;
  height: 40px;
  line-height: 40px;
  position: absolute;
  right: -60px;
  text-align: center;
  top: 20px;
  width: 40px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
@media (max-width: 400px) {
  .lefttside-hidden-bar .sidebar-close-icon {
    right: 30px;
  }
}
.lefttside-hidden-bar:-webkit-scrollbar {
  width: 3px;
}
.lefttside-hidden-bar .hsidebar-content {
  padding: 0 30px 30px;
  position: relative;
  max-height: 750px;
  overflow-y: scroll;
}
.lefttside-hidden-bar .hsidebar-content::-webkit-scrollbar {
  background: transparent;
  border-radius: 10px;
  padding-right: 10px;
  width: 10px;
}
.lefttside-hidden-bar .hsidebar-content::-webkit-scrollbar-thumb {
  background-color: #F1FCFA;
  border-radius: 10px;
}
.lefttside-hidden-bar.map-page .hsidebar-content {
  max-height: 900px;
}

.lefttside-hidden-bar {
  left: -350px;
  right: auto;
  width: 350px;
}
@media (max-width: 400px) {
  .lefttside-hidden-bar {
    width: 300px;
  }
}

.hiddenbar_navbar_menu {
  margin-bottom: 200px;
  padding-top: 30px;
  position: relative;
}
.hiddenbar_navbar_menu .navbar-nav .nav-item {
  padding-left: 2px solid transparent;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link {
  border-left: 2px solid transparent;
  line-height: 51px;
  letter-spacing: 0em;
  position: relative;
  padding: 0 30px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link:hover, .hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link:active, .hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link:focus {
  background-color: rgba(235, 103, 83, 0.07);
  border-left: 2px solid #EB6753;
  color: #EB6753;
}
.hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link i {
  line-height: 60px;
  position: absolute;
  right: 30px;
  -webkit-transition: all ease 0.2s;
  -moz-transition: all ease 0.2s;
  -o-transition: all ease 0.2s;
  transition: all ease 0.2s;
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
}
.hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link.collapsed i {
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  -o-transform: rotate(0deg);
}

.body_content_wrapper:before {
  background: rgba(24, 26, 32, 0.5);
  bottom: 0;
  content: "";
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.body_content_wrapper.before_active:before {
  opacity: 1;
  z-index: 9;
}

.hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link:hover i,
.hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link:active i,
.hiddenbar_navbar_menu .navbar-nav .nav-item .nav-link:focus i {
  color: #443297;
}

.hiddenbar_navbar_menu .navbar-nav .nav-item .collapse .nav .nav-item .nav-link:after {
  display: none;
}

.mobile-menu-btn.mm-listitem {
  position: absolute;
  border: 0;
  width: auto;
  right: 0;
  left: 0;
  bottom: 0;
}

.mm-panel_has-navbar .mm-navbar {
  background-color: rgba(235, 103, 83, 0.07);
}

.advance-search-menu {
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
}
.advance-search-menu.style2 {
  background-color: #181A20;
}

.advance-search-list li .search-field {
  border-radius: 40px;
  height: 44px;
  padding-left: 20px;
  width: 470px;
}
@media (max-width: 767.98px) {
  .advance-search-list li .search-field {
    margin-bottom: 15px;
    width: auto;
  }
}
.advance-search-list li .open-btn {
  border: 1px solid #DDDDDD;
}
.advance-search-list.no-box-shadow .open-btn {
  box-shadow: none;
}
.advance-search-list.at-1col-v2 li:last-child .open-btn {
  background-color: #F7F7F7;
  border: 1px solid #181A20;
}

/* Nav & Tabs Styles */
.advance-search-tab {
  max-width: 970px;
  position: relative;
  z-index: 2;
}
.advance-search-tab .nav.nav-tabs {
  background-color: #ffffff;
  border-bottom: 1px solid #DDDDDD;
  border-radius: 12px 12px 0 0;
  justify-content: center;
  max-width: 240px;
  width: 100%;
}
.advance-search-tab .nav-item {
  overflow: hidden;
  position: relative;
}
.advance-search-tab .nav-item:first-child {
  border-radius: 12px 0 0 0;
}
.advance-search-tab .nav-item:last-child {
  border-radius: 0 12px 0 0;
}
.advance-search-tab .nav-item:last-child .nav-link {
  margin-right: 0;
}
.advance-search-tab .nav-link {
  color: #717171;
  padding: 15px 13px;
  margin-right: 10px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
.advance-search-tab .nav-link:hover, .advance-search-tab .nav-link:focus, .advance-search-tab .nav-link:active {
  border-color: transparent;
}
.advance-search-tab .nav-link.active {
  color: var(--headings-color);
  border-color: transparent;
  border-bottom: 2px solid #181A20;
}
.advance-search-tab .tab-content {
  background-color: #ffffff;
  border-radius: 0 12px 12px 12px;
  padding: 20px;
  position: relative;
}

.advance-style2 {
  max-width: 970px;
  position: relative;
  z-index: 1;
}
.advance-style2 .nav.nav-tabs {
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  justify-content: center;
  margin-bottom: 20px;
  width: 100%;
}
.advance-style2 .nav-item {
  overflow: hidden;
  position: relative;
}
.advance-style2 .nav-item:first-child {
  border-radius: 12px 0 0 0;
}
.advance-style2 .nav-item:last-child {
  border-radius: 0 12px 0 0;
}
.advance-style2 .nav-item:last-child .nav-link {
  margin-right: 0;
}
.advance-style2 .nav-link {
  color: #ffffff;
  padding: 15px 13px;
  margin-right: 10px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
.advance-style2 .nav-link:hover, .advance-style2 .nav-link:focus, .advance-style2 .nav-link:active {
  border-color: transparent;
}
.advance-style2 .nav-link.active {
  background-color: transparent;
  color: #ffffff;
  border-color: transparent;
  border-bottom: 2px solid #ffffff;
}
.advance-style2 .tab-content {
  background-color: #ffffff;
  border-radius: 95px;
  padding: 10px;
  position: relative;
}
@media (max-width: 767.98px) {
  .advance-style2 .tab-content {
    border-radius: 20px;
  }
}

.advance-style3 {
  max-width: 970px;
  position: relative;
  z-index: 2;
}
.advance-style3 .nav.nav-tabs {
  background-color: #ffffff;
  border-bottom: 1px solid #FEF4F3;
  border-radius: 12px 12px 0 0;
  justify-content: center;
  max-width: 240px;
  width: 100%;
}
.advance-style3 .nav-item {
  overflow: hidden;
  position: relative;
}
.advance-style3 .nav-item:first-child {
  border-radius: 12px 0 0 0;
}
.advance-style3 .nav-item:last-child {
  border-radius: 0 12px 0 0;
}
.advance-style3 .nav-item:last-child .nav-link {
  margin-right: 0;
}
.advance-style3 .nav-link {
  color: #717171;
  padding: 15px 13px;
  margin-right: 10px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
.advance-style3 .nav-link:hover, .advance-style3 .nav-link:focus, .advance-style3 .nav-link:active {
  border-color: transparent;
}
.advance-style3 .nav-link.active {
  color: var(--headings-color);
  border-color: transparent;
  border-bottom: 2px solid #181A20;
}
.advance-style3 .tab-content {
  background-color: #ffffff;
  border-radius: 0 12px 12px 12px;
  padding: 20px 10px 20px 20px;
  position: relative;
}
.advance-style3.at-home7 {
  max-width: 685px;
}
.advance-style3.at-home7 .nav.nav-tabs {
  background-color: #F7F7F7;
  border-radius: 0;
}
.advance-style3.at-home7 .tab-content {
  background-color: #F7F7F7;
  border-radius: 0;
}
.advance-style3.at-home7 .advance-search-icon {
  border-radius: 0;
}
.advance-style3.at-home7 .nav-link.active {
  background-color: transparent;
}

.advance-style4 {
  position: relative;
  z-index: 2;
}
.advance-style4 .nav.nav-tabs {
  background-color: #ffffff;
  border-bottom: 1px solid #FEF4F3;
  border-radius: 12px 12px 0 0;
  justify-content: center;
  max-width: 240px;
  width: 100%;
}
.advance-style4 .nav-item {
  overflow: hidden;
  position: relative;
}
.advance-style4 .nav-item:first-child {
  border-radius: 12px 0 0 0;
}
.advance-style4 .nav-item:last-child {
  border-radius: 0 12px 0 0;
}
.advance-style4 .nav-item:last-child .nav-link {
  margin-right: 0;
}
.advance-style4 .nav-link {
  color: #717171;
  padding: 15px 13px;
  margin-right: 10px;
  font-family: var(--title-font-family);
  font-weight: 600;
}
.advance-style4 .nav-link:hover, .advance-style4 .nav-link:focus, .advance-style4 .nav-link:active {
  border-color: transparent;
}
.advance-style4 .nav-link.active {
  color: var(--headings-color);
  border-color: transparent;
  border-bottom: 2px solid #181A20;
}
.advance-style4 .tab-content {
  background-color: #ffffff;
  border-radius: 0 12px 12px 12px;
  padding: 20px 10px 20px 20px;
  position: relative;
}
.advance-style4 .tab-content label {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  display: block;
}
.advance-style4 .dropdown-lists .open-btn {
  box-shadow: none;
  display: block;
  padding: 0;
  position: relative;
  width: 100%;
}
.advance-style4 .dropdown-lists .open-btn i {
  font-size: 12px;
  position: absolute;
  right: 0;
}
.advance-style4 .form-control {
  background-color: #ffffff;
  padding-left: 0;
}
.advance-style4.at-home10 {
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}
.advance-style4.at-home10 .nav-link {
  color: #ffffff;
  margin-right: 0;
  padding: 20px 27px;
}
.advance-style4.at-home10 .nav-link.active {
  background-color: #ffffff;
  color: #181A20;
}
.advance-style4.at-home10 .nav.nav-tabs {
  background-color: #181A20;
  border-bottom: none;
  justify-content: start;
  max-width: 260px;
}

.navpill-style1 {
  position: relative;
}
.navpill-style1 .nav-item {
  margin-right: 10px;
}
.navpill-style1 .nav-item:last-child {
  margin-right: 0;
}
.navpill-style1 .nav-link {
  border: 1px solid #181A20;
  border-radius: 6px;
  padding: 7px 17px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.navpill-style1 .nav-link.active, .navpill-style1 .nav-link:hover {
  background-color: #181A20;
  color: #ffffff;
}

.navtab-style1,
.navtab-style2 {
  position: relative;
}
.navtab-style1 .nav-tabs,
.navtab-style2 .nav-tabs {
  border-bottom: 1px solid #DDDDDD;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.navtab-style1 .nav-link,
.navtab-style2 .nav-link {
  border-color: transparent;
  border: 2px solid transparent;
  color: #717171;
  font-family: var(--title-font-family);
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.navtab-style1 .nav-link:focus, .navtab-style1 .nav-link:hover, .navtab-style1 .nav-link.active,
.navtab-style2 .nav-link:focus,
.navtab-style2 .nav-link:hover,
.navtab-style2 .nav-link.active {
  border-color: transparent;
  border-bottom: 2px solid var(--headings-color);
  color: #181A20;
}

.navtab-style2 .nav-tabs {
  border-bottom: none;
}
.navtab-style2 .nav-link {
  border: none;
  border-bottom: 2px solid transparent;
  position: relative;
}
.navtab-style2 .nav-link:hover, .navtab-style2 .nav-link:focus, .navtab-style2 .nav-link.active {
  border: none;
  border-bottom: 2px solid #181A20;
  color: #181A20;
}

.dark-light-navtab {
  position: relative;
}
.dark-light-navtab .nav-link {
  background-color: rgba(255, 255, 255, 0.07);
  border: 1px solid var(--headings-color);
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  margin-right: 10px;
  padding: 6px 17px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.dark-light-navtab .nav-link.active {
  background-color: #ffffff;
  border: 1px solid var(--headings-color);
  color: var(--headings-color);
}
.dark-light-navtab.style2 .nav-item:last-child .nav-link {
  margin-right: 0;
}
.dark-light-navtab.style2 .nav-link {
  color: var(--headings-color);
}
.dark-light-navtab.style2 .nav-link.active {
  background-color: var(--headings-color);
  color: #ffffff;
}
.dark-light-navtab.style3 .nav-link {
  background-color: rgba(255, 255, 255, 0.07);
  border: none;
  color: #ffffff;
}
.dark-light-navtab.style3 .nav-link.active {
  background-color: #ffffff;
  border: none;
  color: #EB6753;
}
.dark-light-navtab.style4 .nav-link {
  border: none;
  border-radius: 0;
  color: var(--headings-color);
  margin-right: 0;
}
.dark-light-navtab.style4 .nav-link.active {
  border-bottom: 2px solid var(--headings-color);
}

.tab-content {
  height: auto;
}
.tab-content .active {
  height: auto;
}

.ps-navtab .nav-link {
  background-color: #ffffff;
  border: 1px solid #DDDDDD;
  border-radius: 12px;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 40px;
}
.ps-navtab .nav-link.active {
  background-color: #F7F7F7;
  border: 1px solid var(--headings-color);
  color: var(--headings-color);
}

.ps-v4-hero-tab .nav-pills {
  position: absolute;
  right: 30px;
  top: 30px;
  z-index: 9;
}
.ps-v4-hero-tab .nav-link {
  background-color: rgba(24, 26, 32, 0.8);
  border-radius: 12px;
  height: 54px;
  line-height: 60px;
  padding: 0;
  width: 54px;
}
.ps-v4-hero-tab .nav-link.active {
  background-color: #EB6753;
}
.ps-v4-hero-tab.at-v5 .nav-pills {
  right: 0;
}
.ps-v4-hero-tab.at-v5 .nav-link {
  background-color: rgba(255, 255, 255, 0.1);
}
.ps-v4-hero-tab.at-v5 .nav-link.active {
  background-color: #EB6753;
}

/* Paginations Styles */
.mbp_pagination ul {
  margin: 0;
  padding: 0;
}
.mbp_pagination .page_navigation {
  margin-bottom: 0;
  text-align: center;
  width: 100%;
}
.mbp_pagination .page-item {
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.mbp_pagination .page-item:hover {
  background-color: #ffcb46;
}
.mbp_pagination .page-item:first-child, .mbp_pagination .page-item:last-child {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
}
.mbp_pagination .page-item.active .page-link, .mbp_pagination .page-item:hover .page-link {
  background-color: #ffcb46;
  color: #ffffff;
}
.mbp_pagination .page-link {
  background-color: transparent;
  border: none;
  border-radius: 50%;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 15px;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
  padding: 0;
  text-align: center;
  width: 40px;
}

/* ProgressBar Styles */
.progressbar-style1 {
  position: relative;
}
.progressbar-style1 .progressbar-bg {
  background-color: #F7F7F7;
  border-radius: 10px;
  height: 5px;
  width: 100%;
}
.progressbar-style1 .progressd-bar {
  background-color: var(--headings-color);
  border-radius: 10px;
  position: absolute;
  height: 5px;
  top: 0;
}
.progressbar-style1 .progressd-bar span {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  right: 0;
  top: -25px;
  position: absolute;
}

.range-slider-style1,
.range-slider-style2 {
  max-width: 300px;
  position: relative;
  width: 100%;
}

span.ui-slider-handle.ui-corner-all.ui-state-default,
span.ui-slider-handle.ui-corner-all.ui-state-default:active,
span.ui-slider-handle.ui-corner-all.ui-state-default:focus,
span.ui-slider-handle.ui-corner-all.ui-state-default:hover,
span.ui-slider-handle.ui-corner-all.ui-state-default:focus-visible {
  background-color: #ffffff;
  background-image: url(../images/icon/range-slider-range-bg-line.svg);
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid var(--headings-color);
  border-radius: 50%;
  height: 29px;
  margin-left: 0;
  outline: none;
  top: -13px;
  width: 29px;
}

.ui-slider-range.ui-corner-all.ui-widget-header {
  background-color: var(--headings-color);
  height: 3px;
}

.slider-range.ui-slider.ui-corner-all.ui-slider-horizontal.ui-widget.ui-widget-content {
  background-color: #F7F7F7;
  border: 1px solid transparent;
  border-radius: 30px;
  height: 3px;
}

input.amount, input.amount2, input.amount3, input.amount4 {
  background-color: #ffffff;
  border: 1px solid #DDDDDD;
  border-radius: 6px;
  box-shadow: none;
  color: #717171;
  font-family: var(--title-font-family);
  font-size: 14px;
  font-weight: 400;
  height: 50px;
  line-height: 22px;
  letter-spacing: 0em;
  margin-top: 10px;
  padding: 10px 15px;
  width: 130px;
}
@media (max-width: 1199.98px) {
  input.amount, input.amount2, input.amount3, input.amount4 {
    max-width: 100px;
  }
}
input:placeholder {
  color: #717171;
}
input:focus-visible {
  border: 2px solid #DDDDDD;
}

.large-version .amount,
.large-version .amount2 {
  max-width: initial;
  width: 47%;
}

.at-home10 .amount,
.at-home10 .amount2 {
  max-width: initial;
  width: 42%;
}

#slider-range-value1,
#slider-range-value2,
#slider-range-value3,
#slider-range-value4 {
  background-color: #ffffff;
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  color: #717171;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0em;
  padding: 10px 15px;
  width: 130px;
}

.modal-version #slider-range-value1,
.modal-version #slider-range-value2 {
  width: 48%;
}

#slider-range-value1,
#slider-range-value3 {
  float: left;
}

#slider-range-value2 {
  float: right;
}

.slider-labels {
  margin-top: 15px;
}

.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -ms-touch-action: none;
  touch-action: none;
  -ms-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-target {
  position: relative;
}

.noUi-base {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.noUi-origin {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.noUi-handle {
  position: relative;
  z-index: 1;
}
.noUi-handle .noUi-handle {
  z-index: 10;
}
.noUi-handle .noUi-origin {
  -webkit-transition: left 0.3s, top 0.3s;
  transition: left 0.3s, top 0.3s;
}

.noUi-state-drag * {
  cursor: inherit !important;
}

.noUi-base,
.noUi-handle {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.noUi-horizontal .noUi-handle {
  background-color: #ffffff;
  background-image: url(../images/icon/range-slider-range-bg-line.svg);
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid var(--headings-color);
  border-radius: 50%;
  height: 29px;
  left: 0;
  top: -17px;
  width: 29px;
  -webkit-box-shadow: 0px 0px 5px 0px rgba(19, 19, 28, 0.2);
  -moz-box-shadow: 0px 0px 5px 0px rgba(19, 19, 28, 0.2);
  -o-box-shadow: 0px 0px 5px 0px rgba(19, 19, 28, 0.2);
  box-shadow: 0px 0px 5px 0px rgba(19, 19, 28, 0.2);
}

.noUi-background {
  background-color: #F7F7F7;
  border-radius: 30px;
  height: 3px;
}

.noUi-connect {
  background-color: var(--headings-color);
  border-radius: 30px;
  -webkit-transition: background 450ms;
  transition: background 450ms;
}

.noUi-draggable {
  cursor: w-resize;
}

.noUi-vertical .noUi-draggable {
  cursor: n-resize;
}

.noUi-handle {
  cursor: default;
  -webkit-box-sizing: content-box !important;
  -moz-box-sizing: content-box !important;
  box-sizing: content-box !important;
}
.noUi-handle:active {
  background-clip: padding-box;
  -webkit-background-clip: padding-box;
}

/* Social Styles */
.social-style1 i {
  color: #BEBDBD;
  border-radius: 50%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  width: 40px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.social-style1 i:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: #ffffff;
}
.social-style1.light-style i {
  color: var(--headings-color);
}
.social-style1.light-style i:hover {
  background-color: var(--headings-color);
  color: #ffffff;
}
.social-style1.light-style2 i {
  color: var(--headings-color);
}
.social-style1.light-style2 i:hover {
  background-color: #F7F7F7;
  color: var(--headings-color);
}

.home8-sidebar-wrapper .social-style2 {
  bottom: 32%;
  left: 0;
  right: 0;
  position: absolute;
}
.home8-sidebar-wrapper .tel {
  margin-left: -12px;
  top: 18%;
  width: 92px;
}
.home8-sidebar-wrapper .tel,
.home8-sidebar-wrapper .mail {
  font-size: 13px;
  font-family: var(--title-font-family);
  font-weight: 400;
  left: 0;
  position: absolute;
  right: 0;
  transform: rotate(-90deg);
}
.home8-sidebar-wrapper .mail {
  bottom: 55.2%;
}

/* Sliders Styles */
.thumbimg-countnumber-carousel .owl-dots {
  position: absolute;
  right: 340px;
  top: 300px;
  width: 60px;
}
@media (max-width: 1399.98px) {
  .thumbimg-countnumber-carousel .owl-dots {
    right: 50px;
  }
}
@media (max-width: 1199.98px) {
  .thumbimg-countnumber-carousel .owl-dots {
    bottom: 0;
    left: 30px;
    top: auto;
    width: auto;
  }
}
@media (max-width: 575.98px) {
  .thumbimg-countnumber-carousel .owl-dots {
    display: none;
  }
}

.banner-wrapper .owl-dot {
  display: grid;
  position: relative;
  z-index: 991;
}
.banner-wrapper .owl-dot span {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  background-color: transparent;
  border-radius: 50%;
  height: 50px;
  margin: 0 0 15px;
  opacity: 0.7;
  width: 50px;
  transition: all 0.4s ease;
}
.banner-wrapper .owl-dot:last-child {
  margin-bottom: 0;
}
.banner-wrapper .owl-dot.active span {
  opacity: 1;
  outline: 2px solid #ffffff;
  outline-offset: 5px;
}
.banner-wrapper .carousel-control-block {
  align-items: center;
  background-color: #ffffff;
  border-radius: 12px 12px 0 0;
  bottom: 0;
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  max-width: 160px;
  padding: 15px 20px;
  position: absolute;
  right: 20px;
  width: 100%;
  z-index: 999;
}
.banner-wrapper .carousel-btn-block {
  display: flex;
  flex-direction: column;
  border-left: 1px solid #DDDDDD;
  padding-left: 20px;
}
.banner-wrapper .carousel-btn-block span {
  font-size: 30px;
  color: var(--headings-color);
  cursor: pointer;
  line-height: 0.6em;
}
.banner-wrapper .carousel-btn-block span + span {
  margin-top: 7px;
}
.banner-wrapper .slider-number-count {
  font-weight: 400;
  color: var(--headings-color);
  display: flex;
}
.banner-wrapper .sep {
  margin-left: 10px;
  margin-right: 10px;
}
.banner-wrapper .sep,
.banner-wrapper .current-number {
  color: #EB6753;
  font-weight: 600;
}

.nav_none.owl-theme.owl-carousel .owl-nav {
  display: none !important;
}

.dots_none.owl-theme.owl-carousel .owl-dots {
  display: none !important;
}

.navi_pagi_top_right.owl-theme .owl-dots,
.navi_pagi_bottom_left.owl-theme .owl-dots,
.navi_pagi_bottom_center.owl-theme .owl-dots {
  display: block;
  text-align: center;
  width: 100px;
}
.navi_pagi_top_right.owl-theme .owl-nav,
.navi_pagi_bottom_left.owl-theme .owl-nav,
.navi_pagi_bottom_center.owl-theme .owl-nav {
  display: block;
  width: 140px;
  position: absolute;
}
.navi_pagi_top_right.owl-theme .owl-nav .owl-prev,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-prev,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-prev {
  left: 0;
  right: auto;
}
.navi_pagi_top_right.owl-theme .owl-nav .owl-next,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-next,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-next {
  left: auto;
  right: 0;
}
.navi_pagi_top_right.owl-theme .owl-nav .owl-prev,
.navi_pagi_top_right.owl-theme .owl-nav .owl-next,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-prev,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-next,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-prev,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-next {
  background-color: transparent;
  border: none;
  color: #222222;
  height: auto;
  line-height: initial;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  width: auto;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.navi_pagi_top_right.owl-theme .owl-nav .owl-prev:hover, .navi_pagi_top_right.owl-theme .owl-nav .owl-prev:active, .navi_pagi_top_right.owl-theme .owl-nav .owl-prev:focus,
.navi_pagi_top_right.owl-theme .owl-nav .owl-next:hover,
.navi_pagi_top_right.owl-theme .owl-nav .owl-next:active,
.navi_pagi_top_right.owl-theme .owl-nav .owl-next:focus,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-prev:hover,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-prev:active,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-prev:focus,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-next:hover,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-next:active,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-next:focus,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-prev:hover,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-prev:active,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-prev:focus,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-next:hover,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-next:active,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-next:focus {
  background-color: transparent;
  color: #222222;
}
.navi_pagi_top_right.owl-theme .owl-nav .owl-prev i,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-prev i,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-prev i {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.navi_pagi_top_right.owl-theme .owl-nav .owl-prev:hover i,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-prev:hover i,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-prev:hover i {
  transform: translateX(-5px) scale(1.25);
}
.navi_pagi_top_right.owl-theme .owl-nav .owl-next i,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-next i,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-next i {
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.navi_pagi_top_right.owl-theme .owl-nav .owl-next:hover i,
.navi_pagi_bottom_left.owl-theme .owl-nav .owl-next:hover i,
.navi_pagi_bottom_center.owl-theme .owl-nav .owl-next:hover i {
  transform: translateX(5px) scale(1.25);
}
.navi_pagi_top_right.owl-theme .owl-dots button.owl-dot.active span,
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot.active span,
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot.active span {
  background-color: #222222;
  border-radius: 50%;
  height: 8px;
  width: 8px;
}
.navi_pagi_top_right.owl-theme .owl-dots button.owl-dot span,
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot span,
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot span {
  background-color: #D7D7D7;
  border-radius: 50%;
  height: 6px;
  position: relative;
  width: 6px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
@media (max-width: 919px) {
  .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(4), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(5), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(7), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(8), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(9), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(10), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(11), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(12), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(13), .navi_pagi_top_right.owl-theme .owl-dots button.owl-dot:nth-child(14),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(4),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(5),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(7),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(8),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(9),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(10),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(11),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(12),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(13),
.navi_pagi_bottom_left.owl-theme .owl-dots button.owl-dot:nth-child(14),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(4),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(5),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(7),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(8),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(9),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(10),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(11),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(12),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(13),
.navi_pagi_bottom_center.owl-theme .owl-dots button.owl-dot:nth-child(14) {
    display: none;
  }
}

.dots_nav_light.owl-theme .owl-dots button.owl-dot:hover span, .dots_nav_light.owl-theme .owl-dots button.owl-dot.active span {
  background-color: #ffffff;
}
.dots_nav_light.owl-theme .owl-dots button.owl-dot span {
  background-color: #ffffff;
}
.dots_nav_light.owl-theme .owl-nav .owl-prev,
.dots_nav_light.owl-theme .owl-nav .owl-next {
  color: #ffffff;
}
.dots_nav_light.owl-theme .owl-nav .owl-prev:hover i, .dots_nav_light.owl-theme .owl-nav .owl-prev:active i,
.dots_nav_light.owl-theme .owl-nav .owl-next:hover i,
.dots_nav_light.owl-theme .owl-nav .owl-next:active i {
  color: #ffffff;
}

.navi_pagi_bottom_center.owl-theme .owl-nav {
  margin: 0 auto;
  bottom: 0;
  left: 0;
  right: 0;
}
@media (max-width: 991.98px) {
  .navi_pagi_bottom_center.owl-theme .owl-nav {
    bottom: 20px;
  }
}
.navi_pagi_bottom_center.owl-theme .owl-dots {
  bottom: -20px;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
}
@media (max-width: 991.98px) {
  .navi_pagi_bottom_center.owl-theme .owl-dots {
    bottom: 0;
  }
}

.navi_pagi_bottom_left.owl-theme .owl-dots {
  bottom: -18px;
  left: 20px;
  margin: 0 auto;
  position: absolute;
}
@media (max-width: 991.98px) {
  .navi_pagi_bottom_left.owl-theme .owl-dots {
    position: relative;
  }
}
.navi_pagi_bottom_left.owl-theme .owl-nav {
  bottom: 0;
  position: absolute;
}
@media (max-width: 991.98px) {
  .navi_pagi_bottom_left.owl-theme .owl-nav {
    position: relative;
  }
}

.navi_pagi_top_right.owl-theme .owl-dots {
  position: absolute;
  right: 20px;
  top: -90px;
  width: 100px;
}
@media (max-width: 991.98px) {
  .navi_pagi_top_right.owl-theme .owl-dots {
    left: 20px;
    position: relative;
    right: 0;
    top: -7px;
  }
}
.navi_pagi_top_right.owl-theme .owl-nav {
  right: 0;
  top: -83px;
}
@media (max-width: 991.98px) {
  .navi_pagi_top_right.owl-theme .owl-nav {
    position: relative;
    top: 0;
  }
}

.vam_nav_style.owl-theme .owl-nav {
  display: block;
  margin-top: 0;
}
.vam_nav_style.owl-theme .owl-nav button.owl-prev {
  left: -30px;
  right: auto;
}
@media (max-width: 579px) {
  .vam_nav_style.owl-theme .owl-nav button.owl-prev {
    left: -10px;
  }
}
.vam_nav_style.owl-theme .owl-nav button.owl-next {
  left: auto;
  right: -30px;
}
@media (max-width: 579px) {
  .vam_nav_style.owl-theme .owl-nav button.owl-next {
    right: -10px;
  }
}
.vam_nav_style.owl-theme .owl-nav button.owl-prev,
.vam_nav_style.owl-theme .owl-nav button.owl-next {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 10px 35px rgba(5, 16, 54, 0.1);
  -moz-box-shadow: 0px 10px 35px rgba(5, 16, 54, 0.1);
  -o-box-shadow: 0px 10px 35px rgba(5, 16, 54, 0.1);
  box-shadow: 0px 10px 35px rgba(5, 16, 54, 0.1);
  border: none;
  border-radius: 50%;
  color: #051036;
  display: inline-block;
  height: 50px;
  line-height: 50px;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 38%;
  width: 50px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.vam_nav_style.owl-theme .owl-nav button.owl-prev:hover, .vam_nav_style.owl-theme .owl-nav button.owl-prev:active, .vam_nav_style.owl-theme .owl-nav button.owl-prev:focus,
.vam_nav_style.owl-theme .owl-nav button.owl-next:hover,
.vam_nav_style.owl-theme .owl-nav button.owl-next:active,
.vam_nav_style.owl-theme .owl-nav button.owl-next:focus {
  background-color: #EB6753;
  color: #ffffff;
}

.explore-apartment-slider,
.testimonial-slider {
  position: relative;
}
.explore-apartment-slider .owl-stage-outer,
.testimonial-slider .owl-stage-outer {
  display: inline-block;
}

.property-city-slider.style2.vam_nav_style.owl-theme .owl-nav button.owl-prev,
.property-city-slider.style2.vam_nav_style.owl-theme .owl-nav button.owl-next {
  top: 25%;
}

.feature-listing-slider .listing-style1 {
  margin-bottom: 60px;
}

.feature-listing-slider2.owl-theme .owl-nav .owl-prev,
.feature-listing-slider2.owl-theme .owl-nav .owl-next {
  top: 25%;
}

.home6-listing-single-slider.owl-theme .owl-nav {
  bottom: 20px;
  left: 40px;
}
.home6-listing-single-slider.owl-theme .owl-dots {
  bottom: 2px;
  left: 50px;
}
@media (max-width: 991.98px) {
  .home6-listing-single-slider.owl-theme .owl-dots {
    position: absolute;
  }
}

.home7-testimonial-slider {
  position: relative;
}
.home7-testimonial-slider.owl-theme .owl-nav {
  bottom: 80px;
  left: 47%;
  margin: 0 auto;
  position: absolute;
  width: 120px;
}
@media (max-width: 991.98px) {
  .home7-testimonial-slider.owl-theme .owl-nav {
    left: auto;
    right: 0;
  }
}
.home7-testimonial-slider.owl-theme .owl-nav button.owl-prev,
.home7-testimonial-slider.owl-theme .owl-nav button.owl-next {
  background-color: #ffffff;
  border: 1px solid #181A20;
  border-radius: 50%;
  display: block;
  height: 50px;
  line-height: 45px;
  position: absolute;
  text-align: center;
  width: 50px;
}
.home7-testimonial-slider.owl-theme .owl-nav button.owl-prev:hover, .home7-testimonial-slider.owl-theme .owl-nav button.owl-prev:focus, .home7-testimonial-slider.owl-theme .owl-nav button.owl-prev:active,
.home7-testimonial-slider.owl-theme .owl-nav button.owl-next:hover,
.home7-testimonial-slider.owl-theme .owl-nav button.owl-next:focus,
.home7-testimonial-slider.owl-theme .owl-nav button.owl-next:active {
  background-color: #181A20;
  color: #ffffff;
}
.home7-testimonial-slider.owl-theme .owl-nav button.owl-prev {
  left: 0;
}
.home7-testimonial-slider.owl-theme .owl-nav button.owl-next {
  right: 0;
}

.home8-property-slider .swiper-pagination-progressbar.swiper-pagination-horizontal {
  height: 1px;
  left: 20px;
  right: auto;
  width: 70%;
}
.home8-property-slider .swiper-pagination-progressbar-fill {
  height: 3px;
}

.swpr_paginations {
  max-width: 330px;
  position: relative;
  width: 100%;
}
.swpr_paginations .slideactive {
  position: absolute;
  left: 0;
  top: -3px;
}
.swpr_paginations .slidetotal {
  position: absolute;
  right: 0;
  top: -3px;
}

.main-banner-wrapper {
  overflow: hidden;
  position: relative;
}
.main-banner-wrapper .carousel-btn-block {
  cursor: pointer;
  position: absolute;
  top: 40%;
  width: 100%;
  z-index: 1;
}
@media (max-width: 1199.98px) {
  .main-banner-wrapper .carousel-btn-block {
    top: auto;
    bottom: 60px;
  }
}
.main-banner-wrapper .carousel-btn {
  background-color: transparent;
  border: 1px solid #ffffff;
  border-radius: 50%;
  color: #FFF;
  cursor: pointer;
  display: inline-block;
  font-size: 13px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  width: 50px;
  z-index: 1;
  -webkit-box-shadow: 0px 10px 35px rgba(17, 33, 55, 0.1);
  -moz-box-shadow: 0px 10px 35px rgba(17, 33, 55, 0.1);
  -o-box-shadow: 0px 10px 35px rgba(17, 33, 55, 0.1);
  box-shadow: 0px 10px 35px rgba(17, 33, 55, 0.1);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  vertical-align: middle;
}
.main-banner-wrapper .carousel-btn:hover {
  background-color: #ffffff;
  color: #626974;
  opacity: 1;
}
.main-banner-wrapper .carousel-btn.left-btn {
  position: absolute;
  left: 160px;
}
@media (max-width: 1600px) {
  .main-banner-wrapper .carousel-btn.left-btn {
    left: 30px;
  }
}
@media (max-width: 1199.98px) {
  .main-banner-wrapper .carousel-btn.left-btn {
    left: 60px;
  }
}
@media (max-width: 575.98px) {
  .main-banner-wrapper .carousel-btn.left-btn {
    left: 30px;
  }
}
.main-banner-wrapper .carousel-btn.right-btn {
  left: 160px;
  position: absolute;
  right: auto;
  top: 70px;
}
@media (max-width: 1600px) {
  .main-banner-wrapper .carousel-btn.right-btn {
    left: 30px;
  }
}
@media (max-width: 1199.98px) {
  .main-banner-wrapper .carousel-btn.right-btn {
    top: auto;
    left: 120px;
  }
}
@media (max-width: 575.98px) {
  .main-banner-wrapper .carousel-btn.right-btn {
    left: 90px;
  }
}
.main-banner-wrapper.home10_style .carousel-btn.left-btn, .main-banner-wrapper.home10_style .carousel-btn.right-btn {
  left: auto;
  right: 18%;
}
@media (max-width: 1199.98px) {
  .main-banner-wrapper.home10_style .carousel-btn.right-btn {
    top: auto;
    left: auto;
    right: 13%;
  }
}
@media (max-width: 991.98px) {
  .main-banner-wrapper.home10_style .carousel-btn.right-btn {
    right: 10%;
  }
}
@media (max-width: 767.98px) {
  .main-banner-wrapper.home10_style .carousel-btn.right-btn {
    right: 8%;
  }
}
@media (max-width: 575.98px) {
  .main-banner-wrapper.home10_style .carousel-btn.right-btn {
    right: 5px;
  }
}

.banner-style-one.home10_style .slide {
  height: 700px;
}
@media (max-width: 1199.98px) {
  .banner-style-one.home10_style .slide {
    height: 600px;
  }
}
.banner-style-one .slide {
  align-items: center;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  height: 860px;
}
@media (max-width: 1199.98px) {
  .banner-style-one .slide {
    height: 600px;
  }
}
.banner-style-one .sub-title {
  color: #ffffff;
  font-size: 36px;
  font-family: var(--title-font-family);
  line-height: 60px;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
@media (max-width: 1199.98px) {
  .banner-style-one .sub-title {
    font-size: 48px;
    line-height: initial;
  }
}
@media (max-width: 991.98px) {
  .banner-style-one .sub-title {
    font-size: 36px;
  }
}
.banner-style-one .banner-title {
  color: #ffffff;
  font-size: 52px;
  font-family: var(--title-font-family);
  line-height: 60px;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
@media (max-width: 1199.98px) {
  .banner-style-one .banner-title {
    font-size: 48px;
    line-height: initial;
  }
}
@media (max-width: 991.98px) {
  .banner-style-one .banner-title {
    font-size: 36px;
  }
}
.banner-style-one .banner-text {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
.banner-style-one .banner-btn {
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.banner-style-one .banner-content {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
.banner-style-one .active .banner-title,
.banner-style-one .active .sub-title,
.banner-style-one .active .banner-text,
.banner-style-one .active .banner-btn,
.banner-style-one .active .banner-content {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}

.ps-v4-hero-slider.owl-carousel .owl-stage-outer {
  display: inline;
}
.ps-v4-hero-slider.vam_nav_style.owl-theme .owl-nav button.owl-prev,
.ps-v4-hero-slider.vam_nav_style.owl-theme .owl-nav button.owl-next {
  top: 45%;
}
.ps-v4-hero-slider.vam_nav_style.owl-theme .owl-nav button.owl-prev {
  left: -100px;
}
@media (max-width: 1399.98px) {
  .ps-v4-hero-slider.vam_nav_style.owl-theme .owl-nav button.owl-prev {
    left: -30px;
  }
}
@media (max-width: 767.98px) {
  .ps-v4-hero-slider.vam_nav_style.owl-theme .owl-nav button.owl-prev {
    left: 0;
  }
}
.ps-v4-hero-slider.vam_nav_style.owl-theme .owl-nav button.owl-next {
  right: -100px;
}
@media (max-width: 1399.98px) {
  .ps-v4-hero-slider.vam_nav_style.owl-theme .owl-nav button.owl-next {
    right: -30px;
  }
}
@media (max-width: 767.98px) {
  .ps-v4-hero-slider.vam_nav_style.owl-theme .owl-nav button.owl-next {
    right: 0px;
  }
}
@media (max-width: 575.98px) {
  .ps-v4-hero-slider .owl-item img {
    height: 250px;
  }
}

.ps-v4-hero-slider2.vam_nav_style.owl-theme .owl-nav button.owl-prev,
.ps-v4-hero-slider2.vam_nav_style.owl-theme .owl-nav button.owl-next {
  background-color: transparent;
  border: 1px solid #ffffff;
  color: #ffffff;
  top: 45%;
}
.ps-v4-hero-slider2.vam_nav_style.owl-theme .owl-nav button.owl-prev:hover,
.ps-v4-hero-slider2.vam_nav_style.owl-theme .owl-nav button.owl-next:hover {
  background-color: #ffffff;
  color: var(--headings-color);
}
.ps-v4-hero-slider2.vam_nav_style.owl-theme .owl-nav button.owl-prev {
  left: 100px;
}
@media (max-width: 1199.98px) {
  .ps-v4-hero-slider2.vam_nav_style.owl-theme .owl-nav button.owl-prev {
    left: 30px;
  }
}
.ps-v4-hero-slider2.vam_nav_style.owl-theme .owl-nav button.owl-next {
  right: 100px;
}
@media (max-width: 1199.98px) {
  .ps-v4-hero-slider2.vam_nav_style.owl-theme .owl-nav button.owl-next {
    right: 30px;
  }
}
@media (max-width: 575.98px) {
  .ps-v4-hero-slider2 .owl-item img {
    height: 200px;
  }
}

.ps-v6-slider.owl-theme .owl-dots .owl-dot:first-child {
  background-image: url(../images/listings/listing-single-6-1.jpg);
  background-position: center;
  background-size: cover;
}
.ps-v6-slider.owl-theme .owl-dots .owl-dot:nth-child(2) {
  background-image: url(../images/listings/listing-single-6-2.jpg);
  background-position: center;
  background-size: cover;
}
.ps-v6-slider.owl-theme .owl-dots .owl-dot:nth-child(3) {
  background-image: url(../images/listings/listing-single-6-3.jpg);
  background-position: center;
  background-size: cover;
}
.ps-v6-slider.owl-theme .owl-dots .owl-dot:nth-child(4) {
  background-image: url(../images/listings/listing-single-6-4.jpg);
  background-position: center;
  background-size: cover;
}
.ps-v6-slider button.owl-dot {
  background-color: aliceblue;
  border-radius: 12px;
  height: 83.33px;
  margin-bottom: 5px;
  margin-right: 10px;
  width: 90px;
}

.testimonial-style2 .img-part .swiper-slide {
  margin-right: 30px !important;
  width: auto !important;
}
.testimonial-style2 .img-part .swiper-slide:last-child {
  margin-right: 0 !important;
}

/* Sidebar Styles */
.mobile-filter-btn {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  border-radius: 60px;
  display: block;
  height: 40px;
  line-height: 40px;
  position: absolute;
  right: 0;
  text-align: center;
  top: 0;
  width: 98px;
}
.mobile-filter-btn span {
  padding-right: 8px;
}
@media (max-width: 575.98px) {
  .mobile-filter-btn {
    top: 35px;
  }
}
.mobile-filter-btn.map-page {
  border-radius: 12px;
  height: 55px;
  line-height: 55px;
  padding-left: 50px;
  right: auto;
  top: 30px;
  width: 169px;
  z-index: 1;
}
.mobile-filter-btn.map-page span {
  background-color: rgba(255, 255, 255, 0.1);
  display: inline-block;
  height: 55px;
  left: 0;
  padding: 0;
  line-height: 55px;
  position: absolute;
  width: 55px;
}

.list-sidebar-style1 {
  background-color: #ffffff;
  padding: 30px;
  position: relative;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  border-radius: 12px;
  margin-bottom: 30px;
}

.widget-wrapper {
  margin-bottom: 30px;
  position: relative;
}
.widget-wrapper .list-title {
  margin-bottom: 15px;
}
.widget-wrapper .form-check-label,
.widget-wrapper .custom_checkbox {
  font-size: 14px;
}
.widget-wrapper .custom_checkbox {
  line-height: 35px;
}

.search_area {
  position: relative;
}
.search_area .form-control {
  padding-left: 42px;
}
.search_area label {
  font-size: 18px;
  left: 15px;
  position: absolute;
  top: 16px;
}

.room-list {
  display: flex;
}
@media (max-width: 1199.98px) {
  .room-list {
    display: inherit;
  }
}
.room-list a {
  background-color: #ffffff;
  border: 1px solid #DDDDDD;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  height: 45px;
  line-height: 45px;
  margin-right: -1px;
  text-align: center;
  width: 47px;
}
@media (max-width: 1199.98px) {
  .room-list a {
    display: inline-block;
    margin-bottom: 10px;
  }
}
.room-list a:first-child {
  border: 2px solid #181A20;
  border-radius: 12px 0 0 12px;
  margin-right: 0;
}
.room-list a:last-child {
  border-radius: 0 12px 12px 0;
  margin-right: 0;
}

.selection:first-child label {
  border-radius: 12px 0px 0px 12px;
}
.selection:last-child label {
  border-radius: 0px 12px 12px 0px;
}
.selection label {
  background-color: #ffffff;
  border: 2px solid transparent;
  cursor: pointer;
  display: inline-block;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 13px;
  outline: 1px solid #DDDDDD;
  margin-right: 1px;
  padding: 7px 12px;
}

.selection input[type=radio] {
  display: none;
}

.selection input[type=radio]:checked ~ label {
  border: 2px solid #181A20;
  outlinie: transparent;
  z-index: 1;
}

.location-area {
  position: relative;
}
.location-area .btn {
  background-color: #ffffff;
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  height: 50px;
  line-height: 35px;
  padding-left: 15px;
}
.location-area .btn:before, .location-area .btn:after {
  display: none;
}

.space-area .form-control {
  max-width: 130px;
  height: 50px;
}

.feature-button {
  align-items: center;
  display: flex;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  position: relative;
}
.feature-button span {
  background-color: #F7F7F7;
  border-radius: 50%;
  color: var(--headings-color);
  height: 40px;
  line-height: 45px;
  margin-right: 10px;
  text-align: center;
  width: 40px;
}

.reset-button {
  align-items: center;
  display: flex;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}
.reset-button span {
  display: flex;
  font-size: 16px;
  padding-right: 10px;
  line-height: initial;
}

.dropdown-lists {
  position: relative;
}
.dropdown-lists .dropdown-toggle:after {
  display: none;
}
.dropdown-lists .open-btn {
  background-color: #ffffff;
  border-radius: 60px;
  border: none;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  cursor: pointer;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  font-weight: 500;
  letter-spacing: 0em;
  padding: 7px 20px;
}
.dropdown-lists .dropdown-menu,
.dropdown-lists .drop_content,
.dropdown-lists .drop_content2,
.dropdown-lists .drop_content3,
.dropdown-lists .drop_content4 {
  background-color: #ffffff;
  border-top: 1px solid #eaeaea;
  border-radius: 6px;
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  left: 0px;
  padding: 20px 0 10px;
  position: absolute;
  top: 50px;
  width: 260px;
  z-index: 99;
}
.dropdown-lists .dropdown-menu:before,
.dropdown-lists .drop_content:before,
.dropdown-lists .drop_content2:before,
.dropdown-lists .drop_content3:before,
.dropdown-lists .drop_content4:before {
  border-color: transparent transparent #ffffff transparent;
  border-style: solid;
  border-width: 0 5px 5px 5px;
  content: "";
  height: 0;
  left: 30px;
  position: absolute;
  top: -5px;
  width: 0;
}
.dropdown-lists .dropdown-menu.dd3,
.dropdown-lists .dropdown-menu.dd4 {
  width: 330px;
}
.dropdown-lists .done-btn {
  height: 36px;
  line-height: 5px;
}
.dropdown-lists.at-home8 .open-btn {
  border-radius: 12px;
  border: 1px solid #DDDDDD;
  box-shadow: none;
  height: 55px;
  line-height: 40px;
  width: 100%;
  position: relative;
}
.dropdown-lists.at-home8 .open-btn i {
  position: absolute;
  right: 15px;
}

.agent-page-meta .form-control {
  border-radius: 40px;
  height: 44px;
  max-width: 300px;
  width: 100%;
}
.agent-page-meta .open-btn {
  border: 1px solid #DDDDDD;
  box-shadow: none;
  padding: 8px 20px;
}

.list-news-style .news-img {
  border-radius: 6px;
  overflow: hidden;
}
.list-news-style .title span {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}
.list-news-style .list-meta a {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}
.list-news-style .new-text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}

.sidebar-widget {
  background-color: #ffffff;
  border-radius: 12px;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  padding: 30px;
  position: relative;
}

.search_area input::placeholder {
  font-size: 14px;
}

.category-list a {
  color: #181A20;
  display: block;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  font-size: 14px;
  line-height: 35px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.category-list a:hover {
  font-weight: 600;
}

.tag-list a {
  border: 1px solid #DDDDDD;
  border-radius: 60px;
  display: inline-block;
  margin-bottom: 10px;
  margin-right: 10px;
  padding: 6px 20px;
}

/* Table Styles */
.table-style1 {
  position: relative;
}
.table-style1 .table thead tr {
  background-color: #F1FCFA;
  overflow: hidden;
}
.table-style1 .table th {
  color: #222222;
  line-height: 55px;
  padding-left: 27px;
}
.table-style1 .table th:first-child {
  border-radius: 8px 0 0 8px;
}
.table-style1 .table th:last-child {
  border-radius: 0 8px 8px 0;
}
.table-style1 .table td {
  border-bottom: 1px solid #DDDDDD;
  font-family: var(--title-font-family);
  line-height: 52px;
  padding-left: 27px;
}

.table-style2 {
  position: relative;
}
.table-style2 table.table .t-head tr {
  background-color: #F7F7F7;
  overflow: hidden;
}
.table-style2 table.table .t-head th {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  line-height: 22px;
  padding-bottom: 30px;
  padding-top: 30px;
}
.table-style2 table.table .t-head2 th {
  padding-bottom: 22px;
  padding-top: 30px;
}
.table-style2 table.table th:first-child {
  border-radius: 8px 0 0 8px;
}
.table-style2 table.table th:last-child {
  border-radius: 0 8px 8px 0;
}
.table-style2 .t-body th {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  line-height: 23px;
  letter-spacing: 0em;
  padding-bottom: 30px;
  padding-top: 30px;
}
.table-style2 .t-body tr {
  border: 1px solid #DDDDDD;
}
.table-style2 .t-body td {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  padding-bottom: 30px;
  padding-top: 30px;
}
.table-style2 .t-body .check_circle {
  background-color: #F7F7F7;
  border-radius: 50%;
  color: var(--headings-color);
  display: block;
  font-size: 10px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  width: 25px;
}
.table-style2 .t-body .check_circle_close {
  background-color: rgba(235, 103, 83, 0.07);
  border-radius: 50%;
  color: #EB6753;
  display: block;
  font-size: 10px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  width: 25px;
}

.table-style2 table.table > :not(caption) > * > * {
  border-bottom-width: 0;
}

.table-style3.table {
  border-spacing: inherit;
  border-collapse: separate;
}
.table-style3 .t-head {
  background-color: #f5f9fa;
  border-radius: 12px;
}
.table-style3 .t-head th {
  border: 0;
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  padding: 20px 30px;
}
.table-style3 .t-body th {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  padding: 20px 30px;
}
.table-style3 .t-body td {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
  padding: 20px 30px;
}
.table-style3 .t-body tr:last-child {
  border-color: transparent;
}
.table-style3 .icon {
  background-color: #ffffff;
  border-radius: 4px;
  color: var(--headings-color);
  height: 40px;
  line-height: 40px;
  text-align: center;
  width: 40px;
  -webkit-transition: all 0.3s ease 0s ease;
  -moz-transition: all 0.3s ease 0s ease;
  -ms-transition: all 0.3s ease 0s ease;
  -o-transition: all 0.3s ease 0s ease;
  transition: all 0.3s ease 0s ease;
}
.table-style3 .icon:hover {
  background-color: #F7F7F7;
}
.table-style3.at-savesearch .t-body tr:last-child {
  border-color: #DDDDDD;
}

.invoice_table {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  border-radius: 12px;
  position: relative;
}
.invoice_table .wrapper {
  padding: 120px 100px;
}
@media (max-width: 991.98px) {
  .invoice_table .wrapper {
    padding: 60px 30px;
  }
}
.invoice_table .tblh_row {
  background-color: #F7F7F7;
  border-radius: 8px;
}
.invoice_table .tblh_row th {
  color: var(--headings-color);
}
.invoice_table .tbleh_title,
.invoice_table .tbl_title,
.invoice_table .tblpr_title,
.invoice_table .tblp_title {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 600;
  font-size: 15px;
  padding-bottom: 20px;
  padding-left: 30px;
  padding-top: 25px;
}
.invoice_table .tbl_title,
.invoice_table .tblpr_title {
  font-size: 14px;
  font-weight: 400;
}

.invoice_footer {
  background-color: #ffffff;
  border-radius: 0 0 6px 6px;
  border-top: 1px solid #DDDDDD;
  padding: 45px 0 40px;
}

/* Essential utility classes */
.ff-heading {
  font-family: var(--title-font-family);
}

.text-white {
  color: #ffffff;
}

.bgc-white {
  background-color: #ffffff;
}

.bgc-transparent {
  background-color: transparent;
}

.text-gray {
  color: #BEBDBD;
}

.bgc-gray {
  background-color: #BEBDBD;
}

.dark-color,
.heading-color,
.title-color {
  color: var(--headings-color);
}

.bgc-dark {
  background-color: var(--headings-color);
}

.body-light-color {
  color: #717171;
}

.text-thm {
  color: #EB6753;
}

.text-thm2 {
  color: #EE4C34;
}

.bgc-thm {
  background-color: #EB6753;
}

.bgc-thm2 {
  background-color: #EE4C34;
}

.bgc-f7 {
  background-color: #f5f9fa;
}

.bgc-71 {
  background-color: #717171;
}

.text-thm3 {
  color: #5BBB7B;
}

.bgc-thm-light {
  background-color: rgba(235, 103, 83, 0.05);
}

.bgc-thm3-light {
  background-color: rgba(91, 187, 123, 0.15);
}

.review-color {
  color: #E59819;
}

.review-color2 {
  color: #C4C640;
}

/*== Fonts Size, Font Weights, Display & Position ==*/
.fz0 {
  font-size: 0px;
}

.fz6 {
  font-size: 6px;
}

.fz7 {
  font-size: 7px;
}

.fz8 {
  font-size: 8px;
}

.fz9 {
  font-size: 9px;
}

.fz10 {
  font-size: 10px;
}

.fz11 {
  font-size: 11px;
}

.fz12 {
  font-size: 12px;
}

.fz13 {
  font-size: 13px;
}

.fz14 {
  font-size: 14px;
}

.fz15 {
  font-size: 15px;
}

.fz16 {
  font-size: 16px;
}

.fz17 {
  font-size: 17px;
}

.fz18 {
  font-size: 18px;
}

.fz19 {
  font-size: 19px;
}

.fz20 {
  font-size: 20px;
}

.fz24 {
  font-size: 24px;
}

.fz26 {
  font-size: 26px;
}

.fz30 {
  font-size: 30px;
}

.fz40 {
  font-size: 40px;
}

.fz45 {
  font-size: 45px;
}

.fz48 {
  font-size: 48px;
}

.fz50 {
  font-size: 50px;
}

.fz55 {
  font-size: 55px;
}

.fz60 {
  font-size: 60px;
}

.fz72 {
  font-size: 72px;
}

.fz100 {
  font-size: 100px;
}

.lh0 {
  line-height: 0px;
}

.lh30 {
  line-height: 30px;
}

.h510 {
  height: 510px;
}

.h550 {
  height: 550px;
}

.h580 {
  height: 580px;
}

.h600 {
  height: 600px;
}

.h250 {
  height: 250px;
}

.wa {
  width: auto;
}

.w100 {
  width: 100%;
}

.maxw100 {
  max-width: 100%;
}

.maxw140 {
  max-width: 140px;
}

.maxw1600 {
  max-width: 1600px;
}

.maxw1800 {
  max-width: 1800px;
}

.maxw1850 {
  max-width: 1850px;
}

.fw300 {
  font-weight: 300;
}

.fw400 {
  font-weight: 400;
}

.fw500 {
  font-weight: 500;
}

.fw600 {
  font-weight: 600;
}

.fw700 {
  font-weight: 700;
}

.fw800 {
  font-weight: 800;
}

.fw900 {
  font-weight: 900;
}

.fwn {
  font-weight: normal;
}

.fwb {
  font-weight: bold;
}

.db {
  display: block;
}

.dib {
  display: inline-block;
}

.dif {
  display: inline-flex;
}

.df {
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
}

.dfr {
  display: flow-root;
}

.dn {
  display: none;
}

.ovh {
  overflow: hidden;
}

.ovv {
  overflow: visible;
}

.posa {
  position: absolute;
}

.posr {
  position: relative;
}

.vam {
  vertical-align: middle;
}

.zi0 {
  z-index: 0;
}

.zi1 {
  z-index: 1;
}

.zi9 {
  z-index: 9;
}

.zi-1 {
  z-index: -1;
}

.curp {
  cursor: pointer;
}

.bdr1 {
  border: 1px solid #DDDDDD;
}

.bdrb1 {
  border-bottom: 1px solid #DDDDDD;
}

.bb-white-light {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.bdrt1 {
  border-top: 1px solid #DDDDDD;
}

.bdrl1 {
  border-left: 1px solid #DDDDDD;
}

.bdrr1 {
  border-right: 1px solid #DDDDDD;
}

.border-title-color {
  border: 1px solid #181A20;
}

.bbn {
  border-bottom: none;
}

.border-none,
.no-border {
  border: none;
}

.white-bdrt1 {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.gray-bdrb1 {
  border-bottom: 1px solid #ECEDF2;
}

.gray-bdrt1 {
  border-top: 1px solid #ECEDF2;
}

.bdrs0 {
  border-radius: 0;
}

.bdrs6 {
  border-radius: 6px;
}

.bdrs12 {
  border-radius: 12px;
}

.bdrs24 {
  border-radius: 24px;
}

.bdrs60 {
  border-radius: 60px;
}

.bdrs12-right-y {
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
}

.before-none:before {
  display: none;
}

.after-none:after {
  display: none;
}

.wa {
  width: auto !important;
}

.w90 {
  width: 90px;
}

.text {
  color: var(--headings-color);
  font-family: var(--title-font-family);
  font-weight: 400;
}

.default-box-shadow1 {
  -webkit-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -moz-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  -o-box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
  box-shadow: 0px 10px 40px rgba(24, 26, 32, 0.05);
}

.default-box-shadow2 {
  -webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
}

.default-box-shadow3 {
  -webkit-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -moz-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  -o-box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
  box-shadow: 0px 0px 50px rgba(24, 26, 32, 0.07);
}

.default-box-shadow4 {
  -webkit-box-shadow: 0px 10px 35px rgba(5, 16, 54, 0.1);
  -moz-box-shadow: 0px 10px 35px rgba(5, 16, 54, 0.1);
  -o-box-shadow: 0px 10px 35px rgba(5, 16, 54, 0.1);
  box-shadow: 0px 10px 35px rgba(5, 16, 54, 0.1);
}

.default-box-shadow5 {
  -webkit-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -moz-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  -o-box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
  box-shadow: 0px 6px 15px rgba(64, 79, 104, 0.05);
}
.dashboard_header_logo .logo img{
  width: 150px;
}
@media (max-width: 767.98px) {
  .bdrrn-sm {
    border-right: none;
  }
}
@media (max-width: 767.98px) {
  .slider-dib-sm.owl-theme .owl-stage-outer {
    display: inline-block;
  }

  .bb1-sm {
    border-bottom: 1px solid #DDDDDD;
  }
}

/*# sourceMappingURL=style.css.map */

.sidebar_list_item {
    margin-bottom: 5px;
}
.log-reg-form img{
  width: 200px;
}
a.mobile_logo img {
    width: 150px;
}
.dashboard_footer .row {
      justify-content: center !important;
}
.login-pages{
  background: url(../images/hero.jpg) no-repeat;
  background-size: cover;
}


.add-to-compare .remove-compare-icon{
    display: none !important;
}

.remove-compare-property .add-to-compare-icon{
    display: none !important;
}
