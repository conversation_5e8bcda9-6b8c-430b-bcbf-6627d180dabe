
/*** 

====================================================================
  Listing Maps
====================================================================

***/
.cluster-visible {
  text-align: center;
  font-size: 16px !important;
  color: #fff !important;
  font-weight: 400 !important;
  border-radius: 50%;
  width: 36px !important;
  height: 36px !important;
  line-height: 36px !important;
  background-color: #021f4a;
  border: 7px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 7px 30px rgba(33, 33, 33, 0.3);
  box-sizing: content-box;
  background-clip: content-box;
}
.map-marker-container {
  position: absolute;
  margin-top: 10px;
  -webkit-transform: translate3d(-50%, -100%, 0);
  transform: translate3d(-50%, -100%, 0);
}
.marker-container {
  position: relative;
  margin: 10px auto;
  top: -5px;
  width: 46px;
  height: 46px;
  z-index: 1;
  border-radius: 50%;
  cursor: pointer;
}
.face {
  position: absolute;
  width: 46px;
  height: 46px;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  text-align: center;
  border-radius: 50%;
  color: #fff;
  z-index: 100;
  background: #4cbfd8;
  font-size: 24px;
  box-sizing: content-box;
  background-clip: content-box;
  line-height: 46px;
}
.face:before {
  position: absolute;
  left: -7px;
  top: -7px;
  height: 60px;
  width: 60px;
  border-radius: 50%;
  border: 7px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 7px 30px rgba(33, 33, 33, 0.1);
  content: "";
}
.marker-arrow {
  width: 0;
  height: 0;
  content: "";
  /*border-style: solid;
  border-width: 20px 15px 0;
  border-color: rgba(255, 255, 255, 1) transparent transparent;*/
  top: 46px;
  left: 8px;
  position: absolute;
  z-index: 999;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.map-marker-container:hover .marker-container .marker-card,
.clicked .marker-container .marker-card,
.map-marker-container.clicked .marker-card {
  -webkit-transform: rotateY(360deg);
  transform: rotateY(360deg);
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}
#singleListingMap .marker-container {
  cursor: default;
}
.map-marker-container[data-marker_id="0"] .marker-arrow:before {
  border-color: #E8543E transparent transparent;
}
.map-marker-container[data-marker_id="1"] .marker-arrow:before {
  border-color: #EB6753 transparent transparent;
}
.map-marker-container[data-marker_id="2"] .marker-arrow:before {
  border-color: #E8543E transparent transparent;
}
.map-marker-container[data-marker_id="3"] .marker-arrow:before {
  border-color: #35c2a5 transparent transparent;
}
.map-marker-container[data-marker_id="4"] .marker-arrow:before {
  border-color: #EB6753 transparent transparent;
}
.map-marker-container[data-marker_id="5"] .marker-arrow:before {
  border-color: #ffb874 transparent transparent;
}
.map-marker-container[data-marker_id="6"] .marker-arrow:before {
  border-color: #ff007a transparent transparent;
}
.map-marker-container[data-marker_id="0"] .face {
  background-color: #181A20;
}
.map-marker-container[data-marker_id="1"] .face {
  background-color: #181A20;
}
.map-marker-container[data-marker_id="2"] .face {
  background-color: #181A20;
}
.map-marker-container[data-marker_id="3"] .face {
  background-color: #181A20;
}
.map-marker-container[data-marker_id="4"] .face {
  background-color: #181A20;
}
.map-marker-container[data-marker_id="5"] .face {
  background-color: #181A20;
}
.map-marker-container[data-marker_id="6"] .face {
  background-color: #181A20;
}
.marker-card {
  width: 100%;
  height: 100%;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  position: absolute;
  z-index: 1;
}
/*==========================
  Map Listing Item
===========================*/
#map .infoBox {
  margin-left: -55px;
  margin-bottom: 25px;
  width: 380px !important;
}
.map-listing-item {
  position: relative;
}
.map-listing-item .infoBox-close {
  position: absolute;
  right: 10px;
  top: 10px;
  height: 25px;
  width: 25px;
  line-height: 25px;
  border-radius: 3px;
  line-height: 25px;
  text-align: center;
  background: #f4f4f4;
  color: #222222;
  z-index: 9;
  cursor: pointer;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.map-listing-item .infoBox-close:hover {
  background-color: #021f4a;
  color: #ffffff;
}
.map-listing-item .inner-box {
  position: relative;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.09);
}
.map-listing-item .image-box {
  position: absolute;
}
.map-listing-item .image {
  border-radius: 8px;
  float: left;
  margin-left: 10px;
  margin-top: 20px;
  overflow: hidden;
}
.map-listing-item .image img {
  border-radius: 8px;
  display: block;
  max-width: 100%;
  height: auto;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  height: 80px;
  width: 90px;
}
.map-listing-item .image:before {
  display: none;
}
.map-listing-item .inner-box:hover .image img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}
.map-listing-item .content {
  margin-left: 115px;
  margin-top: 25px;
  position: relative;
  width: 100%;
}
.map-listing-item .tags {
  position: absolute;
  left: 0;
  top: 0;
  padding: 20px 20px;
  z-index: 1;
}
.map-listing-item .tags span {
  position: relative;
  display: inline-block;
  margin-right: 10px;
  height: 30px;
  padding: 5px 30px;
  line-height: 22px;
  font-size: 14px;
  color: #1b2032;
  background-color: #ffffff;
  border-radius: 50px;
}
.map-listing-item .like-btn {
  position: absolute;
  right: 25px;
  top: 30px;
  font-size: 14px;
  line-height: 1em;
  color: #ffffff;
  z-index: 2;
}
.map-listing-item .like-btn span {
  margin-right: 10px;
}
.map-listing-item .user-thumb {
  position: relative;
  height: 65px;
  width: 65px;
  border-radius: 50%;
  overflow: hidden;
  margin-top: -60px;
  z-index: 9;
}
.map-listing-item .user-thumb img {
  display: block;
  width: 100%;
}
.map-listing-item .title {
  color: #ffffff;
  margin-left: 3px;
}
.map-listing-item h3 {
  position: relative;
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
  margin-bottom: 7px;
}
.map-listing-item h3 a {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.map-listing-item h3 .icon {
  margin-left: 10px;
}
.map-listing-item .text {
  position: relative;
  margin-bottom: 10px;
}
.map-listing-item .info {
  position: relative;
  display: flex;
  padding-left: 0;
  /*display: -webkit-box;
  -webkit-box-pack: justify;
  justify-content: space-between;*/
}
.map-listing-item .info li {
  color: #222222;
  position: relative;
  font-size: 15px;
  font-weight: normal;
  line-height: 22px;
}
.map-listing-item .info li:first-child{
  margin-right: 15px;
}
.map-listing-item .info li span {
  color: #717171;
  margin-right: 7px;
}
.map-listing-item .bottom-box {
  position: relative;
  display: -webkit-box;
  display: flex;
  height: 120px;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  padding: 10px;
}
.map-listing-item .bottom-box:before{
  background-color: #ffffff;
  bottom: -10px;
  content: "";
  height: 25px;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  width: 25px;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.map-listing-item .status {
  display: none;
  font-size: 14px;
  color: #5c6770;
  text-align: center;
}
.map-listing-item .places {
  display: none;
  position: relative;
  display: -webkit-box;
  display: flex;
}
.map-listing-item .place {
  display: none;
  position: relative;
  color: #4cbfd8;
  font-size: 14px;
  margin-right: 30px;
}
.map-listing-item .place .icon {
  position: relative;
  display: inline-block;
  height: 30px;
  width: 30px;
  border-radius: 50%;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
  font-size: 16px;
  background-color: #4cbfd8;
  margin-right: 7px;
}
.map-listing-item .count {
  position: relative;
  height: 34px;
  width: 34px;
  display: block;
  font-size: 11px;
  color: #5c6770;
  font-weight: 400;
  border: 2px solid #e6e8ed;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
}
.star-rating {
  display: none;
  font-size: 12px;
  padding: 0;
}