.dashboard_navigationbar {
margin-bottom: 30px;
}
.dashboard_navigationbar .dropbtn {
background-color: #fff;
color: #181A20;
border: none;
border-radius: 5px;
display: block;
font-size: 14px;
height: 70px;
margin-bottom: 10px;
outline: none;
padding: 20px 30px;
position: relative;
text-align: left;
-webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
-moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
-o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
width: 100%;
}
.dashboard_navigationbar .dropbtn i {
font-size: 16px;
}
.dashboard_navigationbar .dropbtn:hover, .dashboard_navigationbar .dropbtn:focus {
background-color: #fff;
color: #181A20;
border: none;
cursor: pointer;
outline: none;
}
.dashboard_navigationbar .dropdown {
position: relative;
}
.dashboard_navigationbar .dropdown-content {
box-shadow: none;
display: none;
height: auto;
min-width: 160px;
overflow: auto;
position: absolute;
top: 0;
}
.dashboard_navigationbar .dropdown-content a {
color: black;
padding: 10px 30px;
text-decoration: none;
display: block;
}
.dashboard_navigationbar .dropdown-content li {
height: 50px;
line-height: 30px;
padding-left: 0;
position: relative;
}
.dashboard_navigationbar .dropdown-content li a {
font-size: 14px;
color: #181A20;
}
.dashboard_navigationbar .dropdown-content li a span {
font-size: 18px;
padding-right: 10px;
vertical-align: middle;
}
.dashboard_navigationbar .dropdown-content li.active a {
background-color: #181A20;
color: #FFFFFF;
}
.dashboard_navigationbar .dropdown a:hover {
color: #231f20;
}
.dashboard_navigationbar .show {
background-color: #fff;
border-radius: 12px;
display: block;
-webkit-animation: fadein 2s;
-moz-animation: fadein 2s;
-ms-animation: fadein 2s;
-o-animation: fadein 2s;
animation: fadein 1s;
overflow: hidden;
padding: 15px 0;
position: relative;
min-height: auto;
min-width: auto;
-webkit-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
-moz-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
-o-box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
box-shadow: 0px 1px 4px rgba(24, 26, 32, 0.07);
width: 100%;
}
@keyframes fadein {
from {
opacity:0
}
to {
opacity:1
}
}
.dropdown-content:before {
display: none;
}