<?php

/**
 * Subdomain Property Filtering Test Script
 * 
 * This script tests the subdomain-specific property filtering functionality
 * to ensure each subdomain only shows relevant property types and respects
 * price ranges and criteria.
 * 
 * Run this script from the Laravel application root:
 * php test_subdomain_property_filtering.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Property;
use App\Models\SubDomain;
use App\Services\SubdomainPropertyFilterService;
use Illuminate\Support\Facades\DB;

echo "🧪 Testing Subdomain Property Filtering\n";
echo "=====================================\n\n";

// Test data
$testCompanyId = 9; // Seamless Property Solutions
$subdomains = ['income', 'taxbenefits', 'fhb', 'projects'];

echo "Testing for Company ID: $testCompanyId\n";
echo "Subdomains to test: " . implode(', ', $subdomains) . "\n\n";

foreach ($subdomains as $subdomainSlug) {
    echo "🔍 Testing Subdomain: $subdomainSlug\n";
    echo str_repeat('-', 50) . "\n";
    
    // Get subdomain record
    $subdomain = SubDomain::withoutGlobalScopes()
        ->where('subdomain', $subdomainSlug)
        ->where('company_id', $testCompanyId)
        ->where('status', true)
        ->first();
    
    if (!$subdomain) {
        echo "❌ Subdomain record not found for: $subdomainSlug\n\n";
        continue;
    }
    
    echo "✅ Subdomain record found\n";
    echo "   ID: {$subdomain->id}\n";
    echo "   Name: {$subdomain->name}\n";
    echo "   Company ID: {$subdomain->company_id}\n";
    
    // Test allowed property types
    $allowedTypes = SubdomainPropertyFilterService::getAllowedPropertyTypes($subdomainSlug);
    echo "   Allowed Property Type IDs: [" . implode(', ', $allowedTypes) . "]\n";
    
    // Test price range
    $priceRange = SubdomainPropertyFilterService::getPriceRange($subdomainSlug);
    echo "   Price Range: ";
    if ($priceRange['min'] !== null || $priceRange['max'] !== null) {
        echo "{$priceRange['min']} - {$priceRange['max']}\n";
    } else {
        echo "No limit\n";
    }
    
    // Test actual property filtering
    $baseQuery = Property::withoutGlobalScopes()
        ->where('company_id', $testCompanyId)
        ->whereHas('propertySetting', function($q) {
            $q->where('is_sold', 0); // Only unsold properties
        });
    
    $totalProperties = $baseQuery->count();
    
    // Apply subdomain filtering
    $filteredQuery = clone $baseQuery;
    $filteredQuery = SubdomainPropertyFilterService::applySubdomainFilters(
        $filteredQuery, 
        $subdomainSlug, 
        $testCompanyId
    );
    
    $filteredProperties = $filteredQuery->count();
    
    echo "   Total Properties (Company): $totalProperties\n";
    echo "   Filtered Properties (Subdomain): $filteredProperties\n";
    
    // Get sample properties
    $sampleProperties = $filteredQuery->with(['propertyTypes', 'propertySetting'])
        ->limit(3)
        ->get();
    
    if ($sampleProperties->count() > 0) {
        echo "   Sample Properties:\n";
        foreach ($sampleProperties as $property) {
            $propertyTypes = $property->propertyTypes->pluck('title')->join(', ');
            $price = $property->propertySetting->sale_price ?? 'N/A';
            echo "     - {$property->title} | Types: {$propertyTypes} | Price: $" . number_format($price) . "\n";
        }
    } else {
        echo "   ⚠️  No properties found for this subdomain\n";
    }
    
    // Test specific criteria for each subdomain
    echo "   Testing Subdomain-Specific Criteria:\n";
    switch ($subdomainSlug) {
        case 'fhb':
            // Test FHB price limit (<$750k)
            $expensiveProperties = clone $filteredQuery;
            $expensiveProperties->where('sale_price', '>', 750000);
            $expensiveCount = $expensiveProperties->count();
            echo "     - Properties over $750k (should be 0): $expensiveCount\n";
            break;
            
        case 'income':
            // Test positive cashflow properties
            $cashflowProperties = clone $filteredQuery;
            $cashflowProperties->where(function($q) {
                $q->whereRaw('rental_price > (sale_price * 0.005)')
                  ->orWhere('is_positive_cashflow', true);
            });
            $cashflowCount = $cashflowProperties->count();
            echo "     - Positive cashflow properties: $cashflowCount\n";
            break;
            
        case 'taxbenefits':
            // Test metro location properties
            $metroProperties = clone $filteredQuery;
            $metroProperties->whereHas('propertySetting.city', function($q) {
                $q->whereIn('name', ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide']);
            });
            $metroCount = $metroProperties->count();
            echo "     - Metro location properties: $metroCount\n";
            break;
            
        case 'projects':
            // Test land size criteria (300-600m²)
            $landSizeProperties = clone $filteredQuery;
            $landSizeProperties->whereBetween('land_size', [300, 600]);
            $landSizeCount = $landSizeProperties->count();
            echo "     - Properties with 300-600m² land: $landSizeCount\n";
            break;
    }
    
    echo "\n";
}

echo "🎯 Testing Property Type Visibility\n";
echo str_repeat('-', 50) . "\n";

// Test property type visibility for each subdomain
$propertyTypeIds = [88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102];

foreach ($subdomains as $subdomainSlug) {
    echo "Subdomain: $subdomainSlug\n";
    foreach ($propertyTypeIds as $typeId) {
        $isVisible = SubdomainPropertyFilterService::isPropertyVisibleOnSubdomain($typeId, $subdomainSlug);
        $status = $isVisible ? '✅' : '❌';
        echo "  Property Type $typeId: $status\n";
    }
    echo "\n";
}

echo "🧪 Testing Property Model Global Scope\n";
echo str_repeat('-', 50) . "\n";

// Test if properties are automatically filtered by subdomain when using Property model
// This would simulate accessing properties on a subdomain

foreach ($subdomains as $subdomainSlug) {
    echo "Testing global scope for subdomain: $subdomainSlug\n";
    
    // Simulate setting current subdomain in config
    $subdomain = SubDomain::withoutGlobalScopes()
        ->where('subdomain', $subdomainSlug)
        ->where('company_id', $testCompanyId)
        ->where('status', true)
        ->first();
    
    if ($subdomain) {
        config(['current_subdomain' => $subdomain]);
        
        // Now query properties normally - should be automatically filtered
        $propertiesWithGlobalScope = Property::count();
        echo "  Properties with global scope applied: $propertiesWithGlobalScope\n";
        
        // Compare with manual filtering
        $manualFilteredProperties = Property::withoutGlobalScopes()
            ->where('company_id', $testCompanyId)
            ->whereHas('propertySetting', function($q) {
                $q->where('is_sold', 0);
            });
        
        $manualFilteredProperties = SubdomainPropertyFilterService::applySubdomainFilters(
            $manualFilteredProperties, 
            $subdomainSlug, 
            $testCompanyId
        )->count();
        
        echo "  Properties with manual filtering: $manualFilteredProperties\n";
        
        $globalScopeWorking = ($propertiesWithGlobalScope <= $manualFilteredProperties) ? '✅' : '❌';
        echo "  Global scope working correctly: $globalScopeWorking\n";
    }
    
    echo "\n";
}

// Clear config
config(['current_subdomain' => null]);

echo "✨ Test Complete!\n";
echo "\nExpected Results:\n";
echo "- Income subdomain: Properties with IDs 88-91 (Dual Occupancy, Co-living, SMSF, Regional high-yield)\n";
echo "- Tax Benefits subdomain: Properties with IDs 92-95 (Metro apartments, Growth corridor, Prestige, Premium duplex)\n";
echo "- FHB subdomain: Properties with IDs 96-99, all under $750k (Affordable, Low deposit, Dual living, Scheme-approved)\n";
echo "- Projects subdomain: Properties with IDs 100-102 (Land lots, House & land, Knockdown-rebuild)\n";
echo "\nIf filtering is working correctly, each subdomain should only show its designated property types.\n";