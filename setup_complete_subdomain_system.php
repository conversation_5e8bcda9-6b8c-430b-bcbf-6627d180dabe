<?php

/**
 * COMPLETE SUBDOMAIN SYSTEM SETUP SCRIPT
 * 
 * This script sets up the entire subdomain management system for Seamless Property Solutions
 * including subdomains, property types, CMS pages, and all necessary database entries.
 * 
 * HOW TO RUN ON LIVE SERVER:
 * 
 * 1. Upload this file to the root directory of your Laravel application on the live server
 * 
 * 2. SSH into your live server and navigate to your Laravel application directory:
 *    cd /path/to/your/laravel/app
 * 
 * 3. Run the script via PHP CLI:
 *    php setup_complete_subdomain_system.php
 * 
 * 4. Monitor the output for any errors or confirmations
 * 
 * 5. After successful completion, delete this script file for security:
 *    rm setup_complete_subdomain_system.php
 * 
 * IMPORTANT NOTES:
 * - This script is designed to be run ONCE on a fresh live environment
 * - Make sure to backup your database before running this script
 * - Update the $companyId variable below to match your live company ID
 * - Update the $userId variable to match your live admin user ID
 * - Test on staging environment first if possible
 * 
 * WHAT THIS SCRIPT DOES:
 * 1. Creates SubDomain Management feature and permissions
 * 2. Creates specialized property types for each subdomain focus
 * 3. Creates 4 subdomains: income, taxbenefits, fhb, projects
 * 4. Creates comprehensive CMS pages for each subdomain
 * 5. Sets up property type mappings for targeted listings
 * 
 * REQUIREMENTS:
 * - Laravel 10+ with proper database configuration
 * - MySQL/MariaDB database
 * - Company with ID specified below must exist
 * - User with ID specified below must exist and have admin permissions
 */

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "🚀 Starting Complete Subdomain System Setup...\n";
echo "================================================\n";

// CONFIGURATION - UPDATE THESE VALUES FOR YOUR LIVE ENVIRONMENT
$companyId = 9;  // UPDATE: Your live company ID
$userId = 1;     // UPDATE: Your live admin user ID

echo "Configuration:\n";
echo "- Company ID: {$companyId}\n";
echo "- User ID: {$userId}\n";
echo "- Environment: " . config('app.env') . "\n\n";

// Verify company and user exist
$company = DB::table('companies')->where('id', $companyId)->first();
$user = DB::table('users')->where('id', $userId)->first();

if (!$company) {
    echo "❌ ERROR: Company with ID {$companyId} not found!\n";
    exit(1);
}

if (!$user) {
    echo "❌ ERROR: User with ID {$userId} not found!\n";
    exit(1);
}

echo "✅ Company verified: {$company->name}\n";
echo "✅ User verified: {$user->name}\n\n";

// STEP 1: Create SubDomain Management Feature
echo "Step 1: Creating SubDomain Management feature...\n";

$featureExists = DB::table('features')->where('name', 'SubDomain Management')->where('company_id', $companyId)->first();
if (!$featureExists) {
    $featureId = DB::table('features')->insertGetId([
        'name' => 'SubDomain Management',
        'slug' => 'subdomain_management',
        'description' => 'Manage multiple subdomains with specialized content and property filtering',
        'company_id' => $companyId,
        'status' => 1,
        'created_at' => now(),
        'updated_at' => now(),
        'created_by' => $userId,
        'updated_by' => $userId
    ]);
    echo "  ✓ Created SubDomain Management feature (ID: {$featureId})\n";
} else {
    echo "  ✓ SubDomain Management feature already exists\n";
}

// STEP 2: Create SubDomain Management Permissions
echo "\nStep 2: Creating SubDomain Management permissions...\n";

$permissions = [
    'subdomain.view' => 'View Subdomains',
    'subdomain.create' => 'Create Subdomains', 
    'subdomain.edit' => 'Edit Subdomains',
    'subdomain.delete' => 'Delete Subdomains'
];

foreach ($permissions as $name => $displayName) {
    $permissionExists = DB::table('permissions')
        ->where('name', $name)
        ->where('team_id', $companyId)
        ->first();
        
    if (!$permissionExists) {
        DB::table('permissions')->insert([
            'name' => $name,
            'display_name' => $displayName,
            'guard_name' => 'web',
            'team_id' => $companyId,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "  ✓ Created permission: {$displayName}\n";
    } else {
        echo "  ✓ Permission already exists: {$displayName}\n";
    }
}

// STEP 3: Create Specialized Property Types
echo "\nStep 3: Creating specialized property types...\n";

$propertyTypes = [
    ['id' => 88, 'type' => 'Dual Occupancy New Build', 'description' => 'SMSF-compliant dual occupancy properties with high rental yields'],
    ['id' => 89, 'type' => 'Co-living Investment', 'description' => 'Purpose-built co-living properties for premium rental returns'],
    ['id' => 90, 'type' => 'SMSF Freehold New Build', 'description' => 'Brand new freehold properties perfect for SMSF investment'],
    ['id' => 91, 'type' => 'Regional High-Yield Package', 'description' => 'House and land packages in regional areas with strong yields'],
    ['id' => 92, 'type' => 'Metro High-Depreciation Apartment', 'description' => 'New apartments in metro areas with aggressive depreciation schedules'],
    ['id' => 93, 'type' => 'Growth Corridor Townhouse', 'description' => 'High-spec townhouses in infrastructure growth corridors'],
    ['id' => 94, 'type' => 'Prestige House & Land', 'description' => 'Premium house and land packages with large deduction profiles'],
    ['id' => 95, 'type' => 'Premium Duplex Development', 'description' => 'High-end duplex builds with substantial tax benefits'],
    ['id' => 96, 'type' => 'FHB Affordable Package', 'description' => 'Affordable house and land packages under $750k for first home buyers'],
    ['id' => 97, 'type' => 'Low Deposit Townhome', 'description' => 'Turnkey townhomes with 5% deposit options and government grant eligibility'],
    ['id' => 98, 'type' => 'Dual Living Starter Home', 'description' => 'Properties with granny flat potential for rental income or family living'],
    ['id' => 99, 'type' => 'Scheme-Approved Property', 'description' => 'Properties approved for shared equity and low deposit government schemes'],
    ['id' => 100, 'type' => 'Premium Land Lot (300-600m²)', 'description' => 'Registered land lots ready for custom builds with flexible builders'],
    ['id' => 101, 'type' => 'Knockdown-Rebuild Block', 'description' => 'Established properties in metro areas perfect for knockdown and rebuild'],
    ['id' => 102, 'type' => 'Corner Lot/Duplex-Suitable', 'description' => 'Corner lots and blocks suitable for dual occupancy development']
];

foreach ($propertyTypes as $propertyType) {
    $exists = DB::table('property_types')->where('id', $propertyType['id'])->first();
    if (!$exists) {
        DB::table('property_types')->insert([
            'id' => $propertyType['id'],
            'type' => $propertyType['type'],
            'description' => $propertyType['description'],
            'company_id' => $companyId,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
            'created_by' => $userId,
            'updated_by' => $userId
        ]);
        echo "  ✓ Created property type: {$propertyType['type']}\n";
    } else {
        echo "  ✓ Property type already exists: {$propertyType['type']}\n";
    }
}

// STEP 4: Create Subdomains
echo "\nStep 4: Creating subdomains...\n";

$subdomains = [
    [
        'subdomain' => 'income',
        'description' => 'Positive Cashflow Properties - SMSF investors, passive income seekers, yield-focused buyers',
        'target_audience' => 'SMSF investors, passive income seekers, yield-focused buyers',
        'property_focus' => 'Dual Occupancy, Co-living/Rooming Houses, SMSF-Compliant Freehold New Builds, Regional High-Yield House & Land Packages',
        'full_domain' => 'income.seamlesspropertysolutions.com.au',
        'property_type_ids' => [88, 89, 90, 91] // Dual Occupancy, Co-living, SMSF Freehold, Regional High-Yield
    ],
    [
        'subdomain' => 'taxbenefits', 
        'description' => 'Negatively Geared / Tax-Minimizing Properties - High-income PAYG earners, professionals',
        'target_audience' => 'High-income PAYG earners, professionals',
        'property_focus' => 'Metro Apartments with High Depreciation, High-Spec Townhouses in Growth Corridors, Prestige House & Land Packages, Premium Duplex Builds',
        'full_domain' => 'taxbenefits.seamlesspropertysolutions.com.au',
        'property_type_ids' => [92, 93, 94, 95] // Metro High-Depreciation, Growth Corridor, Prestige, Premium Duplex
    ],
    [
        'subdomain' => 'fhb',
        'description' => 'First Home Buyer Pathways & Grants - Entry-level buyers, couples, young families',
        'target_audience' => 'Entry-level buyers, couples, young families',
        'property_focus' => 'Affordable House & Land Packages (<$750k), Turnkey Townhomes with Low Deposits, Dual Living Starter Homes, Scheme-Approved Properties',
        'full_domain' => 'fhb.seamlesspropertysolutions.com.au',
        'property_type_ids' => [96, 97, 98, 99] // Affordable Package, Low Deposit, Dual Living, Scheme-Approved
    ],
    [
        'subdomain' => 'projects',
        'description' => 'Land Buyers & Custom Builders - Custom home clients, knockdown rebuilders, dual occupancy developers',
        'target_audience' => 'Custom home clients, knockdown rebuilders, dual occupancy developers',
        'property_focus' => 'Registered Land Lots (300m²–600m²), House & Land with Flexible Builders, Knockdown-Rebuild Blocks, Corner Lots or Duplex-Suitable Land',
        'full_domain' => 'projects.seamlesspropertysolutions.com.au',
        'property_type_ids' => [100, 101, 102] // Premium Land, Knockdown-Rebuild, Corner Lot
    ]
];

$subdomainIds = [];
foreach ($subdomains as $subdomain) {
    $exists = DB::table('sub_domains')
        ->where('subdomain', $subdomain['subdomain'])
        ->where('company_id', $companyId)
        ->first();
        
    if (!$exists) {
        $subdomainId = DB::table('sub_domains')->insertGetId([
            'subdomain' => $subdomain['subdomain'],
            'description' => $subdomain['description'],
            'target_audience' => $subdomain['target_audience'],
            'property_focus' => $subdomain['property_focus'],
            'full_domain' => $subdomain['full_domain'],
            'property_type_ids' => json_encode($subdomain['property_type_ids']),
            'company_id' => $companyId,
            'status' => true,
            'created_at' => now(),
            'updated_at' => now(),
            'created_by' => $userId,
            'updated_by' => $userId
        ]);
        echo "  ✓ Created subdomain: {$subdomain['subdomain']} (ID: {$subdomainId})\n";
        $subdomainIds[$subdomain['subdomain']] = $subdomainId;
    } else {
        echo "  ✓ Subdomain already exists: {$subdomain['subdomain']}\n";
        $subdomainIds[$subdomain['subdomain']] = $exists->id;
    }
}

// STEP 5: Create Comprehensive CMS Pages
echo "\nStep 5: Creating comprehensive CMS pages...\n";

// Income Subdomain Pages
$incomePages = [
    [
        'name' => 'SMSF Investment Guides',
        'slug' => 'smsf-investment-guides',
        'content' => '
<!-- Page Header Section Start -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">SMSF Investment Guides</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">SMSF Investment Guides</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SMSF Guide Content Start -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Complete SMSF Property Investment Guide 2025</h3>
                    <h2 class="text-anime">Master SMSF Property Investment</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Build wealth through Self-Managed Super Fund property investments with expert guidance and proven strategies.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Benefits Section Start -->
        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="Tax Benefits">
                    </div>
                    <h3>Tax Advantages</h3>
                    <ul>
                        <li>15% tax rate during accumulation phase</li>
                        <li>0% tax during pension phase</li>
                        <li>Concessional rental income tax treatment</li>
                        <li>Reduced capital gains tax (33% discount after 12 months)</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Control">
                    </div>
                    <h3>Control & Flexibility</h3>
                    <ul>
                        <li>Direct control over investment decisions</li>
                        <li>Choose your own properties and locations</li>
                        <li>Tailor investment strategy to your goals</li>
                        <li>Potential for higher returns than traditional super</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- CTA Section Start -->
        <div class="row">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.75s" style="text-align: center; background: #f8f9fa; padding: 40px; border-radius: 10px;">
                    <h3>Ready to Start Your SMSF Property Journey?</h3>
                    <p>Our SMSF specialists will guide you through every step of the process, ensuring compliance and maximizing your returns.</p>
                    <div style="margin-top: 30px;">
                        <a href="/page/content/book-strategy-call" class="btn-default" style="margin-right: 15px;">Book Free Strategy Call</a>
                        <a href="/property-listing" class="btn-default btn-border">View SMSF Properties</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>'
    ],

    [
        'name' => 'Positive Cashflow Calculator',
        'slug' => 'cashflow-calculator',
        'content' => '
<!-- Page Header Section Start -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Positive Cashflow Calculator</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Cashflow Calculator</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Calculator Section Start -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Property Investment Calculator</h3>
                    <h2 class="text-anime">Calculate Your Potential Returns</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Discover your potential weekly passive income from our positive cashflow investment properties.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Investment Details</h3>
                    <form id="cashflow-calculator">
                        <div class="form-group">
                            <label>Property Value ($)</label>
                            <input type="number" id="property-value" class="form-control" placeholder="450000" value="450000">
                        </div>
                        
                        <div class="form-group">
                            <label>Weekly Rent ($)</label>
                            <input type="number" id="weekly-rent" class="form-control" placeholder="580" value="580">
                        </div>
                        
                        <button type="button" onclick="calculateCashflow()" class="btn-default">Calculate Cashflow</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Your Investment Results</h3>
                    <div id="results-display">
                        <p>Enter your property details to see potential returns</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function calculateCashflow() {
    const propertyValue = parseFloat(document.getElementById("property-value").value) || 0;
    const weeklyRent = parseFloat(document.getElementById("weekly-rent").value) || 0;
    
    const annualRent = weeklyRent * 52;
    const rentalYield = (annualRent / propertyValue) * 100;
    
    document.getElementById("results-display").innerHTML = 
        "<p><strong>Annual Rental Income:</strong> $" + annualRent.toLocaleString() + "</p>" +
        "<p><strong>Rental Yield:</strong> " + rentalYield.toFixed(2) + "%</p>";
}
</script>'
    ],

    [
        'name' => 'Yield Comparison by State',
        'slug' => 'yield-comparison-states',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Yield Comparison by State</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Yield Comparison</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Yield Analysis Section -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">2025 Property Yield Analysis</h3>
                    <h2 class="text-anime">Where Smart Investors Find The Best Returns</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Compare rental yields across Australian states to maximize your investment returns.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-1.svg" alt="Queensland">
                    </div>
                    <h3>Queensland</h3>
                    <p><strong>Average Yield:</strong> 7.2%</p>
                    <p><strong>SMSF Properties:</strong> 8.1%</p>
                    <p><strong>Dual Occupancy:</strong> 8.8%</p>
                    <ul>
                        <li>Strong mining town returns</li>
                        <li>Regional growth corridors</li>
                        <li>SMSF-compliant new builds</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-2.svg" alt="New South Wales">
                    </div>
                    <h3>New South Wales</h3>
                    <p><strong>Average Yield:</strong> 5.8%</p>
                    <p><strong>Regional NSW:</strong> 7.4%</p>
                    <p><strong>Co-living:</strong> 9.2%</p>
                    <ul>
                        <li>Premium co-living opportunities</li>
                        <li>Hunter Valley investments</li>
                        <li>Metro fringe development</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-3.svg" alt="Victoria">
                    </div>
                    <h3>Victoria</h3>
                    <p><strong>Average Yield:</strong> 5.2%</p>
                    <p><strong>Regional VIC:</strong> 6.9%</p>
                    <p><strong>Student Accommodation:</strong> 8.5%</p>
                    <ul>
                        <li>Geelong corridor growth</li>
                        <li>Regional manufacturing hubs</li>
                        <li>University town yields</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>'
    ],

    [
        'name' => 'Book Strategy Call',
        'slug' => 'book-strategy-call',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Book Your Free Strategy Call</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Book Strategy Call</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Section -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Free 30-Minute Strategy Session</h3>
                    <h2 class="text-anime">Discover Your Perfect Investment Strategy</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Book a complimentary consultation with our property investment specialists.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Schedule Your Call</h3>
                    <form id="strategy-call-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="First Name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Last Name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="email" class="form-control" placeholder="Email Address" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="tel" class="form-control" placeholder="Phone Number" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" required>
                                <option value="">What\'s your investment goal?</option>
                                <option value="smsf">SMSF Property Investment</option>
                                <option value="passive-income">Passive Income Generation</option>
                                <option value="capital-growth">Capital Growth Focus</option>
                                <option value="portfolio-expansion">Portfolio Expansion</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <textarea class="form-control" rows="4" placeholder="Tell us about your investment experience and goals..."></textarea>
                        </div>
                        <button type="submit" class="btn-default">Book My Free Call</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>What We\'ll Cover</h3>
                    <ul>
                        <li>✓ Your investment goals and timeline</li>
                        <li>✓ SMSF compliance requirements</li>
                        <li>✓ Property selection criteria</li>
                        <li>✓ Financing strategies</li>
                        <li>✓ Tax optimization opportunities</li>
                        <li>✓ Next steps for your portfolio</li>
                    </ul>
                    
                    <div style="margin-top: 30px;">
                        <h4>Our Specialists</h4>
                        <p>You\'ll speak directly with qualified property investment advisors who specialize in SMSF and positive cashflow strategies.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById("strategy-call-form").addEventListener("submit", function(e) {
    e.preventDefault();
    alert("Thank you! We\'ll contact you within 24 hours to schedule your free strategy call.");
});
</script>'
    ]
];

echo "Creating Income subdomain pages...\n";
foreach ($incomePages as $page) {
    $exists = DB::table('cms_pages')
        ->where('slug', $page['slug'])
        ->where('subdomain_id', $subdomainIds['income'])
        ->where('company_id', $companyId)
        ->first();
        
    if (!$exists) {
        DB::table('cms_pages')->insert([
            'name' => $page['name'],
            'slug' => $page['slug'],
            'content' => $page['content'],
            'subdomain_id' => $subdomainIds['income'],
            'company_id' => $companyId,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
            'created_by' => $userId,
            'updated_by' => $userId
        ]);
        echo "  ✓ Created: {$page['name']}\n";
    } else {
        echo "  ✓ Already exists: {$page['name']}\n";
    }
}

// Tax Benefits Subdomain Pages
$taxBenefitsPages = [
    [
        'name' => 'Negative Gearing Guide',
        'slug' => 'negative-gearing-guide',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Negative Gearing Guide 2025</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Negative Gearing Guide</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Guide Content -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Maximize Your Tax Benefits</h3>
                    <h2 class="text-anime">Complete Negative Gearing Strategy</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Learn how high-income earners can reduce their tax burden by up to 47% through strategic property investment.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="Tax Savings">
                    </div>
                    <h3>How Negative Gearing Works</h3>
                    <ul>
                        <li>Property expenses exceed rental income</li>
                        <li>Loss can be offset against other income</li>
                        <li>Reduces taxable income and tax payable</li>
                        <li>Capital gains tax discount after 12 months</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Tax Rates">
                    </div>
                    <h3>Tax Savings by Income</h3>
                    <ul>
                        <li><strong>$120k-$180k:</strong> 32.5% tax savings</li>
                        <li><strong>$180k-$300k:</strong> 37% tax savings</li>
                        <li><strong>$300k+:</strong> 45% tax savings</li>
                        <li><strong>Plus Medicare Levy:</strong> Additional 2%</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.75s">
                    <h3>Best Property Types for Negative Gearing</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Metro Apartments</h4>
                            <p>New apartments in Sydney, Melbourne, Brisbane with high depreciation benefits and strong growth potential.</p>
                        </div>
                        <div class="col-md-6">
                            <h4>Growth Corridor Townhouses</h4>
                            <p>High-spec townhouses in infrastructure development areas with substantial deduction profiles.</p>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 30px;">
                        <a href="/property-listing" class="btn-default">View Tax-Optimized Properties</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>'
    ],

    [
        'name' => 'Tax Benefit Calculator',
        'slug' => 'tax-benefit-calculator',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Tax Benefit Calculator</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Tax Calculator</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Calculator Section -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Calculate Your Tax Savings</h3>
                    <h2 class="text-anime">See How Much You Can Save</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Calculate your potential tax savings from property investment deductions.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Your Details</h3>
                    <form id="tax-calculator">
                        <div class="form-group">
                            <label>Annual Income ($)</label>
                            <input type="number" id="annual-income" class="form-control" placeholder="180000" value="180000">
                        </div>
                        
                        <div class="form-group">
                            <label>Property Value ($)</label>
                            <input type="number" id="property-value" class="form-control" placeholder="750000" value="750000">
                        </div>
                        
                        <div class="form-group">
                            <label>Annual Rental Income ($)</label>
                            <input type="number" id="rental-income" class="form-control" placeholder="35000" value="35000">
                        </div>
                        
                        <button type="button" onclick="calculateTaxSavings()" class="btn-default">Calculate Savings</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Your Tax Savings</h3>
                    <div id="tax-results">
                        <p>Enter your details to see potential tax savings</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function calculateTaxSavings() {
    const income = parseFloat(document.getElementById("annual-income").value) || 0;
    const propertyValue = parseFloat(document.getElementById("property-value").value) || 0;
    const rentalIncome = parseFloat(document.getElementById("rental-income").value) || 0;
    
    // Simplified calculation
    let marginalRate = 0.37; // Default for $180k+
    if (income >= 300000) marginalRate = 0.45;
    else if (income >= 180000) marginalRate = 0.37;
    else if (income >= 120000) marginalRate = 0.325;
    
    const annualExpenses = propertyValue * 0.06; // Assume 6% annual expenses
    const annualLoss = annualExpenses - rentalIncome;
    const taxSaving = annualLoss * marginalRate;
    
    document.getElementById("tax-results").innerHTML = 
        "<p><strong>Annual Property Loss:</strong> $" + Math.max(0, annualLoss).toLocaleString() + "</p>" +
        "<p><strong>Tax Saving:</strong> $" + Math.max(0, taxSaving).toLocaleString() + "</p>" +
        "<p><strong>Marginal Tax Rate:</strong> " + (marginalRate * 100) + "%</p>";
}
</script>'
    ],

    [
        'name' => 'Book Tax Strategy Session',
        'slug' => 'book-tax-strategy',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Book Tax Strategy Session</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Tax Strategy Session</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Section -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Expert Tax Strategy Consultation</h3>
                    <h2 class="text-anime">Maximize Your Property Tax Benefits</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Book a session with our qualified tax and property specialists.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Schedule Your Consultation</h3>
                    <form id="tax-strategy-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="First Name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Last Name" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" required>
                                <option value="">Annual Income Range</option>
                                <option value="120-180">$120k - $180k</option>
                                <option value="180-300">$180k - $300k</option>
                                <option value="300+">$300k+</option>
                            </select>
                        </div>
                        <button type="submit" class="btn-default">Book My Tax Strategy Session</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>What We\'ll Discuss</h3>
                    <ul>
                        <li>✓ Your current tax situation</li>
                        <li>✓ Negative gearing strategies</li>
                        <li>✓ Depreciation optimization</li>
                        <li>✓ Property selection for tax benefits</li>
                        <li>✓ Structuring recommendations</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>'
    ]
];

echo "Creating Tax Benefits subdomain pages...\n";
foreach ($taxBenefitsPages as $page) {
    $exists = DB::table('cms_pages')
        ->where('slug', $page['slug'])
        ->where('subdomain_id', $subdomainIds['taxbenefits'])
        ->where('company_id', $companyId)
        ->first();
        
    if (!$exists) {
        DB::table('cms_pages')->insert([
            'name' => $page['name'],
            'slug' => $page['slug'],
            'content' => $page['content'],
            'subdomain_id' => $subdomainIds['taxbenefits'],
            'company_id' => $companyId,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
            'created_by' => $userId,
            'updated_by' => $userId
        ]);
        echo "  ✓ Created: {$page['name']}\n";
    } else {
        echo "  ✓ Already exists: {$page['name']}\n";
    }
}

// FHB Subdomain Pages
$fhbPages = [
    [
        'name' => 'First Home Buyer Checklist',
        'slug' => 'fhb-checklist',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">First Home Buyer Checklist</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">FHB Checklist</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Checklist Content -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Your Complete First Home Journey</h3>
                    <h2 class="text-anime">Step-by-Step Checklist</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Follow our comprehensive checklist to navigate your first home purchase with confidence.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Before You Start</h3>
                    <ul>
                        <li>☐ Check your credit score</li>
                        <li>☐ Calculate your borrowing capacity</li>
                        <li>☐ Save for deposit and costs</li>
                        <li>☐ Research government grants</li>
                        <li>☐ Get pre-approval</li>
                        <li>☐ Find a mortgage broker</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Finding Your Home</h3>
                    <ul>
                        <li>☐ Research suburbs and schools</li>
                        <li>☐ Attend open homes</li>
                        <li>☐ Get building and pest inspections</li>
                        <li>☐ Review strata reports (apartments)</li>
                        <li>☐ Negotiate the price</li>
                        <li>☐ Make an offer</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.75s">
                    <h3>Settlement Process</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <ul>
                                <li>☐ Arrange home insurance</li>
                                <li>☐ Finalize loan approval</li>
                                <li>☐ Book conveyancer/solicitor</li>
                                <li>☐ Arrange final inspection</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul>
                                <li>☐ Complete settlement</li>
                                <li>☐ Collect keys</li>
                                <li>☐ Connect utilities</li>
                                <li>☐ Celebrate your new home!</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>'
    ],

    [
        'name' => 'Government Grants',
        'slug' => 'government-grants',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Government Grants & Concessions</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Government Grants</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Grants Content -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Available Grants & Concessions</h3>
                    <h2 class="text-anime">Save Up to $55,000 on Your First Home</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Access government assistance programs designed to help first home buyers enter the market.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-1.svg" alt="FHOG">
                    </div>
                    <h3>First Home Owner Grant</h3>
                    <p><strong>NSW:</strong> $10,000 (new homes)</p>
                    <p><strong>QLD:</strong> $15,000 (new homes)</p>
                    <p><strong>VIC:</strong> $10,000 (new/substantially renovated)</p>
                    <ul>
                        <li>Available for new construction</li>
                        <li>Property value limits apply</li>
                        <li>Must live in home for 6-12 months</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-2.svg" alt="Stamp Duty">
                    </div>
                    <h3>Stamp Duty Concessions</h3>
                    <p><strong>NSW:</strong> Full exemption up to $650k</p>
                    <p><strong>QLD:</strong> Concessions up to $550k</p>
                    <p><strong>VIC:</strong> Full exemption up to $600k</p>
                    <ul>
                        <li>Savings of $15,000-$40,000</li>
                        <li>Property value thresholds</li>
                        <li>Must be first home purchase</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-3.svg" alt="Shared Equity">
                    </div>
                    <h3>Shared Equity Schemes</h3>
                    <p><strong>Federal:</strong> Up to 40% equity share</p>
                    <p><strong>NSW:</strong> Up to 40% for new homes</p>
                    <p><strong>Regional:</strong> Enhanced assistance</p>
                    <ul>
                        <li>5% deposit options</li>
                        <li>No lenders mortgage insurance</li>
                        <li>Income and property limits</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="1s">
                    <h3>Grant Eligibility Checker</h3>
                    <p>Check which grants and concessions you may be eligible for based on your circumstances.</p>
                    <div style="text-align: center;">
                        <a href="/page/content/book-fhb-planning" class="btn-default">Check My Eligibility</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>'
    ],

    [
        'name' => 'Book First Home Planning',
        'slug' => 'book-fhb-planning',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Book First Home Planning Call</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Book FHB Planning</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Section -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Free First Home Buyer Consultation</h3>
                    <h2 class="text-anime">Get Expert Guidance for Your First Purchase</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Book a complimentary planning session with our first home buyer specialists.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Schedule Your Planning Call</h3>
                    <form id="fhb-planning-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="First Name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Last Name" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" required>
                                <option value="">What\'s your timeline?</option>
                                <option value="3-months">Next 3 months</option>
                                <option value="6-months">Next 6 months</option>
                                <option value="12-months">Next 12 months</option>
                                <option value="planning">Just planning ahead</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <select class="form-control" required>
                                <option value="">Deposit saved?</option>
                                <option value="5-percent">5% saved</option>
                                <option value="10-percent">10% saved</option>
                                <option value="20-percent">20% saved</option>
                                <option value="still-saving">Still saving</option>
                            </select>
                        </div>
                        <button type="submit" class="btn-default">Book My Free Planning Call</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>What We\'ll Cover</h3>
                    <ul>
                        <li>✓ Grant eligibility assessment</li>
                        <li>✓ Borrowing capacity calculation</li>
                        <li>✓ Deposit and cost planning</li>
                        <li>✓ Suburb recommendations</li>
                        <li>✓ Pre-approval strategy</li>
                        <li>✓ Timeline and next steps</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>'
    ]
];

echo "Creating FHB subdomain pages...\n";
foreach ($fhbPages as $page) {
    $exists = DB::table('cms_pages')
        ->where('slug', $page['slug'])
        ->where('subdomain_id', $subdomainIds['fhb'])
        ->where('company_id', $companyId)
        ->first();
        
    if (!$exists) {
        DB::table('cms_pages')->insert([
            'name' => $page['name'],
            'slug' => $page['slug'],
            'content' => $page['content'],
            'subdomain_id' => $subdomainIds['fhb'],
            'company_id' => $companyId,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
            'created_by' => $userId,
            'updated_by' => $userId
        ]);
        echo "  ✓ Created: {$page['name']}\n";
    } else {
        echo "  ✓ Already exists: {$page['name']}\n";
    }
}

// Projects Subdomain Pages
$projectsPages = [
    [
        'name' => 'Build Cost Estimator',
        'slug' => 'build-cost-estimator',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Build Cost Estimator</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Build Cost Estimator</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Calculator Section -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Calculate Your Build Costs</h3>
                    <h2 class="text-anime">Estimate Your Custom Home Budget</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Get an accurate estimate for your custom build project including land, construction, and additional costs.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Project Details</h3>
                    <form id="build-calculator">
                        <div class="form-group">
                            <label>Land Size (m²)</label>
                            <input type="number" id="land-size" class="form-control" placeholder="400" value="400">
                        </div>
                        
                        <div class="form-group">
                            <label>House Size (m²)</label>
                            <input type="number" id="house-size" class="form-control" placeholder="250" value="250">
                        </div>
                        
                        <div class="form-group">
                            <label>Build Quality</label>
                            <select id="build-quality" class="form-control">
                                <option value="standard">Standard ($1,800/m²)</option>
                                <option value="premium">Premium ($2,200/m²)</option>
                                <option value="luxury">Luxury ($2,800/m²)</option>
                            </select>
                        </div>
                        
                        <button type="button" onclick="calculateBuildCost()" class="btn-default">Calculate Costs</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Cost Breakdown</h3>
                    <div id="build-results">
                        <p>Enter your project details to see cost estimates</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function calculateBuildCost() {
    const landSize = parseFloat(document.getElementById("land-size").value) || 0;
    const houseSize = parseFloat(document.getElementById("house-size").value) || 0;
    const buildQuality = document.getElementById("build-quality").value;
    
    let costPerSqm = 1800;
    if (buildQuality === "premium") costPerSqm = 2200;
    if (buildQuality === "luxury") costPerSqm = 2800;
    
    const buildCost = houseSize * costPerSqm;
    const additionalCosts = buildCost * 0.15; // 15% for council, utilities, etc.
    const totalBuildCost = buildCost + additionalCosts;
    
    document.getElementById("build-results").innerHTML = 
        "<p><strong>Construction Cost:</strong> $" + buildCost.toLocaleString() + "</p>" +
        "<p><strong>Additional Costs:</strong> $" + additionalCosts.toLocaleString() + "</p>" +
        "<p><strong>Total Build Cost:</strong> $" + totalBuildCost.toLocaleString() + "</p>" +
        "<p><strong>Cost per m²:</strong> $" + costPerSqm.toLocaleString() + "</p>";
}
</script>'
    ],

    [
        'name' => 'Builder Comparison',
        'slug' => 'builder-comparison', 
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Builder Comparison Tool</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Builder Comparison</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Comparison Content -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Compare Leading Builders</h3>
                    <h2 class="text-anime">Find Your Perfect Building Partner</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Compare our vetted network of award-winning builders across key criteria.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="Premium Builders">
                    </div>
                    <h3>Premium Builders</h3>
                    <ul>
                        <li><strong>Experience:</strong> 15+ years</li>
                        <li><strong>Avg Cost:</strong> $2,200-$2,800/m²</li>
                        <li><strong>Timeline:</strong> 8-12 months</li>
                        <li><strong>Warranty:</strong> 6.5 years</li>
                        <li><strong>Specialties:</strong> Custom designs, luxury finishes</li>
                    </ul>
                    <p>Best for: Custom homes, unique designs, premium finishes</p>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Volume Builders">
                    </div>
                    <h3>Volume Builders</h3>
                    <ul>
                        <li><strong>Experience:</strong> 10+ years</li>
                        <li><strong>Avg Cost:</strong> $1,800-$2,200/m²</li>
                        <li><strong>Timeline:</strong> 6-9 months</li>
                        <li><strong>Warranty:</strong> 6.5 years</li>
                        <li><strong>Specialties:</strong> Established designs, efficiency</li>
                    </ul>
                    <p>Best for: House & land packages, proven designs, value for money</p>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-3.svg" alt="Dual Occupancy">
                    </div>
                    <h3>Dual Occupancy Specialists</h3>
                    <ul>
                        <li><strong>Experience:</strong> 8+ years dual occupancy</li>
                        <li><strong>Avg Cost:</strong> $2,000-$2,500/m²</li>
                        <li><strong>Timeline:</strong> 10-14 months</li>
                        <li><strong>Warranty:</strong> 6.5 years</li>
                        <li><strong>Specialties:</strong> Investment yields, compliance</li>
                    </ul>
                    <p>Best for: Investment properties, rental yields, dual occupancy</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="1s">
                    <h3>Builder Selection Criteria</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Key Questions to Ask</h4>
                            <ul>
                                <li>How many homes have you built in this area?</li>
                                <li>Can you provide recent customer references?</li>
                                <li>What is included in your base price?</li>
                                <li>How do you handle variations and delays?</li>
                                <li>What quality control processes do you have?</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h4>Red Flags to Avoid</h4>
                            <ul>
                                <li>Requesting large upfront payments</li>
                                <li>No fixed-price contracts</li>
                                <li>Poor communication or responsiveness</li>
                                <li>No insurance or licensing</li>
                                <li>Unwillingness to provide references</li>
                            </ul>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 30px;">
                        <a href="/page/content/book-build-consultation" class="btn-default">Book Builder Consultation</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>'
    ],

    [
        'name' => 'Book Build Consultation',
        'slug' => 'book-build-consultation',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Book Build Consultation</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Build Consultation</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Section -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Expert Build Consultation</h3>
                    <h2 class="text-anime">Turn Your Vision Into Reality</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Book a consultation with our building and development specialists.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Schedule Your Consultation</h3>
                    <form id="build-consultation-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="First Name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Last Name" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" required>
                                <option value="">Project Type</option>
                                <option value="custom-home">Custom Home Build</option>
                                <option value="knockdown-rebuild">Knockdown Rebuild</option>
                                <option value="dual-occupancy">Dual Occupancy</option>
                                <option value="land-and-build">Land & Build Package</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <select class="form-control" required>
                                <option value="">Budget Range</option>
                                <option value="400-600">$400k - $600k</option>
                                <option value="600-800">$600k - $800k</option>
                                <option value="800-1200">$800k - $1.2M</option>
                                <option value="1200+">$1.2M+</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <textarea class="form-control" rows="4" placeholder="Tell us about your project vision and requirements..."></textarea>
                        </div>
                        <button type="submit" class="btn-default">Book My Build Consultation</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>What We\'ll Discuss</h3>
                    <ul>
                        <li>✓ Your project vision and requirements</li>
                        <li>✓ Land evaluation and suitability</li>
                        <li>✓ Design and floorplan options</li>
                        <li>✓ Builder recommendations</li>
                        <li>✓ Cost estimation and budgeting</li>
                        <li>✓ Timeline and project planning</li>
                        <li>✓ Council approvals process</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById("build-consultation-form").addEventListener("submit", function(e) {
    e.preventDefault();
    alert("Thank you! We\'ll contact you within 24 hours to schedule your build consultation.");
});
</script>'
    ]
];

echo "Creating Projects subdomain pages...\n";
foreach ($projectsPages as $page) {
    $exists = DB::table('cms_pages')
        ->where('slug', $page['slug'])
        ->where('subdomain_id', $subdomainIds['projects'])
        ->where('company_id', $companyId)
        ->first();
        
    if (!$exists) {
        DB::table('cms_pages')->insert([
            'name' => $page['name'],
            'slug' => $page['slug'],
            'content' => $page['content'],
            'subdomain_id' => $subdomainIds['projects'],
            'company_id' => $companyId,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
            'created_by' => $userId,
            'updated_by' => $userId
        ]);
        echo "  ✓ Created: {$page['name']}\n";
    } else {
        echo "  ✓ Already exists: {$page['name']}\n";
    }
}

// STEP 6: Create Main Domain Pages (for seamlesspropertysolutions.com.au)
echo "\nStep 6: Creating main domain pages...\n";

$mainDomainPages = [
    [
        'name' => 'About Us',
        'slug' => 'about-us',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">About Seamless Property Solutions</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">About Us</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- About Content -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Your Trusted Property Investment Partner</h3>
                    <h2 class="text-anime">Expert Guidance for Every Investment Journey</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Seamless Property Solutions specializes in connecting investors, first home buyers, and developers with the perfect property opportunities across Australia.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Our Mission</h3>
                    <p>To provide specialized property investment guidance that matches each client\'s unique goals, whether they\'re seeking positive cashflow, tax benefits, their first home, or custom development opportunities.</p>
                    
                    <h3>Our Approach</h3>
                    <ul>
                        <li>Specialized expertise for different investment strategies</li>
                        <li>Comprehensive market research and analysis</li>
                        <li>Vetted network of industry professionals</li>
                        <li>Ongoing support throughout the investment journey</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Our Expertise</h3>
                    <ul>
                        <li><strong>Income Properties:</strong> SMSF-compliant investments with positive cashflow</li>
                        <li><strong>Tax Benefits:</strong> Negative gearing and depreciation strategies</li>
                        <li><strong>First Home Buyers:</strong> Government grants and low-deposit solutions</li>
                        <li><strong>Development Projects:</strong> Land acquisition and custom builds</li>
                    </ul>
                    
                    <div style="margin-top: 30px;">
                        <h4>Industry Recognition</h4>
                        <p>Licensed and certified professionals with extensive experience in Australian property markets.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>'
    ],

    [
        'name' => 'Contact Us',
        'slug' => 'contact-us',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Contact Us</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Contact</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contact Content -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Get In Touch</h3>
                    <form id="contact-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="First Name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Last Name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="email" class="form-control" placeholder="Email Address" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="tel" class="form-control" placeholder="Phone Number">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" required>
                                <option value="">What are you interested in?</option>
                                <option value="income">Income/SMSF Properties</option>
                                <option value="tax">Tax Benefit Properties</option>
                                <option value="fhb">First Home Purchase</option>
                                <option value="development">Land/Development</option>
                                <option value="general">General Inquiry</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <textarea class="form-control" rows="5" placeholder="Your Message" required></textarea>
                        </div>
                        <button type="submit" class="btn-default">Send Message</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Specialized Consultations</h3>
                    <p>Choose the right specialist for your needs:</p>
                    
                    <div style="margin-bottom: 20px;">
                        <h4>Income Properties</h4>
                        <p>SMSF and positive cashflow specialists</p>
                        <a href="/income" class="btn-default btn-sm">Visit Income Portal</a>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <h4>Tax Benefits</h4>
                        <p>Negative gearing and depreciation experts</p>
                        <a href="/taxbenefits" class="btn-default btn-sm">Visit Tax Portal</a>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <h4>First Home Buyers</h4>
                        <p>Grant and finance specialists</p>
                        <a href="/fhb" class="btn-default btn-sm">Visit FHB Portal</a>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <h4>Development Projects</h4>
                        <p>Land and building experts</p>
                        <a href="/projects" class="btn-default btn-sm">Visit Projects Portal</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById("contact-form").addEventListener("submit", function(e) {
    e.preventDefault();
    alert("Thank you for your message! We\'ll get back to you within 24 hours.");
});
</script>'
    ]
];

foreach ($mainDomainPages as $page) {
    $exists = DB::table('cms_pages')
        ->where('slug', $page['slug'])
        ->whereNull('subdomain_id')
        ->where('company_id', $companyId)
        ->first();
        
    if (!$exists) {
        DB::table('cms_pages')->insert([
            'name' => $page['name'],
            'slug' => $page['slug'],
            'content' => $page['content'],
            'subdomain_id' => null,
            'company_id' => $companyId,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
            'created_by' => $userId,
            'updated_by' => $userId
        ]);
        echo "  ✓ Created main domain page: {$page['name']}\n";
    } else {
        echo "  ✓ Main domain page already exists: {$page['name']}\n";
    }
}

echo "\n🎉 Complete Subdomain System Setup Completed Successfully!\n";
echo "===============================================\n";

// Final verification
$totalSubdomains = DB::table('sub_domains')->where('company_id', $companyId)->count();
$totalCmsPages = DB::table('cms_pages')->where('company_id', $companyId)->count();
$totalPropertyTypes = DB::table('property_types')->where('company_id', $companyId)->where('id', '>=', 88)->count();

echo "Final Status:\n";
echo "- Subdomains created: {$totalSubdomains}\n";
echo "- CMS pages created: {$totalCmsPages}\n";
echo "- Specialized property types: {$totalPropertyTypes}\n";
echo "- Feature: SubDomain Management ✓\n";
echo "- Permissions: Created ✓\n";
echo "- Theme integration: Ready ✓\n\n";

echo "✅ Your subdomain system is now ready!\n";
echo "✅ Access your specialized portals:\n";
echo "   - income.seamlesspropertysolutions.com.au\n";
echo "   - taxbenefits.seamlesspropertysolutions.com.au\n";
echo "   - fhb.seamlesspropertysolutions.com.au\n";
echo "   - projects.seamlesspropertysolutions.com.au\n\n";

echo "🔒 SECURITY: Remember to delete this script file after successful execution!\n";
echo "Command: rm setup_complete_subdomain_system.php\n\n";

echo "📝 Next Steps:\n";
echo "1. Test each subdomain URL to ensure proper routing\n";
echo "2. Configure DNS/hosting for subdomain access\n";
echo "3. Test CMS page functionality on each subdomain\n";
echo "4. Configure property filtering by subdomain\n";
echo "5. Train your team on the new subdomain features\n";

?>