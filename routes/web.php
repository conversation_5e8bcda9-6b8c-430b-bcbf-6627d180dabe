<?php

use App\Features\CompanyManagementFeature;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;
use Laravel\Pennant\Middleware\EnsureFeaturesAreActive;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::post('tmp-media/upload', [Controller::class, 'handleTempMedia'])->middleware('auth');
Route::get('piab-property-sync/{company:id}/{status}', [DashboardController::class, 'importPiabProducts'])->middleware('auth');
Route::get('piab-import-all/{status?}', [DashboardController::class, 'importAllPiabProperties'])->middleware('auth');
Route::get('lp-import-properties/{company:id}', [DashboardController::class, 'importLatitudePropertyData'])->middleware('auth');

Route::get('sample-event/dispatch/{counter}', [DashboardController::class,'dipatchSampleEvent'])
    ->middleware(['auth', 'verified'])
    ->name('dispatch_sample_event');


// Route::get('feature/real-estate', [DashboardController::class,'viewRealEstate'])
//     ->middleware(['auth', 'verified', EnsureFeaturesAreActive::using('real-estate')])
//     ->name('real_estate_feature');

// Route::get('feature/company-management', [DashboardController::class,'viewCompanyManagement'])
//     ->middleware(['auth', 'verified', EnsureFeaturesAreActive::using(CompanyManagementFeature::class)])
//     ->name('company_management_feature');

Route::get('blogs', [BlogController::class,'blogs'])
    ->middleware(['auth', 'verified'])
    ->name('blog_list');
// Route::get('blogs/{tag}', [BlogController::class,'blogByTag'])
//     ->middleware(['auth', 'verified'])
//     ->name('blog_list_by_tag');

// Route::view('profile', 'profile')
//     ->middleware(['auth'])
//     ->name('profile');

Route::view('role', 'role')
    ->middleware(['auth', 'permission:Role List'])
    ->name('role_list');

Route::view('company', 'company')
    ->middleware(['auth', 'permission:Company List'])
    ->name('company_list');

require __DIR__.'/auth.php';
