<?php

use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\SocialiteController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CmsPageController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PropertyController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
Route::middleware('guest')->group(function () {

    Route::get('reset-password/{token}', [UserController::class, 'resetPassword'])
            ->name('password.reset');

    Route::get('sign-in', [AuthController::class, 'login'])
            ->name('signin');

    Route::get('register', [AuthController::class, 'register'])
            ->name('register');

    Route::get('auth/{provider}/redirection', [SocialiteController::class, 'authProviderRedirection'])->name('login.redirection');
    Route::get('auth/{provider}/callback', [SocialiteController::class, 'handleAuthCallback']);
});

Route::middleware('advance_search_data')->group(function(){
    Route::get('/', [HomeController::class, 'index'])->middleware('active_menu:home')->name('index');
    Route::get('/lists/properties', [PropertyController::class, 'getListing'])->middleware('active_menu:properties')->name('property.listing');
    Route::get('/properties/search-results', [PropertyController::class, 'advanceSearchListing'])->middleware('active_menu:properties')->name('property.search_results');
    Route::get('/properties/map-search', [PropertyController::class, 'mapSearchListing'])->middleware('active_menu:properties')->name('property.map_search');
    Route::get('/properties/map-view', [PropertyController::class, 'mapView'])->middleware('active_menu:properties')->name('property.map_view');
    Route::get('/properties/type/{propertyType:slug}', [PropertyController::class, 'getListingByType'])->middleware('active_menu:properties')->name('property.by_type');
    Route::get('/properties/status/{status:slug}', [PropertyController::class, 'getListingByStatus'])->middleware('active_menu:properties')->name('property.by_status');
    Route::get('/properties/city/{city}', [PropertyController::class, 'getListingByCity'])->middleware('active_menu:properties')->name('property.by_city');
    Route::get('/properties/state/{state}', [PropertyController::class, 'getListingByState'])->middleware('active_menu:properties')->name('property.by_state');
    Route::get('/properties/featured', [PropertyController::class, 'getListingByFeatured'])->middleware('active_menu:properties')->name('property.featured');
    Route::get('/properties/ajax-map-data', [PropertyController::class, 'ajaxMapViewProperties'])->middleware('active_menu:properties')->name('property.ajax_map_data');

});

Route::get('/property/{property:slug}', [PropertyController::class, 'index'])->name('property.details');
Route::get('/property/{property:slug}/print', [PropertyController::class, 'propertyPrint'])->name('property.details.print');
Route::get('/property/{property:slug}/flyer', [PropertyController::class, 'index'])->name('property.flyer');

Route::get('/page/content/{cmsPage:slug}', [CmsPageController::class, 'index'])->name('cms_page')->middleware('feature_validate:CMS Page');
Route::get('/page/{cmsPage}', [CmsPageController::class, 'showStaticPage'])->name('static_page');
Route::get('/contact-us', [CmsPageController::class, 'showContactPage'])->name('contact_us');
Route::get('/properties/compare', [CmsPageController::class, 'comparePropertyPage'])->name('compare_properties');
Route::get('/book-a-strategy-call', [CmsPageController::class, 'showBookStrategyCall'])->name('book_strategy_call');
Route::get('/piaq', [CmsPageController::class, 'showPiaQuestionnaireForm'])->name('pia_questionnaire');
Route::get('/eoi', [CmsPageController::class, 'showEoiForm'])->name('eoi');


Route::middleware(['feature_validate:Blog Management'])->prefix('/blogs')->group(function () {

    Route::get('/', [BlogController::class, 'showAllBlogs'])->name('blogs');
    Route::get('/tag/{tag:slug}', [BlogController::class, 'blogsListByTag'])->name('blogs.tag');
    Route::get('/category/{category:slug}', [BlogController::class, 'blogsListByCategory'])->name('blogs.category');
    Route::get('/search-results', [BlogController::class, 'blogSearchListing'])->name('blogs.search_results');
    Route::get('/{blog:slug}', [BlogController::class, 'viewDetail'])->name('blog_detail');
});

Route::get('/error/invalid', [CmsPageController::class, 'invalidRequestPage'])->name('invalid_request');
Route::get('/verify/subscriber/{subscriber:email}', [CmsPageController::class, 'viewSubscriberPage'])->name('subscriber.verify_view');
Route::post('/verify/subscriber/{subscriber:email}', [CmsPageController::class, 'submitSubscriberForm'])->name('subscriber.verify_view');
Route::get('/unsubscribe/{subscriber:email}', [CmsPageController::class, 'unsubscribe'])->name('unssubscriber');
Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
        ->middleware(['signed', 'throttle:6,1'])
        ->name('verification.verify');

Route::middleware('auth')->group( function(){
    Route::post('logout', [ UserController::class, 'logout'])->name('logout');
    Route::get('dashboard', [ UserController::class, 'viewDashboard'])->name('dashboard');
    Route::get('profile', [ UserController::class, 'viewProfile'])->name('profile');
    Route::get('profile/favourite-properties', [ UserController::class, 'viewFavouriteProperties'])->name('profile.favourite_property');
    Route::get('profile/saved-searches', [ UserController::class, 'viewSavedSearchList'])->name('profile.saved_search_list');
});

Route::get('/pages/tnc', function () {
    return view('static-page.tnc');
});

Route::get('/pages/privacy', function () {
    return view('static-page.privacy');
});
