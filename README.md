<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## About Laravel Boilerplate

- [x] Laravel Breeze ✅
- [ ] Laravel Reverb
- [x] Laravel livewire ✅
- [x] Laravel Livewire Volt ✅
- [x] Laravel Sanctum ✅
- [x] Pest PHP ✅
- [x] Pest PHP Plugins ✅
  - Arch setup ✅
  - Pest Faker plugin ✅
  - Pest Livewire plugin ✅
  - Pest Watch plugin ✅
  - Pest Stresstesting (Stressless) plugin ✅
  - Pest Type Coverage plugin ✅
- [x] FilamentPHP  ✅
- [ ] Laravel Echo with Broadcasting
- [ ] Laravel Cashier
- [ ] Laravel Debugbar
- [ ] Laravel Horizon
- [ ] Laravel Pennant
- [ ] Laravel Pulse
- [ ] Laravel Scout
- [ ] Laravel Telescope
- [x] Laravel Pint ✅
- [x] Laravel Sail ✅
- [ ] Laravel Socialite
- [ ] Laravel Cashier
- [ ] Laravel Vapor 
- [x] Laravel Octane and frankenphp ✅
- [ ] Spatie Permissions
- [x] Spatie Media Library ✅
  - Please check the optimization [tools setup](https://spatie.be/docs/laravel-medialibrary/v11/installation-setup#content-setting-up-optimization-tools)
- [ ] Spatie Comments with Livewire Component
- [ ] Spatie Settings
- [ ] Spatie Tags
- [ ] Laravel SEO
- [x] Laravel OpenAI API ✅
- [ ] [Laravel Restify](https://restify.binarcode.com/)
- [x] Laravel Saloon ✅
- [x] [laravelmodules](https://laravelmodules.com/) ✅
- [x] [laravelmoduleslivewire](https://github.com/mhmiton/laravel-modules-livewire) ✅
- [x] Basic Blog Module ✅
- [x] Laravel themes manager ✅
- [x] Replicate.com Client ✅
- [x] MaryUI as default theme ✅


## Instructions
1. Laravel Modules example: go to /posts page
## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).


## for puppeteer install issue in windows
- npm install chromium
- Run command to install `puppeteer` 
    ```
    npm install --ignore-scripts puppeteer
    ```
- Set node binary path:
    ```
    setNodeBinary("PATH//node.exe")
    ```
- Set npm binary path:
    ```
    setNpmBinary("NODE_PATH//npm")
    ```
- Set Chrome binary path:
    ```
    setChromePath('C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe')
    ```
## for Macbook browsershot pdf setup
// HelperService.php
// ->setIncludePath("/Users/<USER>/.nvm/versions/node/v20.11.0/bin/node")
// ->setNodeBinary('/Users/<USER>/.nvm/versions/node/v20.11.0/bin/node')
// ->setNpmBinary('/Users/<USER>/.nvm/versions/node/v20.11.0/bin/npm')
// ->setChromePath('/Users/<USER>/Library/Application Support/Google/Chrome/Default')