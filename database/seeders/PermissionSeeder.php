<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = File::get('database/seeders/data/permissions.json');
        $permissions = collect(json_decode($permissions));
        $permissions->each(function ($permission) {
            Permission::create([
                'name' => $permission->name,
                'description' => $permission->description ?? null,
                'module' => $permission->module ?? null,
                'guard_name' => $permission->guard_name ?? 'web',
            ]);
        });
    }
}
