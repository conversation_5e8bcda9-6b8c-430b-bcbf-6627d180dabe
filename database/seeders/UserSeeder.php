<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = File::get('database/seeders/data/users.json');
        $users = collect(json_decode($users));
        $users->each(function ($user) {
            User::create([
                'name' => $user->name,
                'email' => $user->email,
                'password' => $user->password,
                'email_verified_at' => now(),
            ])->assignRole($user->role);
        });
    }
}
