<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Spatie\Permission\Models\Role;

class RolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = File::get('database/seeders/data/roles.json');
        $roles = collect(json_decode($roles));
        $roles->each(function ($role) {
            Role::create([
                'name' => $role->name,
                'description' => $role->description ?? null,
                'guard_name' => $role->guard_name ?? 'web',
            ])->syncPermissions($role->permissions);
        });
    }
}
