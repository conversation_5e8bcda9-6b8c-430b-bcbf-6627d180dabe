{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "repositories": [{"type": "vcs", "url": "https://github.com/laravel-shift/laravel-themes-manager.git"}], "license": "MIT", "require": {"php": "^8.2", "benbjurstrom/replicate-php": "^0.3.0", "elic-dev/laravel-site-protection": "^1.2", "filament/filament": "^3.2.92", "guzzlehttp/guzzle": "^7.8.1", "hexadog/laravel-themes-manager": "dev-l11-compatibility", "laravel/breeze": "^2.1.2", "laravel/framework": "^11.15", "laravel/octane": "^2.5.2", "laravel/sanctum": "^4.0.2", "laravel/socialite": "^5.16", "laravel/tinker": "^2.9.0", "league/flysystem-aws-s3-v3": "^3.28", "livewire/livewire": "^3.5.2", "livewire/volt": "^1.6.5", "mhmiton/laravel-modules-livewire": "^3.0", "nwidart/laravel-modules": "^11.0.11", "openai-php/laravel": "^0.8.1", "robsontenorio/mary": "^1.34.2", "saloonphp/laravel-plugin": "^3.5.0", "saloonphp/saloon": "^3.9.1", "spatie/laravel-medialibrary": "^11.7.3", "spatie/laravel-pdf": "^1.5.1", "spatie/laravel-permission": "^6.9", "spatie/laravel-sitemap": "^7.2", "spatie/laravel-sluggable": "^3.6", "stripe/stripe-php": "^14.10"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13.5", "fakerphp/faker": "^1.23.1", "laravel/pint": "^1.16.2", "laravel/sail": "^1.30.2", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.1.1", "pestphp/pest": "^2.34.8", "pestphp/pest-plugin-faker": "^2.0", "pestphp/pest-plugin-laravel": "^2.4", "pestphp/pest-plugin-livewire": "^2.1", "pestphp/pest-plugin-stressless": "^2.2", "pestphp/pest-plugin-type-coverage": "^2.8.4", "pestphp/pest-plugin-watch": "^2.1", "spatie/laravel-ignition": "^2.8.0"}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "hexadog/laravel-theme-installer": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}