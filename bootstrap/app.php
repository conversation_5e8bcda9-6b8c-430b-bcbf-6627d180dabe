<?php

use App\Providers\AppServiceProvider;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders()
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        // api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        // channels: __DIR__.'/../routes/channels.php',
        health: '/up',
        then: function () {
            if (file_exists(base_path('routes/debug.php'))) {
                Route::middleware('web')->group(base_path('routes/debug.php'));
            }
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectGuestsTo(fn () => route('index'));
        $middleware->redirectUsersTo(AppServiceProvider::HOME);

        $middleware->append(
            [
                \App\Http\Middleware\SetCompanyUsingWhiteLabelMiddleware::class,
                \App\Http\Middleware\SubDomainContextMiddleware::class,
                \App\Http\Middleware\SetCompanyEnvConfiguration::class,
                \App\Http\Middleware\ThemeLoader::class,
            ]);

        // $middleware->validateCsrfTokens(
        //     except:[
        //         'livewire/*',
        //     ]);
        $middleware->appendToGroup('web',\ElicDev\SiteProtection\Http\Middleware\SiteProtection::class);

        $middleware->throttleApi();

        $middleware->alias([
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'theme' => \Hexadog\ThemesManager\Http\Middleware\ThemeLoader::class,
            'advance_search_data' => \App\Http\Middleware\ShareAdvanceSearchFormData::class,
            'active_menu' => \App\Http\Middleware\ActiveMenuLink::class,
            'feature_validate' => \App\Http\Middleware\FeatureValidationMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
