<div>
    <script type="module">
        import * as LR from "https://cdn.jsdelivr.net/npm/@uploadcare/blocks@0.35.2/web/lr-file-uploader-regular.min.js";

        LR.registerBlocks(LR);

        const form = document.querySelector("form");
        form.addEventListener("submit", (e) => {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const outputEl = document.querySelector("#form-output");
            alert('here');
            outputEl.innerText = JSON.stringify([...formData.entries()], null, 2);
        });
    </script>
    <style>
        .source, .destination {
            --darkmode: 0;
            --h-accent: 223;
            --s-accent: 100%;
            --l-accent: 61%;
        }
    </style>

    <form>
    <lr-config
        ctx-name="source"
        pubkey="ab04c86f51d5a567f23a"
        max-local-file-size-bytes="10000000"
        img-only="true"
        source-list="local, url, camera, dropbox"
    ></lr-config>

    <lr-config
        ctx-name="dstination"
        pubkey="ab04c86f51d5a567f23a"
        max-local-file-size-bytes="10000000"
        img-only="true"
        source-list="local, url, camera, dropbox"
    ></lr-config>

    <label>Source</label>
    <lr-file-uploader-regular
        css-src="https://cdn.jsdelivr.net/npm/@uploadcare/blocks@0.35.2/web/lr-file-uploader-regular.min.css"
        ctx-name="source"
        class="source"
        la
    >
    </lr-file-uploader-regular>
    <input type="text" id="sourceurl"/>

    <br/>

    <label>Destination</label>
    <lr-file-uploader-regular
        css-src="https://cdn.jsdelivr.net/npm/@uploadcare/blocks@0.35.2/web/lr-file-uploader-regular.min.css"
        ctx-name="dstination"
        class="dstination"
    >
    </lr-file-uploader-regular>
    <input type="text" id="destinationurl"/>
    <br/>

    <button type="submit">Submit</button>
    <br />
    <br />
    <code>
        <pre id="form-output"></pre>
    </code>

    <label>Results</label>
    <img src="https://www.shutterstock.com/image-vector/default-ui-image-placeholder-wireframes-260nw-1037719192.jpg" />


</form>
</div>
