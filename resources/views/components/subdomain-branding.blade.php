@php
    $subdomainData = $subdomainData ?? [];
    $styling = $subdomainData['styling'] ?? [];
    $customSettings = $styling['custom_settings'] ?? [];
    $primaryColor = $customSettings['primary_color'] ?? '#007bff';
    $secondaryColor = $customSettings['secondary_color'] ?? '#6c757d';
    
    // Simple RGB conversion for primary color
    $primaryRgb = '0, 123, 255'; // default blue
    if ($primaryColor && str_starts_with($primaryColor, '#')) {
        $hex = ltrim($primaryColor, '#');
        if (strlen($hex) === 6) {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
            $primaryRgb = "$r, $g, $b";
        }
    }
    
    // Simple font family extraction
    $fontFamily = 'Roboto';
    if (!empty($styling['google_fonts'])) {
        $fontParts = explode(':', $styling['google_fonts']);
        $fontFamily = $fontParts[0] ?? 'Roboto';
    }
@endphp

{{-- External CSS --}}
@if(!empty($styling['external_css_url']))
<link rel="stylesheet" href="{{ $styling['external_css_url'] }}" type="text/css" media="all">
@endif

{{-- Google Fonts --}}
@if(!empty($styling['google_fonts']))
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family={{ $styling['google_fonts'] }}&display=swap" rel="stylesheet">
@endif

{{-- Dynamic CSS Variables --}}
<style>
:root {
    /* Primary Colors */
    --bs-primary: {{ $primaryColor }};
    --bs-secondary: {{ $secondaryColor }};
    --bs-success: {{ $customSettings['success_color'] ?? '#28a745' }};
    --bs-info: {{ $customSettings['info_color'] ?? '#17a2b8' }};
    --bs-warning: {{ $customSettings['warning_color'] ?? '#ffc107' }};
    --bs-danger: {{ $customSettings['danger_color'] ?? '#dc3545' }};
    --bs-light: {{ $customSettings['light_color'] ?? '#f8f9fa' }};
    --bs-dark: {{ $customSettings['dark_color'] ?? '#343a40' }};
    
    /* Primary color variations */
    --primary-rgb: {{ $primaryRgb }};
    
    /* Typography */
    --font-family-primary: '{{ $fontFamily }}', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    
    /* Theme-specific overrides */
    --navbar-brand-color: var(--bs-primary);
    --btn-primary-bg: var(--bs-primary);
    --btn-primary-border: var(--bs-primary);
    --link-color: var(--bs-primary);
}

/* Apply branding to key elements */
body {
    font-family: var(--font-family-primary);
}

.navbar-brand {
    color: var(--navbar-brand-color) !important;
    font-weight: 600;
}

.btn-primary {
    background-color: var(--btn-primary-bg) !important;
    border-color: var(--btn-primary-border) !important;
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--bs-secondary) !important;
    border-color: var(--bs-secondary) !important;
}

a {
    color: var(--link-color);
}

a:hover {
    color: var(--bs-secondary);
}

/* Bootstrap override for theme consistency */
.bg-primary {
    background-color: var(--bs-primary) !important;
}

.text-primary {
    color: var(--bs-primary) !important;
}

.border-primary {
    border-color: var(--bs-primary) !important;
}

/* Custom hero section for subdomains */
.hero-section {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    color: white;
}

/* Property cards theming */
.property-card:hover {
    border-color: var(--bs-primary);
    box-shadow: 0 0.5rem 1rem rgba(var(--primary-rgb), 0.15);
}

/* Navigation theming */
.navbar-nav .nav-link:hover {
    color: var(--bs-primary) !important;
}

/* Form controls theming */
.form-control:focus {
    border-color: rgba(var(--primary-rgb), 0.5);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

/* Subdomain-specific branding marker */
.subdomain-branded {
    /* This class indicates that subdomain branding is active */
}
</style>

{{-- Custom CSS --}}
@if(!empty($styling['custom_css']))
<style>
{!! $styling['custom_css'] !!}
</style>
@endif