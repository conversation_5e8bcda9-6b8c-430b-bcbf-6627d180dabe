@if(config('current_subdomain'))
    @php
        $subdomain = config('current_subdomain');
        $heroConfig = [
            'income' => [
                'title' => 'Positive Cashflow Property Investment',
                'subtitle' => 'Build wealth through SMSF-compliant properties that generate immediate passive income',
                'description' => 'Discover dual occupancy, co-living, and regional high-yield properties specifically selected for SMSF investors and passive income seekers. Our properties deliver $12k+ annual net income with rental yields exceeding 6.8%.',
                'features' => [
                    ['icon' => '💰', 'title' => '$230+ Weekly Income', 'desc' => 'Average weekly rental return'],
                    ['icon' => '🏦', 'title' => '100% SMSF Compliant', 'desc' => 'All properties meet strict SMSF requirements'],
                    ['icon' => '📈', 'title' => '6.8% Average Yield', 'desc' => 'Significantly above market average'],
                    ['icon' => '🌍', 'title' => 'Regional Focus', 'desc' => '85% located in high-growth regional areas']
                ],
                'cta_primary' => 'Download SMSF Investment Guide',
                'cta_primary_link' => route('cms_page', 'smsf-investment-guides'),
                'cta_secondary' => 'Book Strategy Call',
                'cta_secondary_link' => route('cms_page', 'book-strategy-call'),
                'bg_gradient' => 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100',
                'text_color' => 'text-green-800',
                'accent_color' => 'bg-green-600',
                'icon_bg' => 'bg-green-100'
            ],
            'taxbenefits' => [
                'title' => 'Tax-Minimizing Property Strategies',
                'subtitle' => 'Maximize your tax benefits through strategic negative gearing and depreciation',
                'description' => 'High-income earners can reduce their tax burden by up to 47% through our carefully selected metro apartments and prestige properties with aggressive depreciation schedules and strong capital growth potential.',
                'features' => [
                    ['icon' => '📊', 'title' => '37-47% Tax Savings', 'desc' => 'For high-income earners in top tax brackets'],
                    ['icon' => '💸', 'title' => '$25k+ Depreciation', 'desc' => 'Annual depreciation claims available'],
                    ['icon' => '🏙️', 'title' => '78% Metro Properties', 'desc' => 'Prime locations with strong growth'],
                    ['icon' => '✨', 'title' => '92% High-Spec Builds', 'desc' => 'Premium finishes for maximum deductions']
                ],
                'cta_primary' => 'Calculate Tax Benefits',
                'cta_primary_link' => route('cms_page', 'tax-benefit-calculator'),
                'cta_secondary' => 'Book Tax Strategy Session',
                'cta_secondary_link' => route('cms_page', 'book-tax-strategy'),
                'bg_gradient' => 'bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-100',
                'text_color' => 'text-blue-800',
                'accent_color' => 'bg-blue-600',
                'icon_bg' => 'bg-blue-100'
            ],
            'fhb' => [
                'title' => 'First Home Buyer Pathways',
                'subtitle' => 'Your complete guide to homeownership with government support',
                'description' => 'Access up to $55,000 in combined government grants and concessions. Our affordable properties under $750k come with 5% deposit options, stamp duty exemptions, and comprehensive first-home buyer support.',
                'features' => [
                    ['icon' => '💳', 'title' => '5% Deposit Options', 'desc' => 'Low deposit schemes available'],
                    ['icon' => '🎁', 'title' => 'Up to $55k Grants', 'desc' => 'Combined government assistance'],
                    ['icon' => '🏠', 'title' => 'Under $750k Range', 'desc' => 'Affordable starter home options'],
                    ['icon' => '💰', 'title' => 'Stamp Duty Savings', 'desc' => 'Up to $15k+ in concessions']
                ],
                'cta_primary' => 'Check Grant Eligibility',
                'cta_primary_link' => route('cms_page', 'government-grants'),
                'cta_secondary' => 'Book First Home Planning',
                'cta_secondary_link' => route('cms_page', 'book-fhb-planning'),
                'bg_gradient' => 'bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100',
                'text_color' => 'text-purple-800',
                'accent_color' => 'bg-purple-600',
                'icon_bg' => 'bg-purple-100'
            ],
            'projects' => [
                'title' => 'Custom Build & Land Solutions',
                'subtitle' => 'Design and build your dream home with expert guidance',
                'description' => 'Access premium land lots (300-600m²) with our network of 15+ award-winning builders. From knockdown-rebuilds to custom designs, we provide comprehensive project management from concept to completion.',
                'features' => [
                    ['icon' => '📐', 'title' => '300-600m² Land', 'desc' => 'Premium sized lots available'],
                    ['icon' => '🏗️', 'title' => '15+ Builder Partners', 'desc' => 'Award-winning construction network'],
                    ['icon' => '✅', 'title' => '100% Council Approved', 'desc' => 'All blocks ready for immediate build'],
                    ['icon' => '🎨', 'title' => 'Unlimited Design', 'desc' => 'Complete customization flexibility']
                ],
                'cta_primary' => 'Estimate Build Costs',
                'cta_primary_link' => route('cms_page', 'build-cost-estimator'),
                'cta_secondary' => 'Book Build Consultation',
                'cta_secondary_link' => route('cms_page', 'book-build-consultation'),
                'bg_gradient' => 'bg-gradient-to-br from-orange-50 via-amber-50 to-orange-100',
                'text_color' => 'text-orange-800',
                'accent_color' => 'bg-orange-600',
                'icon_bg' => 'bg-orange-100'
            ]
        ];
        
        $config = $heroConfig[$subdomain->subdomain] ?? null;
    @endphp

    @if($config)
    <section class="enhanced-subdomain-hero {{ $config['bg_gradient'] }} py-20 px-4 relative overflow-hidden">
        <!-- Background Decoration -->
        <div class="absolute inset-0 opacity-30">
            <div class="absolute top-10 left-10 w-32 h-32 {{ $config['icon_bg'] }} rounded-full blur-3xl"></div>
            <div class="absolute bottom-10 right-10 w-40 h-40 {{ $config['icon_bg'] }} rounded-full blur-3xl"></div>
            <div class="absolute top-1/2 left-1/3 w-24 h-24 {{ $config['icon_bg'] }} rounded-full blur-2xl"></div>
        </div>
        
        <div class="container mx-auto max-w-7xl relative z-10">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Left Column: Content -->
                <div class="space-y-8">
                    <!-- Badge -->
                    <div class="inline-block">
                        <span class="{{ $config['accent_color'] }} text-white px-4 py-2 rounded-full text-sm font-bold tracking-wide uppercase">
                            {{ strtoupper($subdomain->subdomain) }} SPECIALIST
                        </span>
                    </div>
                    
                    <!-- Main Heading -->
                    <div class="space-y-4">
                        <h1 class="text-4xl lg:text-6xl font-bold {{ $config['text_color'] }} leading-tight">
                            {{ $config['title'] }}
                        </h1>
                        <h2 class="text-xl lg:text-2xl {{ $config['text_color'] }} opacity-80 font-medium">
                            {{ $config['subtitle'] }}
                        </h2>
                    </div>
                    
                    <!-- Description -->
                    <p class="text-lg {{ $config['text_color'] }} opacity-70 leading-relaxed max-w-xl">
                        {{ $config['description'] }}
                    </p>
                    
                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="{{ $config['cta_primary_link'] }}" 
                           class="{{ $config['accent_color'] }} text-white px-8 py-4 rounded-lg font-semibold hover:opacity-90 transition-all transform hover:scale-105 shadow-lg">
                            {{ $config['cta_primary'] }}
                        </a>
                        <a href="{{ $config['cta_secondary_link'] }}" 
                           class="border-2 border-current {{ $config['text_color'] }} px-8 py-4 rounded-lg font-semibold hover:bg-current hover:text-white transition-all shadow-md">
                            {{ $config['cta_secondary'] }}
                        </a>
                    </div>
                </div>
                
                <!-- Right Column: Features Grid -->
                <div class="grid grid-cols-2 gap-6">
                    @foreach($config['features'] as $feature)
                        <div class="bg-white/70 backdrop-blur-sm p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                            <div class="flex flex-col items-center text-center space-y-3">
                                <div class="text-4xl mb-2">{{ $feature['icon'] }}</div>
                                <div class="font-bold {{ $config['text_color'] }} text-lg">{{ $feature['title'] }}</div>
                                <div class="text-sm {{ $config['text_color'] }} opacity-70">{{ $feature['desc'] }}</div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            
            <!-- Trust Indicators -->
            <div class="mt-16 pt-8 border-t border-white/30">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                    @if($subdomain->subdomain === 'income')
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">500+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Properties Delivered</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">$2.5M+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Annual Income Generated</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">95%</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">SMSF Compliance Rate</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">4.8★</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Client Satisfaction</div>
                        </div>
                    @elseif($subdomain->subdomain === 'taxbenefits')
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">$15M+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Tax Savings Delivered</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">350+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">High-Income Clients</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">42%</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Average Tax Reduction</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">100%</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">ATO Compliant</div>
                        </div>
                    @elseif($subdomain->subdomain === 'fhb')
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">1200+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">First Homes Delivered</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">$18M+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Grants Secured</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">98%</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Loan Approval Rate</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">30 Days</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Average Settlement</div>
                        </div>
                    @elseif($subdomain->subdomain === 'projects')
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">800+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Custom Builds</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">15+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Builder Partners</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">6 Months</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Average Build Time</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold {{ $config['text_color'] }}">100%</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Fixed Price Contracts</div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
    @endif
@endif