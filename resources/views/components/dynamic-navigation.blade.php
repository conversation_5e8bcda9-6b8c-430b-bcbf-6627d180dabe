@props(['subdomainData' => null, 'navigation' => [], 'currentPage' => 'home'])

@php
    $brandName = $subdomainData['name'] ?? 'Seamless Property Solutions';
    $brandLogo = $subdomainData['branding']['logo'] ?? null;
    $navItems = $navigation['main'] ?? [
        ['label' => 'Home', 'url' => '/', 'active' => $currentPage === 'home'],
        ['label' => 'Properties', 'url' => '/properties', 'active' => $currentPage === 'properties'],
        ['label' => 'Calculator', 'url' => '/calculator', 'active' => $currentPage === 'calculator'],
        ['label' => 'About', 'url' => '/about', 'active' => $currentPage === 'about'],
        ['label' => 'Contact', 'url' => '/contact', 'active' => $currentPage === 'contact'],
    ];
    $navStyle = $subdomainData['styling']['navigation']['style'] ?? 'navbar-expand-lg';
    $navPosition = $subdomainData['styling']['navigation']['position'] ?? 'static';
@endphp

<nav class="navbar {{ $navStyle }} navbar-custom {{ $navPosition === 'fixed' ? 'fixed-top' : '' }}" 
     role="navigation" 
     aria-label="Main navigation">
    
    <div class="container{{ $subdomainData['styling']['navigation']['full_width'] ?? false ? '-fluid' : '' }}">
        
        {{-- Brand/Logo --}}
        <a class="navbar-brand" href="/" aria-label="Home">
            @if($brandLogo)
                <img src="{{ $brandLogo }}" 
                     alt="{{ $brandName }}" 
                     height="{{ $subdomainData['branding']['logo_height'] ?? '40' }}"
                     class="d-inline-block align-text-top">
            @else
                {{ $brandName }}
            @endif
        </a>

        {{-- Mobile Toggle Button --}}
        <button class="navbar-toggler" 
                type="button" 
                data-bs-toggle="collapse" 
                data-bs-target="#navbarNav" 
                aria-controls="navbarNav" 
                aria-expanded="false" 
                aria-label="Toggle navigation">
            <span class="navbar-toggler-icon">
                <i class="fas fa-bars"></i>
            </span>
        </button>

        {{-- Navigation Menu --}}
        <div class="collapse navbar-collapse" id="navbarNav">
            
            {{-- Main Navigation Links --}}
            <ul class="navbar-nav {{ $subdomainData['styling']['navigation']['alignment'] ?? 'ms-auto' }}">
                @foreach($navItems as $item)
                <li class="nav-item {{ isset($item['dropdown']) ? 'dropdown' : '' }}">
                    @if(isset($item['dropdown']))
                        {{-- Dropdown Menu --}}
                        <a class="nav-link dropdown-toggle" 
                           href="#" 
                           role="button" 
                           data-bs-toggle="dropdown" 
                           aria-expanded="false">
                            @if(isset($item['icon']))
                                <i class="fas {{ $item['icon'] }} me-1"></i>
                            @endif
                            {{ $item['label'] }}
                        </a>
                        <ul class="dropdown-menu">
                            @foreach($item['dropdown'] as $dropdownItem)
                            <li>
                                <a class="dropdown-item" 
                                   href="{{ $dropdownItem['url'] }}"
                                   @if(isset($dropdownItem['target'])) target="{{ $dropdownItem['target'] }}" @endif>
                                    @if(isset($dropdownItem['icon']))
                                        <i class="fas {{ $dropdownItem['icon'] }} me-2"></i>
                                    @endif
                                    {{ $dropdownItem['label'] }}
                                </a>
                            </li>
                            @endforeach
                        </ul>
                    @else
                        {{-- Regular Link --}}
                        <a class="nav-link {{ ($item['active'] ?? false) ? 'active' : '' }}" 
                           href="{{ $item['url'] }}"
                           @if(isset($item['target'])) target="{{ $item['target'] }}" @endif
                           @if($item['active'] ?? false) aria-current="page" @endif>
                            @if(isset($item['icon']))
                                <i class="fas {{ $item['icon'] }} me-1"></i>
                            @endif
                            {{ $item['label'] }}
                        </a>
                    @endif
                </li>
                @endforeach

                {{-- Search Toggle (if enabled) --}}
                @if(isset($subdomainData['features']['search_enabled']) && $subdomainData['features']['search_enabled'])
                <li class="nav-item">
                    <button class="nav-link btn btn-link" 
                            type="button" 
                            data-bs-toggle="modal" 
                            data-bs-target="#searchModal"
                            aria-label="Search properties">
                        <i class="fas fa-search"></i>
                        <span class="d-lg-none ms-1">Search</span>
                    </button>
                </li>
                @endif

                {{-- Contact CTA Button (if configured) --}}
                @if(isset($subdomainData['contact']['cta_button']['show']) && $subdomainData['contact']['cta_button']['show'])
                <li class="nav-item ms-2">
                    <a class="btn btn-primary nav-cta-btn" 
                       href="{{ $subdomainData['contact']['cta_button']['url'] ?? '/contact' }}">
                        <i class="fas fa-phone me-1"></i>
                        {{ $subdomainData['contact']['cta_button']['text'] ?? 'Get Started' }}
                    </a>
                </li>
                @endif
            </ul>

            {{-- Secondary Actions --}}
            @if(isset($subdomainData['features']['user_accounts']) && $subdomainData['features']['user_accounts'])
            <ul class="navbar-nav ms-auto">
                @auth
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" 
                       href="#" 
                       role="button" 
                       data-bs-toggle="dropdown" 
                       aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i>
                        {{ auth()->user()->name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="/saved-properties"><i class="fas fa-heart me-2"></i>Saved Properties</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </li>
                @else
                <li class="nav-item">
                    <a class="nav-link" href="{{ route('login') }}">
                        <i class="fas fa-sign-in-alt me-1"></i>Login
                    </a>
                </li>
                <li class="nav-item">
                    <a class="btn btn-outline-primary btn-sm" href="{{ route('register') }}">
                        <i class="fas fa-user-plus me-1"></i>Register
                    </a>
                </li>
                @endauth
            </ul>
            @endif
        </div>
    </div>
</nav>

{{-- Search Modal (if search is enabled) --}}
@if(isset($subdomainData['features']['search_enabled']) && $subdomainData['features']['search_enabled'])
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchModalLabel">
                    <i class="fas fa-search me-2"></i>Search Properties
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="/search" method="GET" role="search">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="searchLocation" class="form-label">Location</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="searchLocation" 
                                   name="location" 
                                   placeholder="Enter suburb, city, or postcode"
                                   autocomplete="off">
                        </div>
                        <div class="col-md-3">
                            <label for="searchMinPrice" class="form-label">Min Price</label>
                            <select class="form-select" id="searchMinPrice" name="min_price">
                                <option value="">Any</option>
                                <option value="100000">$100,000+</option>
                                <option value="200000">$200,000+</option>
                                <option value="300000">$300,000+</option>
                                <option value="400000">$400,000+</option>
                                <option value="500000">$500,000+</option>
                                <option value="750000">$750,000+</option>
                                <option value="1000000">$1,000,000+</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="searchMaxPrice" class="form-label">Max Price</label>
                            <select class="form-select" id="searchMaxPrice" name="max_price">
                                <option value="">Any</option>
                                <option value="300000">$300,000</option>
                                <option value="400000">$400,000</option>
                                <option value="500000">$500,000</option>
                                <option value="750000">$750,000</option>
                                <option value="1000000">$1,000,000</option>
                                <option value="1500000">$1,500,000</option>
                                <option value="2000000">$2,000,000+</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="searchPropertyType" class="form-label">Property Type</label>
                            <select class="form-select" id="searchPropertyType" name="property_type">
                                <option value="">Any Type</option>
                                @if(isset($subdomain_property_types) && is_array($subdomain_property_types))
                                    @foreach($subdomain_property_types as $type)
                                    <option value="{{ $type['id'] ?? $type }}">{{ $type['name'] ?? $type }}</option>
                                    @endforeach
                                @else
                                    <option value="house">House</option>
                                    <option value="apartment">Apartment</option>
                                    <option value="townhouse">Townhouse</option>
                                    <option value="land">Land</option>
                                @endif
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="searchBedrooms" class="form-label">Bedrooms</label>
                            <select class="form-select" id="searchBedrooms" name="bedrooms">
                                <option value="">Any</option>
                                <option value="1">1+</option>
                                <option value="2">2+</option>
                                <option value="3">3+</option>
                                <option value="4">4+</option>
                                <option value="5">5+</option>
                            </select>
                        </div>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Search Properties
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endif

{{-- Navigation Styles --}}
@push('styles')
<style>
.navbar-custom {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.navbar-custom .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-custom .navbar-toggler:focus {
    box-shadow: none;
}

.navbar-custom .navbar-toggler-icon {
    background-image: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5em;
    height: 1.5em;
}

.navbar-custom .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-custom .nav-link:hover {
    transform: translateY(-1px);
}

.navbar-custom .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: currentColor;
}

.nav-cta-btn {
    border-radius: 50px;
    font-weight: 600;
    padding: 0.5rem 1.25rem;
    transition: all 0.3s ease;
}

.nav-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-radius: 8px;
}

.dropdown-item {
    padding: 0.75rem 1.25rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--bs-primary);
    color: white;
    transform: translateX(5px);
}

@media (max-width: 991.98px) {
    .navbar-nav {
        padding-top: 1rem;
    }
    
    .nav-cta-btn {
        margin-top: 0.5rem;
        text-align: center;
    }
    
    .navbar-custom .nav-link.active::after {
        display: none;
    }
}

/* Fixed navbar adjustments */
.fixed-top + main {
    padding-top: 76px;
}

@media (max-width: 991.98px) {
    .fixed-top + main {
        padding-top: 60px;
    }
}
</style>
@endpush