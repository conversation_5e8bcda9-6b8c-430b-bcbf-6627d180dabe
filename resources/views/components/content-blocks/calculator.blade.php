@props(['title', 'content', 'settings', 'subdomainData'])

@php
    $calculatorType = $settings['type'] ?? 'mortgage';
    $showAdvanced = $settings['show_advanced'] ?? false;
    $style = $settings['style'] ?? 'default';
    $backgroundColor = $settings['background_color'] ?? '#ffffff';
    $borderColor = $settings['border_color'] ?? '#e9ecef';
@endphp

<section class="calculator-section calculator-style-{{ $style }} py-5" 
         style="background-color: {{ $backgroundColor }};">
    
    <div class="container">
        
        @if($title)
        <div class="section-title text-center mb-5">
            <h2 class="h1 font-weight-bold">{{ $title }}</h2>
            @if($content)
            <div class="section-description mt-3">
                {!! $content !!}
            </div>
            @endif
        </div>
        @endif
        
        <div class="calculator-container" style="border: 1px solid {{ $borderColor }};">
            <div class="calculator-content p-4">
                
                @if($calculatorType === 'mortgage')
                <div class="mortgage-calculator">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="calculator-inputs">
                                <div class="form-group">
                                    <label for="loanAmount">Loan Amount</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" class="form-control" id="loanAmount" value="500000">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="interestRate">Interest Rate (%)</label>
                                            <input type="number" class="form-control" id="interestRate" value="3.5" step="0.1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="loanTerm">Loan Term (years)</label>
                                            <select class="form-control" id="loanTerm">
                                                <option value="30">30 years</option>
                                                <option value="25">25 years</option>
                                                <option value="20">20 years</option>
                                                <option value="15">15 years</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                @if($showAdvanced)
                                <div class="advanced-options">
                                    <h5>Advanced Options</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="propertyTax">Property Tax (monthly)</label>
                                                <input type="number" class="form-control" id="propertyTax" value="500">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="insurance">Insurance (monthly)</label>
                                                <input type="number" class="form-control" id="insurance" value="200">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                
                                <button type="button" class="btn btn-primary btn-lg btn-block" onclick="calculateMortgage()">
                                    Calculate Payment
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="calculator-results bg-light p-4 rounded">
                                <h4>Monthly Payment</h4>
                                <div class="payment-result">
                                    <div class="payment-amount h2 text-primary" id="monthlyPayment">$2,533</div>
                                    
                                    @if($showAdvanced)
                                    <div class="payment-breakdown mt-3">
                                        <div class="breakdown-item d-flex justify-content-between">
                                            <span>Principal & Interest:</span>
                                            <span id="principalInterest">$2,533</span>
                                        </div>
                                        <div class="breakdown-item d-flex justify-content-between">
                                            <span>Property Tax:</span>
                                            <span id="taxAmount">$500</span>
                                        </div>
                                        <div class="breakdown-item d-flex justify-content-between">
                                            <span>Insurance:</span>
                                            <span id="insuranceAmount">$200</span>
                                        </div>
                                        <hr>
                                        <div class="breakdown-item d-flex justify-content-between font-weight-bold">
                                            <span>Total:</span>
                                            <span id="totalPayment">$3,233</span>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                @elseif($calculatorType === 'investment')
                <div class="investment-calculator">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="propertyValue">Property Value</label>
                                <input type="number" class="form-control" id="propertyValue" value="600000">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="weeklyRent">Weekly Rent</label>
                                <input type="number" class="form-control" id="weeklyRent" value="650">
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-primary btn-lg btn-block" onclick="calculateROI()">
                        Calculate ROI
                    </button>
                    
                    <div class="roi-results mt-4 p-4 bg-light rounded">
                        <div class="result-item">
                            <strong>Rental Yield:</strong> <span id="rentalYield">5.63%</span>
                        </div>
                        <div class="result-item">
                            <strong>Annual Rent:</strong> <span id="annualRent">$33,800</span>
                        </div>
                    </div>
                </div>
                @endif
                
                @if(isset($settings['disclaimer']))
                <div class="calculator-disclaimer mt-4">
                    <small class="text-muted">{!! $settings['disclaimer'] !!}</small>
                </div>
                @endif
                
            </div>
        </div>
        
        @if(isset($settings['cta']))
        <div class="calculator-cta text-center mt-4">
            <a href="{{ $settings['cta']['url'] ?? '#' }}" class="btn btn-outline-primary btn-lg">
                {{ $settings['cta']['text'] ?? 'Get Pre-Approved' }}
            </a>
        </div>
        @endif
        
    </div>
</section>

@push('scripts')
<script>
function calculateMortgage() {
    const loanAmount = parseFloat(document.getElementById('loanAmount').value);
    const interestRate = parseFloat(document.getElementById('interestRate').value) / 100 / 12;
    const loanTerm = parseFloat(document.getElementById('loanTerm').value) * 12;
    
    const monthlyPayment = loanAmount * (interestRate * Math.pow(1 + interestRate, loanTerm)) / (Math.pow(1 + interestRate, loanTerm) - 1);
    
    document.getElementById('monthlyPayment').textContent = '$' + monthlyPayment.toFixed(0);
    document.getElementById('principalInterest').textContent = '$' + monthlyPayment.toFixed(0);
    
    @if($showAdvanced)
    const propertyTax = parseFloat(document.getElementById('propertyTax').value) || 0;
    const insurance = parseFloat(document.getElementById('insurance').value) || 0;
    const total = monthlyPayment + propertyTax + insurance;
    
    document.getElementById('taxAmount').textContent = '$' + propertyTax.toFixed(0);
    document.getElementById('insuranceAmount').textContent = '$' + insurance.toFixed(0);
    document.getElementById('totalPayment').textContent = '$' + total.toFixed(0);
    @endif
}

function calculateROI() {
    const propertyValue = parseFloat(document.getElementById('propertyValue').value);
    const weeklyRent = parseFloat(document.getElementById('weeklyRent').value);
    const annualRent = weeklyRent * 52;
    const rentalYield = (annualRent / propertyValue) * 100;
    
    document.getElementById('rentalYield').textContent = rentalYield.toFixed(2) + '%';
    document.getElementById('annualRent').textContent = '$' + annualRent.toLocaleString();
}

// Initialize calculations
document.addEventListener('DOMContentLoaded', function() {
    @if($calculatorType === 'mortgage')
    calculateMortgage();
    @elseif($calculatorType === 'investment')
    calculateROI();
    @endif
});
</script>
@endpush

@push('styles')
<style>
.calculator-section {
    background-color: #f8f9fa;
}

.calculator-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.calculator-inputs .form-group {
    margin-bottom: 1.5rem;
}

.calculator-inputs label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.calculator-results {
    border-radius: 8px;
    min-height: 200px;
}

.payment-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 1rem 0;
}

.breakdown-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.breakdown-item:last-child {
    border-bottom: none;
}

.advanced-options {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1.5rem 0;
}

.calculator-disclaimer {
    padding: 1rem;
    background: #fff3cd;
    border-radius: 5px;
    border-left: 4px solid #ffc107;
}

.roi-results .result-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
    font-size: 1.1rem;
}

.roi-results .result-item:last-child {
    border-bottom: none;
}

@media (max-width: 768px) {
    .payment-amount {
        font-size: 2rem;
    }
    
    .calculator-inputs,
    .calculator-results {
        margin-bottom: 2rem;
    }
}
</style>
@endpush