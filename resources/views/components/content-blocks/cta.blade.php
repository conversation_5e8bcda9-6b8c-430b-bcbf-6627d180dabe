@props(['title', 'content', 'settings', 'subdomainData'])

@php
    $ctaText = $settings['cta_text'] ?? 'Get Started';
    $ctaUrl = $settings['cta_url'] ?? '#';
    $secondaryCtaText = $settings['secondary_cta_text'] ?? '';
    $secondaryCtaUrl = $settings['secondary_cta_url'] ?? '';
    $style = $settings['style'] ?? 'default';
    $backgroundColor = $settings['background_color'] ?? '#007bff';
    $textColor = $settings['text_color'] ?? '#ffffff';
    $alignment = $settings['alignment'] ?? 'center';
    $size = $settings['size'] ?? 'medium';
@endphp

<section class="cta-section cta-style-{{ $style }} cta-size-{{ $size }} text-{{ $alignment }} py-5" 
         style="background-color: {{ $backgroundColor }}; color: {{ $textColor }};">
    
    <div class="container">
        <div class="cta-content">
            
            @if($title)
            <h2 class="cta-title mb-4">{{ $title }}</h2>
            @endif
            
            @if($content)
            <div class="cta-description mb-4">
                {!! $content !!}
            </div>
            @endif
            
            @if(isset($settings['features']) && is_array($settings['features']))
            <div class="cta-features mb-4">
                <div class="row justify-content-center">
                    @foreach($settings['features'] as $feature)
                    <div class="col-md-4 mb-2">
                        <div class="cta-feature-item">
                            @if(isset($feature['icon']))
                            <i class="fas {{ $feature['icon'] }} mr-2"></i>
                            @endif
                            {{ $feature['text'] ?? $feature }}
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
            
            <div class="cta-buttons">
                @if($ctaText && $ctaUrl !== '#')
                <a href="{{ $ctaUrl }}" 
                   class="btn btn-{{ $style === 'outline' ? 'outline-light' : 'light' }} btn-lg cta-primary mr-3 mb-2">
                    {{ $ctaText }}
                    @if(isset($settings['show_arrow']) && $settings['show_arrow'])
                    <i class="fas fa-arrow-right ml-2"></i>
                    @endif
                </a>
                @endif
                
                @if($secondaryCtaText && $secondaryCtaUrl !== '#')
                <a href="{{ $secondaryCtaUrl }}" 
                   class="btn btn-outline-light btn-lg cta-secondary mb-2">
                    {{ $secondaryCtaText }}
                </a>
                @endif
            </div>
            
            @if(isset($settings['urgency_text']))
            <div class="cta-urgency mt-3">
                <small class="text-warning">
                    <i class="fas fa-clock mr-1"></i>
                    {{ $settings['urgency_text'] }}
                </small>
            </div>
            @endif
            
            @if(isset($settings['trust_indicators']) && is_array($settings['trust_indicators']))
            <div class="cta-trust-indicators mt-4">
                <div class="row justify-content-center">
                    @foreach($settings['trust_indicators'] as $indicator)
                    <div class="col-auto">
                        <div class="trust-indicator">
                            @if(isset($indicator['icon']))
                            <i class="fas {{ $indicator['icon'] }} mr-1"></i>
                            @endif
                            <small>{{ $indicator['text'] ?? $indicator }}</small>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
            
        </div>
    </div>
    
    @if($style === 'gradient')
    <div class="cta-gradient-overlay"></div>
    @endif
    
</section>

@push('styles')
<style>
.cta-section {
    position: relative;
    overflow: hidden;
}

.cta-style-default {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cta-style-professional {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.cta-style-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.cta-style-minimal {
    background-color: #f8f9fa;
    color: #343a40;
}

.cta-size-small {
    padding: 3rem 0;
}

.cta-size-medium {
    padding: 5rem 0;
}

.cta-size-large {
    padding: 7rem 0;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.cta-description {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.cta-buttons .btn {
    font-size: 1.1rem;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.cta-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.cta-secondary {
    border-width: 2px;
}

.cta-feature-item {
    font-size: 1rem;
    opacity: 0.9;
}

.cta-urgency {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.trust-indicator {
    background: rgba(255,255,255,0.1);
    padding: 8px 16px;
    border-radius: 20px;
    margin: 0 5px;
    font-size: 0.9rem;
}

.cta-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

@media (max-width: 768px) {
    .cta-title {
        font-size: 2rem;
    }
    
    .cta-description {
        font-size: 1rem;
    }
    
    .cta-buttons .btn {
        font-size: 1rem;
        padding: 10px 25px;
        margin-bottom: 1rem;
        display: block;
        width: 100%;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .cta-feature-item {
        text-align: center;
        margin-bottom: 1rem;
    }
}
</style>
@endpush