@props(['title', 'content', 'settings', 'blockType', 'subdomainData'])

@php
    $style = $settings['style'] ?? 'default';
    $alignment = $settings['alignment'] ?? 'left';
    $backgroundColor = $settings['background_color'] ?? '#ffffff';
    $textColor = $settings['text_color'] ?? '#333333';
    $padding = $settings['padding'] ?? 'medium';
@endphp

<section class="default-content-block content-style-{{ $style }} content-align-{{ $alignment }} content-padding-{{ $padding }}" 
         style="background-color: {{ $backgroundColor }}; color: {{ $textColor }};">
    
    <div class="container">
        <div class="content-wrapper">
            
            @if($title)
            <div class="content-title mb-4">
                <h2 class="h1 font-weight-bold">{{ $title }}</h2>
            </div>
            @endif
            
            @if($content)
            <div class="content-body">
                {!! $content !!}
            </div>
            @endif
            
            @if(isset($settings['buttons']) && is_array($settings['buttons']))
            <div class="content-actions mt-4">
                @foreach($settings['buttons'] as $button)
                <a href="{{ $button['url'] ?? '#' }}" 
                   class="btn {{ $button['style'] ?? 'btn-primary' }} {{ $button['size'] ?? '' }} mr-2 mb-2"
                   @if(isset($button['target'])) target="{{ $button['target'] }}" @endif>
                    {{ $button['text'] ?? 'Learn More' }}
                    @if(isset($button['icon']))
                    <i class="fas {{ $button['icon'] }} ml-2"></i>
                    @endif
                </a>
                @endforeach
            </div>
            @endif
            
            @if(isset($settings['image']))
            <div class="content-image mt-4">
                <img src="{{ $settings['image'] }}" 
                     alt="{{ $settings['image_alt'] ?? $title }}" 
                     class="img-fluid {{ $settings['image_class'] ?? '' }}">
            </div>
            @endif
            
            @if(isset($settings['list_items']) && is_array($settings['list_items']))
            <div class="content-list mt-4">
                <ul class="list-{{ $settings['list_style'] ?? 'styled' }}">
                    @foreach($settings['list_items'] as $item)
                    <li class="list-item">
                        @if(isset($item['icon']))
                        <i class="fas {{ $item['icon'] }} mr-2"></i>
                        @endif
                        {{ $item['text'] ?? $item }}
                    </li>
                    @endforeach
                </ul>
            </div>
            @endif
            
            @if(isset($settings['columns']) && is_array($settings['columns']))
            <div class="content-columns mt-4">
                <div class="row">
                    @foreach($settings['columns'] as $column)
                    <div class="col-md-{{ $column['width'] ?? '6' }}">
                        <div class="column-content">
                            @if(isset($column['title']))
                            <h4>{{ $column['title'] }}</h4>
                            @endif
                            @if(isset($column['content']))
                            {!! $column['content'] !!}
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
            
            @if(isset($settings['video_url']))
            <div class="content-video mt-4">
                <div class="embed-responsive embed-responsive-16by9">
                    @if(str_contains($settings['video_url'], 'youtube.com') || str_contains($settings['video_url'], 'youtu.be'))
                    <iframe class="embed-responsive-item" 
                            src="{{ $settings['video_url'] }}" 
                            allowfullscreen></iframe>
                    @elseif(str_contains($settings['video_url'], 'vimeo.com'))
                    <iframe class="embed-responsive-item" 
                            src="{{ $settings['video_url'] }}" 
                            allowfullscreen></iframe>
                    @else
                    <video class="embed-responsive-item" controls>
                        <source src="{{ $settings['video_url'] }}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    @endif
                </div>
            </div>
            @endif
            
        </div>
    </div>
</section>

@push('styles')
<style>
.default-content-block {
    padding: 3rem 0;
}

.content-padding-small {
    padding: 2rem 0;
}

.content-padding-medium {
    padding: 3rem 0;
}

.content-padding-large {
    padding: 5rem 0;
}

.content-align-center {
    text-align: center;
}

.content-align-right {
    text-align: right;
}

.content-style-bordered {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    margin: 2rem 0;
}

.content-style-shadow {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin: 2rem 0;
}

.content-style-gradient {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.content-wrapper {
    max-width: 100%;
}

.content-title h2 {
    color: inherit;
}

.content-body {
    font-size: 1.1rem;
    line-height: 1.7;
}

.content-body p {
    margin-bottom: 1.5rem;
}

.content-body h3,
.content-body h4,
.content-body h5 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: inherit;
}

.content-actions .btn {
    font-weight: 600;
    border-radius: 50px;
    padding: 12px 24px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.content-actions .btn:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.content-image {
    text-align: center;
}

.content-image img {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.list-styled {
    list-style: none;
    padding-left: 0;
}

.list-styled .list-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.list-styled .list-item:last-child {
    border-bottom: none;
}

.list-checkmarks .list-item:before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    margin-right: 0.5rem;
}

.column-content {
    padding: 1rem;
}

.content-video {
    margin: 2rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .default-content-block {
        padding: 2rem 0;
    }
    
    .content-padding-large {
        padding: 3rem 0;
    }
    
    .content-title h2 {
        font-size: 2rem;
    }
    
    .content-body {
        font-size: 1rem;
    }
    
    .content-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
        margin-right: 0;
    }
    
    .column-content {
        margin-bottom: 2rem;
    }
}

/* Print styles */
@media print {
    .content-actions {
        display: none;
    }
    
    .content-video {
        display: none;
    }
}
</style>
@endpush