@props(['title', 'content', 'settings', 'subdomainData'])

@php
    $tools = $settings['tools'] ?? [];
    $layout = $settings['layout'] ?? 'grid';
    $columns = $settings['columns'] ?? 3;
    $style = $settings['style'] ?? 'default';
    $showDescriptions = $settings['show_descriptions'] ?? true;
@endphp

<section class="tools-section tools-style-{{ $style }} py-5">
    <div class="container">
        
        @if($title)
        <div class="section-title text-center mb-5">
            <h2 class="h1 font-weight-bold">{{ $title }}</h2>
            @if($content)
            <div class="section-description mt-3">
                {!! $content !!}
            </div>
            @endif
        </div>
        @endif
        
        @if(!empty($tools))
        <div class="tools-container">
            <div class="row">
                @foreach($tools as $index => $tool)
                <div class="col-md-{{ 12 / $columns }} mb-4">
                    <div class="tool-item tool-layout-{{ $layout }}" 
                         data-aos="fade-up" 
                         data-aos-delay="{{ $index * 100 }}">
                        
                        <div class="tool-content">
                            
                            @if(isset($tool['icon']))
                            <div class="tool-icon mb-3">
                                <i class="fas {{ $tool['icon'] }} fa-3x"></i>
                            </div>
                            @elseif(isset($tool['image']))
                            <div class="tool-image mb-3">
                                <img src="{{ $tool['image'] }}" alt="{{ $tool['title'] ?? '' }}" class="img-fluid">
                            </div>
                            @endif
                            
                            @if(isset($tool['title']))
                            <h4 class="tool-title mb-3">{{ $tool['title'] }}</h4>
                            @endif
                            
                            @if(isset($tool['description']) && $showDescriptions)
                            <p class="tool-description text-muted mb-3">{{ $tool['description'] }}</p>
                            @endif
                            
                            @if(isset($tool['features']) && is_array($tool['features']))
                            <div class="tool-features mb-3">
                                <ul class="list-unstyled">
                                    @foreach($tool['features'] as $feature)
                                    <li class="feature-item">
                                        <i class="fas fa-check text-success mr-2"></i>
                                        {{ $feature }}
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                            @endif
                            
                            <div class="tool-actions">
                                @if(isset($tool['url']))
                                <a href="{{ $tool['url'] }}" 
                                   class="btn btn-primary btn-block tool-button"
                                   @if(isset($tool['external']) && $tool['external']) target="_blank" @endif>
                                    {{ $tool['button_text'] ?? 'Use Tool' }}
                                    @if(isset($tool['external']) && $tool['external'])
                                    <i class="fas fa-external-link-alt ml-2"></i>
                                    @else
                                    <i class="fas fa-arrow-right ml-2"></i>
                                    @endif
                                </a>
                                @endif
                                
                                @if(isset($tool['demo_url']))
                                <a href="{{ $tool['demo_url'] }}" 
                                   class="btn btn-outline-secondary btn-sm mt-2"
                                   target="_blank">
                                    View Demo
                                </a>
                                @endif
                            </div>
                            
                            @if(isset($tool['tags']) && is_array($tool['tags']))
                            <div class="tool-tags mt-3">
                                @foreach($tool['tags'] as $tag)
                                <span class="badge badge-light mr-1">{{ $tag }}</span>
                                @endforeach
                            </div>
                            @endif
                            
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
        
        @if(isset($settings['cta']))
        <div class="tools-cta text-center mt-5">
            <div class="cta-content">
                @if(isset($settings['cta']['title']))
                <h3>{{ $settings['cta']['title'] }}</h3>
                @endif
                @if(isset($settings['cta']['description']))
                <p class="text-muted">{{ $settings['cta']['description'] }}</p>
                @endif
                <a href="{{ $settings['cta']['url'] ?? '#' }}" class="btn btn-primary btn-lg">
                    {{ $settings['cta']['text'] ?? 'Get Started' }}
                </a>
            </div>
        </div>
        @endif
        
    </div>
</section>

@push('styles')
<style>
.tools-section {
    background-color: #f8f9fa;
}

.tool-item {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    overflow: hidden;
}

.tool-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.tools-style-cards .tool-item {
    border: 1px solid #e9ecef;
}

.tools-style-minimal .tool-item {
    box-shadow: none;
    border: 2px solid #e9ecef;
}

.tools-style-professional .tool-item {
    border-left: 4px solid #007bff;
}

.tool-content {
    padding: 2rem;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tool-icon {
    color: #007bff;
    margin-bottom: 1rem;
}

.tool-icon i {
    transition: all 0.3s ease;
}

.tool-item:hover .tool-icon i {
    transform: scale(1.1);
}

.tool-image img {
    max-height: 80px;
    width: auto;
}

.tool-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.tool-description {
    line-height: 1.6;
    flex-grow: 1;
}

.tool-features {
    text-align: left;
    margin: 1rem 0;
}

.tool-features .feature-item {
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

.tool-actions {
    margin-top: auto;
}

.tool-button {
    font-weight: 600;
    border-radius: 50px;
    padding: 12px 24px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.tool-button:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.tool-tags {
    text-align: center;
}

.tool-tags .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    background-color: #e9ecef;
    color: #6c757d;
}

.tools-cta {
    background: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.tool-layout-list .tool-content {
    text-align: left;
    padding: 1.5rem;
}

.tool-layout-list .tool-icon {
    text-align: center;
    margin-bottom: 1rem;
}

.tool-layout-compact .tool-content {
    padding: 1.5rem;
}

.tool-layout-compact .tool-icon i {
    font-size: 2rem;
}

.tool-layout-compact .tool-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .tool-content {
        padding: 1.5rem;
    }
    
    .tool-icon i {
        font-size: 2.5rem;
    }
    
    .tool-title {
        font-size: 1.2rem;
    }
    
    .tools-cta {
        padding: 2rem 1rem;
    }
    
    .col-md-4,
    .col-md-3,
    .col-md-6 {
        margin-bottom: 2rem;
    }
}
</style>
@endpush