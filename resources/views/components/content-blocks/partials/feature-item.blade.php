@props(['feature', 'showIcons' => true, 'style' => 'default', 'layout' => 'vertical', 'index' => 0])

@php
    $title = $feature['title'] ?? '';
    $description = $feature['description'] ?? '';
    $icon = $feature['icon'] ?? 'fa-star';
    $image = $feature['image'] ?? '';
    $link = $feature['link'] ?? '';
    $color = $feature['color'] ?? '#007bff';
@endphp

<div class="feature-item feature-layout-{{ $layout }} feature-style-{{ $style }}" 
     data-aos="fade-up" 
     data-aos-delay="{{ $index * 100 }}">
    
    @if($layout === 'horizontal')
    <div class="row align-items-center">
        <div class="col-md-2 text-center">
            @if($showIcons && $icon)
            <div class="feature-icon">
                <i class="fas {{ $icon }}" style="color: {{ $color }};"></i>
            </div>
            @elseif($image)
            <div class="feature-image">
                <img src="{{ $image }}" alt="{{ $title }}" class="img-fluid">
            </div>
            @endif
        </div>
        <div class="col-md-10">
            <div class="feature-content">
                @if($title)
                <h4 class="feature-title">{{ $title }}</h4>
                @endif
                @if($description)
                <p class="feature-description">{{ $description }}</p>
                @endif
                @if($link)
                <a href="{{ $link }}" class="feature-link">Learn More <i class="fas fa-arrow-right"></i></a>
                @endif
            </div>
        </div>
    </div>
    
    @else
    <div class="feature-content text-center p-4">
        
        @if($showIcons && $icon)
        <div class="feature-icon mb-3">
            <i class="fas {{ $icon }} fa-3x" style="color: {{ $color }};"></i>
        </div>
        @elseif($image)
        <div class="feature-image mb-3">
            <img src="{{ $image }}" alt="{{ $title }}" class="img-fluid" style="max-height: 80px;">
        </div>
        @endif
        
        @if($title)
        <h4 class="feature-title mb-3">{{ $title }}</h4>
        @endif
        
        @if($description)
        <p class="feature-description text-muted">{{ $description }}</p>
        @endif
        
        @if($link)
        <a href="{{ $link }}" class="feature-link btn btn-sm btn-outline-primary">
            Learn More <i class="fas fa-arrow-right ml-1"></i>
        </a>
        @endif
        
    </div>
    @endif
    
</div>

@push('styles')
<style>
.feature-item {
    height: 100%;
    transition: all 0.3s ease;
}

.feature-style-cards .feature-item {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.feature-style-cards .feature-item:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.feature-style-minimal .feature-item {
    background: transparent;
}

.feature-style-bordered .feature-item {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
}

.feature-style-bordered .feature-item:hover {
    border-color: #007bff;
}

.feature-icon {
    margin-bottom: 1rem;
}

.feature-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-description {
    line-height: 1.6;
    color: #6c757d;
}

.feature-link {
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.feature-link:hover {
    text-decoration: none;
    transform: translateX(5px);
}

.feature-layout-horizontal .feature-icon i {
    font-size: 2.5rem;
}

.feature-layout-horizontal .feature-title {
    margin-bottom: 0.5rem;
}

.feature-layout-horizontal .feature-description {
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .feature-layout-horizontal .col-md-2,
    .feature-layout-horizontal .col-md-10 {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .feature-layout-horizontal .row {
        text-align: center;
    }
}
</style>
@endpush