@props(['testimonial', 'showAvatar' => true, 'showRating' => true, 'style' => 'default', 'layout' => 'vertical', 'index' => 0])

@php
    $name = $testimonial['name'] ?? '';
    $title = $testimonial['title'] ?? '';
    $company = $testimonial['company'] ?? '';
    $content = $testimonial['content'] ?? '';
    $avatar = $testimonial['avatar'] ?? '';
    $rating = $testimonial['rating'] ?? 5;
    $location = $testimonial['location'] ?? '';
@endphp

<div class="testimonial-item testimonial-layout-{{ $layout }} testimonial-style-{{ $style }}" 
     data-aos="fade-up" 
     data-aos-delay="{{ $index * 100 }}">
    
    <div class="testimonial-content">
        
        @if($showRating && $rating)
        <div class="testimonial-rating mb-3">
            @for($i = 1; $i <= 5; $i++)
                @if($i <= $rating)
                <i class="fas fa-star text-warning"></i>
                @else
                <i class="far fa-star text-muted"></i>
                @endif
            @endfor
        </div>
        @endif
        
        @if($content)
        <div class="testimonial-text mb-4">
            <i class="fas fa-quote-left quote-icon"></i>
            <p class="quote-content">{{ $content }}</p>
            <i class="fas fa-quote-right quote-icon"></i>
        </div>
        @endif
        
        <div class="testimonial-author">
            
            @if($layout === 'horizontal')
            <div class="row align-items-center">
                @if($showAvatar && $avatar)
                <div class="col-auto">
                    <div class="author-avatar">
                        <img src="{{ $avatar }}" alt="{{ $name }}" class="rounded-circle">
                    </div>
                </div>
                @endif
                <div class="col">
                    <div class="author-info">
                        @if($name)
                        <h5 class="author-name mb-1">{{ $name }}</h5>
                        @endif
                        @if($title || $company)
                        <div class="author-details text-muted">
                            @if($title){{ $title }}@endif
                            @if($title && $company), @endif
                            @if($company){{ $company }}@endif
                        </div>
                        @endif
                        @if($location)
                        <div class="author-location text-muted">
                            <i class="fas fa-map-marker-alt mr-1"></i>{{ $location }}
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            
            @else
            <div class="author-vertical text-center">
                @if($showAvatar && $avatar)
                <div class="author-avatar mb-3">
                    <img src="{{ $avatar }}" alt="{{ $name }}" class="rounded-circle">
                </div>
                @endif
                
                <div class="author-info">
                    @if($name)
                    <h5 class="author-name mb-1">{{ $name }}</h5>
                    @endif
                    @if($title || $company)
                    <div class="author-details text-muted">
                        @if($title){{ $title }}@endif
                        @if($title && $company), @endif
                        @if($company){{ $company }}@endif
                    </div>
                    @endif
                    @if($location)
                    <div class="author-location text-muted mt-1">
                        <i class="fas fa-map-marker-alt mr-1"></i>{{ $location }}
                    </div>
                    @endif
                </div>
            </div>
            @endif
            
        </div>
        
        @if(isset($testimonial['verified']) && $testimonial['verified'])
        <div class="verified-badge mt-3">
            <span class="badge badge-success">
                <i class="fas fa-check-circle mr-1"></i>Verified Review
            </span>
        </div>
        @endif
        
    </div>
</div>

@push('styles')
<style>
.testimonial-item {
    height: 100%;
    transition: all 0.3s ease;
}

.testimonial-style-cards .testimonial-item {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.testimonial-style-cards .testimonial-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.testimonial-style-minimal .testimonial-item {
    background: transparent;
    padding: 1.5rem;
    border-left: 4px solid #007bff;
}

.testimonial-style-bordered .testimonial-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 2rem;
}

.testimonial-style-bordered .testimonial-item:hover {
    border-color: #007bff;
}

.testimonial-style-bubble .testimonial-item {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.testimonial-style-bubble .testimonial-item:before {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 30px;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 15px solid white;
}

.testimonial-rating {
    font-size: 1.1rem;
}

.testimonial-text {
    position: relative;
    font-style: italic;
    line-height: 1.6;
}

.quote-icon {
    color: #007bff;
    opacity: 0.3;
    font-size: 1.5rem;
}

.quote-content {
    margin: 0;
    font-size: 1.1rem;
    color: #2c3e50;
}

.author-avatar img {
    width: 60px;
    height: 60px;
    object-fit: cover;
}

.testimonial-layout-centered .author-avatar img {
    width: 80px;
    height: 80px;
}

.author-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.author-details {
    font-size: 0.9rem;
    line-height: 1.4;
}

.author-location {
    font-size: 0.8rem;
}

.verified-badge {
    text-align: center;
}

.verified-badge .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.testimonial-layout-horizontal .testimonial-content {
    text-align: left;
}

.testimonial-layout-centered {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.testimonial-layout-centered .testimonial-text {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.testimonial-layout-centered .quote-content {
    font-size: 1.3rem;
}

@media (max-width: 768px) {
    .testimonial-item {
        margin-bottom: 2rem;
    }
    
    .testimonial-style-cards .testimonial-item,
    .testimonial-style-bordered .testimonial-item,
    .testimonial-style-bubble .testimonial-item {
        padding: 1.5rem;
    }
    
    .author-avatar img {
        width: 50px;
        height: 50px;
    }
    
    .testimonial-layout-centered .author-avatar img {
        width: 60px;
        height: 60px;
    }
    
    .quote-content {
        font-size: 1rem;
    }
    
    .testimonial-layout-centered .quote-content {
        font-size: 1.1rem;
    }
}
</style>
@endpush