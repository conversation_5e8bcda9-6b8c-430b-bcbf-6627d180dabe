@props(['title', 'content', 'settings', 'subdomainData'])

@php
    $features = $settings['features'] ?? [];
    $layout = $settings['layout'] ?? 'grid'; // grid, list, carousel
    $columns = $settings['columns'] ?? 3;
    $showIcons = $settings['show_icons'] ?? true;
    $style = $settings['style'] ?? 'default';
@endphp

<section class="features-section features-style-{{ $style }} py-5">
    <div class="container">
        
        @if($title)
        <div class="section-title text-center mb-5">
            <h2 class="h1 font-weight-bold">{{ $title }}</h2>
            @if($content)
            <div class="section-description mt-3">
                {!! $content !!}
            </div>
            @endif
        </div>
        @endif
        
        @if(!empty($features))
        <div class="features-container">
            
            @if($layout === 'grid')
            <div class="row">
                @foreach($features as $index => $feature)
                <div class="col-md-{{ 12 / $columns }} mb-4">
                    @include('components.content-blocks.partials.feature-item', [
                        'feature' => $feature,
                        'showIcons' => $showIcons,
                        'style' => $style,
                        'index' => $index
                    ])
                </div>
                @endforeach
            </div>
            
            @elseif($layout === 'list')
            <div class="features-list">
                @foreach($features as $index => $feature)
                <div class="feature-list-item mb-4">
                    @include('components.content-blocks.partials.feature-item', [
                        'feature' => $feature,
                        'showIcons' => $showIcons,
                        'style' => $style,
                        'layout' => 'horizontal',
                        'index' => $index
                    ])
                </div>
                @endforeach
            </div>
            
            @elseif($layout === 'carousel')
            <div class="features-carousel" id="featuresCarousel">
                <div class="row">
                    @foreach($features as $index => $feature)
                    <div class="col-md-4 mb-4">
                        @include('components.content-blocks.partials.feature-item', [
                            'feature' => $feature,
                            'showIcons' => $showIcons,
                            'style' => $style,
                            'index' => $index
                        ])
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
            
        </div>
        @endif
        
        @if(isset($settings['cta']))
        <div class="features-cta text-center mt-5">
            <a href="{{ $settings['cta']['url'] ?? '#' }}" class="btn btn-primary btn-lg">
                {{ $settings['cta']['text'] ?? 'Learn More' }}
            </a>
        </div>
        @endif
        
    </div>
</section>

@push('styles')
<style>
.features-section {
    background-color: #f8f9fa;
}

.features-style-cards .feature-item {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.features-style-cards .feature-item:hover {
    transform: translateY(-5px);
}

.features-style-minimal .feature-item {
    background: transparent;
    text-align: center;
}

.features-style-professional .feature-item {
    background: white;
    border-left: 4px solid #007bff;
    padding-left: 2rem;
}

.feature-list-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.feature-list-item:last-child {
    border-bottom: none;
}

@media (max-width: 768px) {
    .features-section .col-md-4,
    .features-section .col-md-3,
    .features-section .col-md-6 {
        margin-bottom: 2rem;
    }
}
</style>
@endpush