@props(['title', 'content', 'settings', 'subdomainData'])

@php
    $testimonials = $settings['testimonials'] ?? [];
    $layout = $settings['layout'] ?? 'grid';
    $columns = $settings['columns'] ?? 3;
    $style = $settings['style'] ?? 'default';
    $showAvatars = $settings['show_avatars'] ?? true;
    $showRatings = $settings['show_ratings'] ?? true;
@endphp

<section class="testimonials-section testimonials-style-{{ $style }} py-5">
    <div class="container">
        
        @if($title)
        <div class="section-title text-center mb-5">
            <h2 class="h1 font-weight-bold">{{ $title }}</h2>
            @if($content)
            <div class="section-description mt-3">
                {!! $content !!}
            </div>
            @endif
        </div>
        @endif
        
        @if(!empty($testimonials))
        <div class="testimonials-container">
            
            @if($layout === 'carousel')
            <div id="testimonialsCarousel" class="carousel slide" data-ride="carousel">
                <div class="carousel-inner">
                    @foreach($testimonials as $index => $testimonial)
                    <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                @include('components.content-blocks.partials.testimonial-item', [
                                    'testimonial' => $testimonial,
                                    'showAvatar' => $showAvatars,
                                    'showRating' => $showRatings,
                                    'style' => $style,
                                    'layout' => 'centered'
                                ])
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                
                @if(count($testimonials) > 1)
                <div class="carousel-controls">
                    <a class="carousel-control-prev" href="#testimonialsCarousel" role="button" data-slide="prev">
                        <i class="fas fa-chevron-left" aria-hidden="true"></i>
                    </a>
                    <a class="carousel-control-next" href="#testimonialsCarousel" role="button" data-slide="next">
                        <i class="fas fa-chevron-right" aria-hidden="true"></i>
                    </a>
                </div>
                
                <ol class="carousel-indicators">
                    @foreach($testimonials as $index => $testimonial)
                    <li data-target="#testimonialsCarousel" 
                        data-slide-to="{{ $index }}" 
                        class="{{ $index === 0 ? 'active' : '' }}"></li>
                    @endforeach
                </ol>
                @endif
            </div>
            
            @else
            <div class="row">
                @foreach($testimonials as $index => $testimonial)
                <div class="col-md-{{ 12 / $columns }} mb-4">
                    @include('components.content-blocks.partials.testimonial-item', [
                        'testimonial' => $testimonial,
                        'showAvatar' => $showAvatars,
                        'showRating' => $showRatings,
                        'style' => $style,
                        'index' => $index
                    ])
                </div>
                @endforeach
            </div>
            @endif
            
        </div>
        @endif
        
        @if(isset($settings['trust_badges']) && is_array($settings['trust_badges']))
        <div class="trust-badges text-center mt-5">
            <div class="row justify-content-center">
                @foreach($settings['trust_badges'] as $badge)
                <div class="col-auto">
                    <div class="trust-badge">
                        @if(isset($badge['image']))
                        <img src="{{ $badge['image'] }}" alt="{{ $badge['alt'] ?? '' }}" class="img-fluid">
                        @endif
                        @if(isset($badge['text']))
                        <div class="badge-text">{{ $badge['text'] }}</div>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
        
        @if(isset($settings['cta']))
        <div class="testimonials-cta text-center mt-5">
            <div class="cta-content">
                @if(isset($settings['cta']['title']))
                <h3>{{ $settings['cta']['title'] }}</h3>
                @endif
                @if(isset($settings['cta']['description']))
                <p class="text-muted">{{ $settings['cta']['description'] }}</p>
                @endif
                <a href="{{ $settings['cta']['url'] ?? '#' }}" class="btn btn-primary btn-lg">
                    {{ $settings['cta']['text'] ?? 'Share Your Story' }}
                </a>
            </div>
        </div>
        @endif
        
    </div>
</section>

@push('styles')
<style>
.testimonials-section {
    background-color: #f8f9fa;
}

.testimonials-style-minimal {
    background-color: white;
}

.testimonials-style-dark {
    background-color: #2c3e50;
    color: white;
}

.testimonials-style-dark .section-title h2 {
    color: white;
}

.carousel-control-prev,
.carousel-control-next {
    width: 50px;
    height: 50px;
    background-color: rgba(0,0,0,0.1);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
}

.carousel-control-prev {
    left: -25px;
}

.carousel-control-next {
    right: -25px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background-color: rgba(0,0,0,0.2);
}

.carousel-indicators {
    bottom: -50px;
}

.carousel-indicators li {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ccc;
    border: none;
    margin: 0 5px;
}

.carousel-indicators .active {
    background-color: #007bff;
}

.trust-badges {
    padding: 2rem 0;
    border-top: 1px solid #e9ecef;
}

.trust-badge {
    padding: 1rem;
    text-align: center;
}

.trust-badge img {
    max-height: 60px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.trust-badge:hover img {
    opacity: 1;
}

.badge-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.testimonials-cta {
    background: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .carousel-control-prev,
    .carousel-control-next {
        display: none;
    }
    
    .carousel-indicators {
        bottom: -30px;
    }
    
    .testimonials-cta {
        padding: 2rem 1rem;
    }
    
    .trust-badges .col-auto {
        margin-bottom: 1rem;
    }
}
</style>
@endpush