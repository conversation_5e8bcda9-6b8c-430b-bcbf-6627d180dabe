@props(['title', 'content', 'settings', 'subdomainData'])

@php
    $ctaText = $settings['cta_text'] ?? 'Get Started';
    $ctaUrl = $settings['cta_url'] ?? '#';
    $backgroundImage = $settings['background_image'] ?? '';
    $style = $settings['style'] ?? 'default';
    $textAlign = $settings['text_align'] ?? 'center';
    $overlayOpacity = $settings['overlay_opacity'] ?? '0.5';
@endphp

<section class="hero-section hero-style-{{ $style }}" 
         @if($backgroundImage)
         style="background-image: url('{{ $backgroundImage }}');"
         @endif>
    
    @if($backgroundImage)
    <div class="hero-overlay" style="opacity: {{ $overlayOpacity }};"></div>
    @endif
    
    <div class="container">
        <div class="hero-content text-{{ $textAlign }}">
            
            @if($title)
            <div class="hero-title">
                <h1 class="display-4 font-weight-bold text-white mb-4">{{ $title }}</h1>
            </div>
            @endif
            
            @if($content)
            <div class="hero-content-body">
                {!! $content !!}
            </div>
            @endif
            
            @if($ctaText && $ctaUrl !== '#')
            <div class="hero-cta mt-4">
                <a href="{{ $ctaUrl }}" class="btn btn-primary btn-lg cta-button">
                    {{ $ctaText }}
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
            @endif
            
            @if(isset($settings['features']) && is_array($settings['features']))
            <div class="hero-features mt-5">
                <div class="row">
                    @foreach($settings['features'] as $feature)
                    <div class="col-md-4 mb-3">
                        <div class="feature-item text-white">
                            @if(isset($feature['icon']))
                            <i class="fas {{ $feature['icon'] }} fa-2x mb-2"></i>
                            @endif
                            <h5>{{ $feature['title'] ?? '' }}</h5>
                            <p>{{ $feature['description'] ?? '' }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
            
        </div>
    </div>
</section>

@push('styles')
<style>
.hero-section {
    padding: 100px 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    min-height: 500px;
    display: flex;
    align-items: center;
}

.hero-section:not([style*="background-image"]) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-style-professional {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.hero-style-executive {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.hero-style-friendly {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.hero-style-modern {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.cta-button {
    font-size: 1.2rem;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
    text-decoration: none;
}

.feature-item {
    text-align: center;
    padding: 20px;
}

.feature-item i {
    opacity: 0.9;
}

@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
        min-height: 400px;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .cta-button {
        font-size: 1rem;
        padding: 10px 25px;
    }
}
</style>
@endpush