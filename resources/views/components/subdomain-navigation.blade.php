@if(config('current_subdomain'))
    @php
        $subdomain = config('current_subdomain');
        
        // Get subdomain-specific header links (already filtered by middleware)
        $subdomainHeaderLinks = $header_links ?? collect();
        
        $navConfig = [
            'income' => [
                'icon' => '💰',
                'color' => 'text-green-600',
                'bg_color' => 'bg-green-50'
            ],
            'taxbenefits' => [
                'icon' => '📊', 
                'color' => 'text-blue-600',
                'bg_color' => 'bg-blue-50'
            ],
            'fhb' => [
                'icon' => '🏠',
                'color' => 'text-purple-600', 
                'bg_color' => 'bg-purple-50'
            ],
            'projects' => [
                'icon' => '🏗️',
                'color' => 'text-orange-600',
                'bg_color' => 'bg-orange-50'
            ]
        ];
        
        $config = $navConfig[$subdomain->subdomain] ?? null;
    @endphp

    @if($config && $subdomainHeaderLinks->isNotEmpty())
    <!-- Subdomain Specialist Navigation -->
    <div class="subdomain-nav {{ $config['bg_color'] }} border-b border-gray-200">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <!-- Subdomain Brand -->
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">{{ $config['icon'] }}</span>
                    <div>
                        <div class="font-semibold {{ $config['color'] }} text-lg">
                            {{ ucfirst($subdomain->subdomain) }} Specialist
                        </div>
                        <div class="text-sm text-gray-600">
                            {{ $subdomain->description }}
                        </div>
                    </div>
                </div>
                
                <!-- Quick Navigation -->
                <nav class="hidden md:flex space-x-6">
                    @foreach($subdomainHeaderLinks as $link)
                        <a href="{{ route('cms_page', $link->slug) }}" 
                           class="text-gray-700 hover:{{ $config['color'] }} transition-colors text-sm font-medium">
                            {{ $link->name }}
                        </a>
                    @endforeach
                </nav>
                
                <!-- Mobile Menu Toggle (if needed) -->
                <button class="md:hidden {{ $config['color'] }}" onclick="toggleSubdomainNav()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Mobile Navigation -->
            <nav id="subdomain-mobile-nav" class="md:hidden mt-4 hidden">
                <div class="space-y-2">
                    @foreach($subdomainHeaderLinks as $link)
                        <a href="{{ route('cms_page', $link->slug) }}" 
                           class="block text-gray-700 hover:{{ $config['color'] }} transition-colors text-sm font-medium py-2">
                            {{ $link->name }}
                        </a>
                    @endforeach
                </div>
            </nav>
        </div>
    </div>
    
    <script>
        function toggleSubdomainNav() {
            const nav = document.getElementById('subdomain-mobile-nav');
            nav.classList.toggle('hidden');
        }
    </script>
    @endif
@endif