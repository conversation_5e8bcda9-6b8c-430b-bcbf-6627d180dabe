@props(['subdomainData' => null])

@php
    use App\Services\DynamicThemeService;
    
    $subdomain = request()->getHost();
    $subdomain = explode('.', $subdomain)[0];
    
    // Get theme configuration
    $themeConfig = DynamicThemeService::getThemeConfiguration($subdomain, $subdomainData);
@endphp

{{-- External CSS if configured --}}
@if($themeConfig['external_css'])
<link rel="stylesheet" href="{{ $themeConfig['external_css'] }}" crossorigin="anonymous">
@endif

{{-- Dynamic Theme Styles --}}
<style type="text/css">
{!! $themeConfig['custom_css'] !!}
</style>

{{-- Additional responsive utilities --}}
<style type="text/css">
/* Dynamic Responsive Utilities */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .hero-section {
        padding: 60px 0;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .features-section .col-md-4,
    .features-section .col-md-3,
    .features-section .col-md-6 {
        margin-bottom: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 1.75rem;
    }
    
    .section-title h2 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .btn + .btn {
        margin-left: 0;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .cta-section,
    .calculator-section,
    .tools-section .tool-actions {
        display: none !important;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    .hero-section {
        background: none !important;
        color: #000 !important;
        padding: 2rem 0;
    }
    
    .card {
        border: 1px solid #ccc;
        box-shadow: none;
        page-break-inside: avoid;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn-primary {
        background-color: #000;
        border-color: #000;
        color: #fff;
    }
    
    .btn-primary:hover {
        background-color: #333;
        border-color: #333;
    }
    
    .card {
        border: 2px solid #000;
    }
    
    .form-control {
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .cta-urgency {
        animation: none !important;
    }
    
    .tool-item:hover,
    .feature-item:hover,
    .testimonial-item:hover {
        transform: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --bg-alt-color: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --text-muted: #999999;
        --border-color: #444444;
        --border-light-color: #555555;
    }
    
    .card {
        background-color: var(--bg-alt-color);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .features-section,
    .tools-section,
    .testimonials-section {
        background-color: var(--bg-alt-color);
    }
    
    .form-control {
        background-color: var(--bg-alt-color);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .form-control:focus {
        background-color: var(--bg-alt-color);
        color: var(--text-primary);
    }
}

/* Focus Visible Support for Better Accessibility */
.btn:focus-visible,
.form-control:focus-visible,
.nav-link:focus-visible {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
}

/* Skip to Content Link for Screen Readers */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--bs-primary);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-to-content:focus {
    top: 6px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid var(--bs-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Validation Styles */
.form-control.is-invalid {
    border-color: var(--bs-danger);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: var(--bs-success);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.invalid-feedback {
    color: var(--bs-danger);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.valid-feedback {
    color: var(--bs-success);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Content Block Animations */
[data-aos="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

[data-aos="fade-up"].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-alt-color);
}

::-webkit-scrollbar-thumb {
    background: var(--bs-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}
</style>

{{-- Theme-specific JavaScript if needed --}}
@if(isset($themeConfig['components']['interactive']) && $themeConfig['components']['interactive'])
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS (Animate On Scroll) if available
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });
    }
    
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Initialize popovers if Bootstrap is available
    if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
    
    // Add loading states to forms
    document.querySelectorAll('form').forEach(function(form) {
        form.addEventListener('submit', function() {
            var submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.classList.add('loading');
                submitBtn.textContent = 'Loading...';
            }
        });
    });
    
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(function(anchor) {
        anchor.addEventListener('click', function (e) {
            var target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>
@endif