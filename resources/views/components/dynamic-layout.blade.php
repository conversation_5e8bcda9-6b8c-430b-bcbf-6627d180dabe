@props(['page' => 'home', 'title' => null])

@php
    // Get subdomain data from view shared variables
    $subdomainData = $subdomain_data ?? null;
    $subdomainSeo = $subdomain_seo ?? null;
    $subdomainContentBlocks = $subdomain_content_blocks ?? [];
    $subdomainNavigation = $subdomain_navigation ?? [];
    $currentPage = $current_page ?? $page;
@endphp

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- Dynamic SEO Meta Tags --}}
    <x-dynamic-seo-tags :subdomain-data="$subdomainData" :page="$currentPage" />

    {{-- Dynamic Theme Styles --}}
    <x-dynamic-theme-styles :subdomain-data="$subdomainData" />

    {{-- Bootstrap CSS --}}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">
    
    {{-- Font Awesome --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous">
    
    {{-- AOS Animation Library --}}
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    {{-- Custom Fonts (if configured) --}}
    @if(isset($subdomainData['styling']['google_fonts']))
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family={{ $subdomainData['styling']['google_fonts'] }}&display=swap" rel="stylesheet">
    @endif

    {{-- Page-specific styles --}}
    @stack('styles')
</head>

<body class="{{ $subdomainData['styling']['body_class'] ?? 'theme-default' }}">
    {{-- Skip to content link for accessibility --}}
    <a class="skip-to-content" href="#main-content">Skip to main content</a>

    {{-- Dynamic Navigation --}}
    <x-dynamic-navigation :subdomain-data="$subdomainData" :navigation="$subdomainNavigation" :current-page="$currentPage" />

    {{-- Main Content Area --}}
    <main id="main-content" role="main">
        @if($currentPage === 'home')
            {{-- Home page: Render dynamic content blocks --}}
            @if(!empty($subdomainContentBlocks))
                @foreach($subdomainContentBlocks as $block)
                    <x-dynamic-content-block :block="$block" :subdomain-data="$subdomainData" />
                @endforeach
            @else
                {{-- Fallback content if no blocks configured --}}
                <x-fallback-home-content :subdomain-data="$subdomainData" />
            @endif
        @else
            {{-- Other pages: Use slot content --}}
            {{ $slot }}
        @endif
    </main>

    {{-- Dynamic Footer --}}
    <x-dynamic-footer :subdomain-data="$subdomainData" />

    {{-- Bootstrap JavaScript --}}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    
    {{-- AOS Animation Library --}}
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    {{-- jQuery (if needed) --}}
    <script src="https://code.jquery.com/jquery-3.7.0.min.js" crossorigin="anonymous"></script>

    {{-- Custom Scripts (if configured) --}}
    @if(isset($subdomainData['integrations']['custom_scripts']) && is_array($subdomainData['integrations']['custom_scripts']))
        @foreach($subdomainData['integrations']['custom_scripts'] as $script)
            @if(isset($script['type']) && $script['type'] === 'inline')
                <script>
                    {!! $script['content'] !!}
                </script>
            @elseif(isset($script['src']))
                <script src="{{ $script['src'] }}" 
                        @if(isset($script['async']) && $script['async']) async @endif
                        @if(isset($script['defer']) && $script['defer']) defer @endif
                        crossorigin="anonymous"></script>
            @endif
        @endforeach
    @endif

    {{-- Page-specific scripts --}}
    @stack('scripts')

    {{-- Live Chat Widget (if configured) --}}
    @if(isset($subdomainData['integrations']['live_chat']['enabled']) && $subdomainData['integrations']['live_chat']['enabled'])
        @if($subdomainData['integrations']['live_chat']['provider'] === 'intercom')
            <script>
                window.intercomSettings = {
                    app_id: "{{ $subdomainData['integrations']['live_chat']['app_id'] }}"
                };
                (function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',w.intercomSettings);}else{var d=document;var i=function(){i.c(arguments);};i.q=[];i.c=function(args){i.q.push(args);};w.Intercom=i;var l=function(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/'+window.intercomSettings.app_id;var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s,x);};if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})();
            </script>
        @elseif($subdomainData['integrations']['live_chat']['provider'] === 'zendesk')
            <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key={{ $subdomainData['integrations']['live_chat']['key'] }}"></script>
        @elseif($subdomainData['integrations']['live_chat']['provider'] === 'tawk')
            <script type="text/javascript">
                var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
                (function(){
                    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
                    s1.async=true;
                    s1.src='https://embed.tawk.to/{{ $subdomainData['integrations']['live_chat']['property_id'] }}/{{ $subdomainData['integrations']['live_chat']['widget_id'] }}';
                    s1.charset='UTF-8';
                    s1.setAttribute('crossorigin','*');
                    s0.parentNode.insertBefore(s1,s0);
                })();
            </script>
        @endif
    @endif

    {{-- Cookie Consent (if configured) --}}
    @if(isset($subdomainData['legal']['cookie_consent']['enabled']) && $subdomainData['legal']['cookie_consent']['enabled'])
    <div id="cookieConsent" class="cookie-consent" style="display: none;">
        <div class="cookie-consent-content">
            <p>{{ $subdomainData['legal']['cookie_consent']['message'] ?? 'We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.' }}</p>
            <div class="cookie-consent-actions">
                <button type="button" class="btn btn-primary btn-sm" onclick="acceptCookies()">
                    {{ $subdomainData['legal']['cookie_consent']['accept_text'] ?? 'Accept' }}
                </button>
                @if(isset($subdomainData['legal']['cookie_consent']['show_decline']) && $subdomainData['legal']['cookie_consent']['show_decline'])
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="declineCookies()">
                    {{ $subdomainData['legal']['cookie_consent']['decline_text'] ?? 'Decline' }}
                </button>
                @endif
                @if(isset($subdomainData['legal']['cookie_consent']['privacy_policy_url']))
                <a href="{{ $subdomainData['legal']['cookie_consent']['privacy_policy_url'] }}" class="btn btn-link btn-sm" target="_blank">
                    Privacy Policy
                </a>
                @endif
            </div>
        </div>
    </div>

    <script>
        // Cookie consent functionality
        function checkCookieConsent() {
            if (!localStorage.getItem('cookieConsent')) {
                document.getElementById('cookieConsent').style.display = 'block';
            }
        }

        function acceptCookies() {
            localStorage.setItem('cookieConsent', 'accepted');
            document.getElementById('cookieConsent').style.display = 'none';
            
            // Enable analytics if configured
            @if(isset($subdomainData['analytics']['google_analytics_id']))
            gtag('consent', 'update', {
                'analytics_storage': 'granted'
            });
            @endif
        }

        function declineCookies() {
            localStorage.setItem('cookieConsent', 'declined');
            document.getElementById('cookieConsent').style.display = 'none';
        }

        // Check cookie consent on page load
        document.addEventListener('DOMContentLoaded', checkCookieConsent);
    </script>

    <style>
        .cookie-consent {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 1rem;
            z-index: 10000;
        }

        .cookie-consent-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .cookie-consent p {
            margin: 0;
            flex: 1;
            min-width: 300px;
        }

        .cookie-consent-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .cookie-consent-content {
                flex-direction: column;
                text-align: center;
            }

            .cookie-consent-actions {
                justify-content: center;
            }
        }
    </style>
    @endif

    {{-- Error Reporting (only in development) --}}
    @if(config('app.debug'))
    <script>
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                error: e.error
            });
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled Promise Rejection:', e.reason);
        });
    </script>
    @endif
</body>
</html>