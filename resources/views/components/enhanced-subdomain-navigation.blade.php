@if(config('current_subdomain'))
    @php
        $subdomain = config('current_subdomain');
        
        // Get subdomain-specific header links (already filtered by middleware)
        $subdomainHeaderLinks = $header_links ?? collect();
        
        $navConfig = [
            'income' => [
                'icon' => '💰',
                'color' => 'text-green-600',
                'bg_color' => 'bg-green-50',
                'border_color' => 'border-green-200',
                'hover_color' => 'hover:text-green-700',
                'badge_bg' => 'bg-green-600'
            ],
            'taxbenefits' => [
                'icon' => '📊', 
                'color' => 'text-blue-600',
                'bg_color' => 'bg-blue-50',
                'border_color' => 'border-blue-200',
                'hover_color' => 'hover:text-blue-700',
                'badge_bg' => 'bg-blue-600'
            ],
            'fhb' => [
                'icon' => '🏠',
                'color' => 'text-purple-600', 
                'bg_color' => 'bg-purple-50',
                'border_color' => 'border-purple-200',
                'hover_color' => 'hover:text-purple-700',
                'badge_bg' => 'bg-purple-600'
            ],
            'projects' => [
                'icon' => '🏗️',
                'color' => 'text-orange-600',
                'bg_color' => 'bg-orange-50',
                'border_color' => 'border-orange-200',
                'hover_color' => 'hover:text-orange-700',
                'badge_bg' => 'bg-orange-600'
            ]
        ];
        
        $config = $navConfig[$subdomain->subdomain] ?? null;
    @endphp

    @if($config && $subdomainHeaderLinks->isNotEmpty())
    <!-- Enhanced Subdomain Specialist Navigation -->
    <div class="enhanced-subdomain-nav {{ $config['bg_color'] }} {{ $config['border_color'] }} border-b shadow-sm">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <!-- Subdomain Brand Section -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 {{ $config['badge_bg'] }} rounded-full flex items-center justify-center text-white text-xl">
                            {{ $config['icon'] }}
                        </div>
                        <div>
                            <div class="font-bold {{ $config['color'] }} text-lg">
                                {{ ucfirst($subdomain->subdomain) }} Specialist
                            </div>
                            <div class="text-sm text-gray-600 max-w-md">
                                {{ $subdomain->description }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Live Indicator -->
                    <div class="hidden lg:flex items-center space-x-2 ml-6">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-xs text-gray-500 font-medium">LIVE SPECIALIST</span>
                    </div>
                </div>
                
                <!-- Quick Navigation Links -->
                <nav class="hidden lg:flex items-center space-x-8">
                    @foreach($subdomainHeaderLinks as $link)
                        <a href="{{ route('cms_page', $link->slug) }}" 
                           class="relative group text-gray-700 {{ $config['hover_color'] }} transition-all duration-200 text-sm font-medium py-2 px-1">
                            {{ $link->name }}
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 {{ $config['badge_bg'] }} transition-all duration-200 group-hover:w-full"></span>
                        </a>
                    @endforeach
                    
                    <!-- View Properties Link -->
                    <a href="{{ route('property.listing') }}" 
                       class="{{ $config['badge_bg'] }} text-white px-4 py-2 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity">
                        View Properties
                    </a>
                </nav>
                
                <!-- Mobile Menu Button -->
                <button class="lg:hidden {{ $config['color'] }} p-2" onclick="toggleEnhancedSubdomainNav()">
                    <svg id="mobile-menu-icon" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <svg id="mobile-close-icon" class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Mobile Navigation Menu -->
            <div id="enhanced-subdomain-mobile-nav" class="lg:hidden mt-4 hidden">
                <div class="bg-white rounded-lg shadow-lg p-4 space-y-3">
                    <div class="border-b pb-3 mb-3">
                        <div class="text-xs text-gray-500 uppercase tracking-wide font-semibold mb-2">Specialist Resources</div>
                    </div>
                    
                    @foreach($subdomainHeaderLinks as $link)
                        <a href="{{ route('cms_page', $link->slug) }}" 
                           class="flex items-center space-x-3 text-gray-700 {{ $config['hover_color'] }} transition-colors text-sm font-medium py-2 px-3 rounded-lg hover:{{ $config['bg_color'] }}">
                            <span class="w-2 h-2 {{ $config['badge_bg'] }} rounded-full"></span>
                            <span>{{ $link->name }}</span>
                        </a>
                    @endforeach
                    
                    <div class="border-t pt-3 mt-3">
                        <a href="{{ route('property.listing') }}" 
                           class="{{ $config['badge_bg'] }} text-white px-4 py-3 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity block text-center">
                            View All Properties
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Progress Bar for Visual Interest -->
        <div class="h-1 {{ $config['badge_bg'] }} opacity-20"></div>
    </div>
    
    <script>
        function toggleEnhancedSubdomainNav() {
            const nav = document.getElementById('enhanced-subdomain-mobile-nav');
            const menuIcon = document.getElementById('mobile-menu-icon');
            const closeIcon = document.getElementById('mobile-close-icon');
            
            nav.classList.toggle('hidden');
            menuIcon.classList.toggle('hidden');
            closeIcon.classList.toggle('hidden');
        }
        
        // Close mobile nav when clicking outside
        document.addEventListener('click', function(event) {
            const nav = document.getElementById('enhanced-subdomain-mobile-nav');
            const button = event.target.closest('button[onclick="toggleEnhancedSubdomainNav()"]');
            
            if (!nav.contains(event.target) && !button && !nav.classList.contains('hidden')) {
                toggleEnhancedSubdomainNav();
            }
        });
    </script>
    @endif
@endif