@props(['block', 'subdomainData' => null])

@php
    $blockType = $block['block_type'] ?? 'default';
    $blockKey = $block['block_key'] ?? '';
    $title = $block['title'] ?? '';
    $content = $block['content'] ?? '';
    $settings = $block['settings'] ?? [];
    $displayOrder = $block['display_order'] ?? 0;
@endphp

<div class="dynamic-content-block block-{{ $blockType }} block-key-{{ $blockKey }}" 
     data-block-type="{{ $blockType }}" 
     data-block-key="{{ $blockKey }}" 
     data-order="{{ $displayOrder }}">

    @switch($blockType)
        @case('hero')
            @include('components.content-blocks.hero', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('features')
            @include('components.content-blocks.features', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('cta')
            @include('components.content-blocks.cta', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('calculator')
            @include('components.content-blocks.calculator', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('tools')
            @include('components.content-blocks.tools', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('testimonials')
            @include('components.content-blocks.testimonials', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('partners')
            @include('components.content-blocks.partners', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('benefits')
            @include('components.content-blocks.benefits', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('process')
            @include('components.content-blocks.process', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('faq')
            @include('components.content-blocks.faq', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @case('footer')
            @include('components.content-blocks.footer', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'subdomainData' => $subdomainData
            ])
            @break

        @default
            @include('components.content-blocks.default', [
                'title' => $title,
                'content' => $content,
                'settings' => $settings,
                'blockType' => $blockType,
                'subdomainData' => $subdomainData
            ])
    @endswitch

</div>

@push('styles')
<style>
.dynamic-content-block {
    margin-bottom: 2rem;
}

.dynamic-content-block[data-order] {
    order: {{ $displayOrder }};
}

/* Dynamic styling based on subdomain */
@if(isset($subdomainData['styling']['custom_settings']['primary_color']))
.block-{{ $blockType }} .btn-primary,
.block-{{ $blockType }} .cta-button {
    background-color: {{ $subdomainData['styling']['custom_settings']['primary_color'] }};
}
@endif

@if(isset($subdomainData['styling']['custom_settings']['secondary_color']))
.block-{{ $blockType }} .btn-secondary,
.block-{{ $blockType }} .link-color {
    color: {{ $subdomainData['styling']['custom_settings']['secondary_color'] }};
}
@endif
</style>
@endpush