@if(config('current_subdomain'))
    @php
        $subdomain = config('current_subdomain');
        $heroConfig = [
            'income' => [
                'title' => 'Positive Cashflow Properties',
                'subtitle' => 'Build wealth through SMSF-compliant investments that pay you weekly',
                'description' => 'Discover dual occupancy, co-living, and regional high-yield properties designed for passive income seekers and SMSF investors.',
                'cta_text' => 'Book Strategy Call',
                'cta_link' => route('cms_page', 'book-strategy-call'),
                'bg_color' => 'bg-green-50',
                'text_color' => 'text-green-800',
                'accent_color' => 'bg-green-600'
            ],
            'taxbenefits' => [
                'title' => 'Tax-Minimizing Properties',
                'subtitle' => 'Reduce your tax burden with strategic negative gearing',
                'description' => 'Metro apartments and prestige properties with high depreciation schedules for high-income earners and professionals.',
                'cta_text' => 'Book Tax Strategy Session',
                'cta_link' => route('cms_page', 'book-tax-strategy'),
                'bg_color' => 'bg-blue-50',
                'text_color' => 'text-blue-800', 
                'accent_color' => 'bg-blue-600'
            ],
            'fhb' => [
                'title' => 'First Home Buyer Solutions',
                'subtitle' => 'Your pathway to homeownership starts here',
                'description' => 'Affordable starter homes, government grants, and low deposit options designed for entry-level buyers and young families.',
                'cta_text' => 'Book First Home Planning',
                'cta_link' => route('cms_page', 'book-fhb-planning'),
                'bg_color' => 'bg-purple-50',
                'text_color' => 'text-purple-800',
                'accent_color' => 'bg-purple-600'
            ],
            'projects' => [
                'title' => 'Custom Build & Land Solutions',
                'subtitle' => 'Design and build your dream home',
                'description' => 'Premium land lots, custom builders, and knockdown-rebuild opportunities for discerning home builders.',
                'cta_text' => 'Book Build Consultation', 
                'cta_link' => route('cms_page', 'book-build-consultation'),
                'bg_color' => 'bg-orange-50',
                'text_color' => 'text-orange-800',
                'accent_color' => 'bg-orange-600'
            ]
        ];
        
        $config = $heroConfig[$subdomain->subdomain] ?? null;
    @endphp

    @if($config)
    <section class="subdomain-hero {{ $config['bg_color'] }} py-16 px-4">
        <div class="container mx-auto max-w-6xl">
            <div class="text-center">
                <!-- Subdomain Badge -->
                <div class="inline-block {{ $config['accent_color'] }} text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
                    {{ strtoupper($subdomain->subdomain) }} SPECIALIST
                </div>
                
                <!-- Hero Title -->
                <h1 class="text-4xl md:text-5xl font-bold {{ $config['text_color'] }} mb-4">
                    {{ $config['title'] }}
                </h1>
                
                <!-- Subtitle -->
                <h2 class="text-xl md:text-2xl {{ $config['text_color'] }} opacity-80 mb-6">
                    {{ $config['subtitle'] }}
                </h2>
                
                <!-- Description -->
                <p class="text-lg {{ $config['text_color'] }} opacity-70 mb-8 max-w-3xl mx-auto">
                    {{ $config['description'] }}
                </p>
                
                <!-- CTA Button -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ $config['cta_link'] }}" 
                       class="{{ $config['accent_color'] }} text-white px-8 py-4 rounded-lg font-semibold hover:opacity-90 transition-opacity">
                        {{ $config['cta_text'] }}
                    </a>
                    <a href="{{ route('property.listing') }}" 
                       class="border-2 border-current {{ $config['text_color'] }} px-8 py-4 rounded-lg font-semibold hover:bg-current hover:text-white transition-colors">
                        View Properties
                    </a>
                </div>
                
                <!-- Key Benefits -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                    @if($subdomain->subdomain === 'income')
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">$12k+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Annual Net Income</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">SMSF</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Compliant Properties</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">Regional</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">High-Yield Focus</div>
                        </div>
                    @elseif($subdomain->subdomain === 'taxbenefits')
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">40%+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Tax Bracket Savings</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">Metro</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">High Depreciation</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">ATO</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Approved Schedules</div>
                        </div>
                    @elseif($subdomain->subdomain === 'fhb')
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">5%</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Deposit Options</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">$25k+</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Government Grants</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}"><$750k</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Affordable Range</div>
                        </div>
                    @elseif($subdomain->subdomain === 'projects')
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">300-600m²</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Premium Land Lots</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">Custom</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Builder Network</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $config['text_color'] }}">Council</div>
                            <div class="text-sm {{ $config['text_color'] }} opacity-70">Approved Blocks</div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
    @endif
@endif