@props(['subdomainData' => null, 'page' => null])

@php
    use App\Services\DynamicSeoService;
    
    $subdomain = request()->getHost();
    $subdomain = explode('.', $subdomain)[0];
    
    // Get SEO data
    $seoData = DynamicSeoService::getSubdomainSeoData($subdomain, $subdomainData, $page);
@endphp

{{-- Basic Meta Tags --}}
<title>{{ $seoData['title'] }}</title>
<meta name="description" content="{{ $seoData['description'] }}">
<meta name="keywords" content="{{ $seoData['keywords'] }}">
<meta name="robots" content="{{ $seoData['meta_robots'] }}">
<meta name="author" content="{{ $subdomainData['name'] ?? 'Seamless Property Solutions' }}">

{{-- Canonical URL --}}
<link rel="canonical" href="{{ $seoData['canonical_url'] }}">

{{-- Open Graph Meta Tags --}}
@foreach($seoData['og_tags'] as $property => $content)
<meta property="{{ $property }}" content="{{ $content }}">
@endforeach

{{-- Twitter Card Meta Tags --}}
@foreach($seoData['twitter_tags'] as $name => $content)
<meta name="{{ $name }}" content="{{ $content }}">
@endforeach

{{-- Hreflang Tags --}}
@foreach($seoData['hreflang'] as $lang => $url)
<link rel="alternate" hreflang="{{ $lang }}" href="{{ $url }}">
@endforeach

{{-- Schema.org Structured Data --}}
<script type="application/ld+json">
{!! json_encode($seoData['schema_markup'], JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
</script>

{{-- Additional SEO Meta Tags --}}
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="format-detection" content="telephone=no">

{{-- Favicon and Touch Icons (Dynamic based on subdomain if available) --}}
@if(isset($subdomainData['branding']['favicon']))
<link rel="icon" type="image/x-icon" href="{{ $subdomainData['branding']['favicon'] }}">
<link rel="apple-touch-icon" href="{{ $subdomainData['branding']['favicon'] }}">
@else
<link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
<link rel="apple-touch-icon" href="{{ asset('images/apple-touch-icon.png') }}">
@endif

{{-- Theme Color (Dynamic based on subdomain styling) --}}
@if(isset($subdomainData['styling']['custom_settings']['primary_color']))
<meta name="theme-color" content="{{ $subdomainData['styling']['custom_settings']['primary_color'] }}">
<meta name="msapplication-TileColor" content="{{ $subdomainData['styling']['custom_settings']['primary_color'] }}">
@else
<meta name="theme-color" content="#007bff">
<meta name="msapplication-TileColor" content="#007bff">
@endif

{{-- Additional Meta for PWA Support --}}
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="apple-mobile-web-app-title" content="{{ $subdomainData['name'] ?? 'Seamless Property Solutions' }}">

{{-- Verification Tags (if configured) --}}
@if(isset($subdomainData['seo']['google_verification']))
<meta name="google-site-verification" content="{{ $subdomainData['seo']['google_verification'] }}">
@endif

@if(isset($subdomainData['seo']['bing_verification']))
<meta name="msvalidate.01" content="{{ $subdomainData['seo']['bing_verification'] }}">
@endif

@if(isset($subdomainData['seo']['facebook_verification']))
<meta name="facebook-domain-verification" content="{{ $subdomainData['seo']['facebook_verification'] }}">
@endif

{{-- Preconnect for Performance --}}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="preconnect" href="https://cdnjs.cloudflare.com">

{{-- DNS Prefetch for External Resources --}}
<link rel="dns-prefetch" href="//www.google-analytics.com">
<link rel="dns-prefetch" href="//www.googletagmanager.com">
<link rel="dns-prefetch" href="//fonts.googleapis.com">

{{-- RSS Feed (if configured) --}}
@if(isset($subdomainData['content']['blog_enabled']) && $subdomainData['content']['blog_enabled'])
<link rel="alternate" type="application/rss+xml" title="{{ $subdomainData['name'] ?? 'Seamless Property Solutions' }} Blog" href="{{ url('/feed') }}">
@endif

{{-- Sitemap --}}
<link rel="sitemap" type="application/xml" title="Sitemap" href="{{ url('/sitemap.xml') }}">

{{-- Additional Custom Meta Tags (if configured) --}}
@if(isset($subdomainData['seo']['custom_meta_tags']) && is_array($subdomainData['seo']['custom_meta_tags']))
@foreach($subdomainData['seo']['custom_meta_tags'] as $tag)
@if(isset($tag['type']) && $tag['type'] === 'meta')
<meta name="{{ $tag['name'] }}" content="{{ $tag['content'] }}">
@elseif(isset($tag['type']) && $tag['type'] === 'property')
<meta property="{{ $tag['property'] }}" content="{{ $tag['content'] }}">
@elseif(isset($tag['type']) && $tag['type'] === 'link')
<link rel="{{ $tag['rel'] }}" href="{{ $tag['href'] }}" @if(isset($tag['type'])) type="{{ $tag['type'] }}" @endif>
@endif
@endforeach
@endif

{{-- Analytics and Tracking (if configured) --}}
@if(isset($subdomainData['analytics']['google_analytics_id']))
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id={{ $subdomainData['analytics']['google_analytics_id'] }}"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', '{{ $subdomainData['analytics']['google_analytics_id'] }}');
</script>
@endif

@if(isset($subdomainData['analytics']['facebook_pixel_id']))
<!-- Facebook Pixel -->
<script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '{{ $subdomainData['analytics']['facebook_pixel_id'] }}');
    fbq('track', 'PageView');
</script>
<noscript>
    <img height="1" width="1" style="display:none" 
         src="https://www.facebook.com/tr?id={{ $subdomainData['analytics']['facebook_pixel_id'] }}&ev=PageView&noscript=1"/>
</noscript>
@endif

@if(isset($subdomainData['analytics']['hotjar_id']))
<!-- Hotjar Tracking -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:{{ $subdomainData['analytics']['hotjar_id'] }},hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>
@endif