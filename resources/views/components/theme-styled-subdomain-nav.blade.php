@if(config('current_subdomain') && isset($header_links) && $header_links->isNotEmpty())
    @php
        $subdomain = config('current_subdomain');
        
        $navConfig = [
            'income' => [
                'icon' => '💰',
                'badge_text' => 'INCOME SPECIALIST',
                'description' => 'SMSF & Positive Cashflow Properties'
            ],
            'taxbenefits' => [
                'icon' => '📊',
                'badge_text' => 'TAX SPECIALIST',
                'description' => 'Negative Gearing & Depreciation'
            ],
            'fhb' => [
                'icon' => '🏠',
                'badge_text' => 'FHB SPECIALIST',
                'description' => 'First Home Buyer Pathways'
            ],
            'projects' => [
                'icon' => '🏗️',
                'badge_text' => 'BUILD SPECIALIST',
                'description' => 'Custom Homes & Land'
            ]
        ];
        
        $config = $navConfig[$subdomain->subdomain] ?? null;
    @endphp

    @if($config)
    <!-- Subdomain Specialist Bar Start -->
    <div class="specialist-bar subdomain-{{ $subdomain->subdomain }}">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="specialist-info">
                        <div class="specialist-badge">
                            <span class="specialist-icon">{{ $config['icon'] }}</span>
                            <div class="specialist-text">
                                <strong>{{ $config['badge_text'] }}</strong>
                                <span>{{ $config['description'] }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="specialist-nav">
                        <nav class="specialist-menu">
                            @foreach($header_links as $link)
                                <a href="{{ route('cms_page', $link->slug) }}" class="specialist-link">
                                    {{ $link->name }}
                                </a>
                            @endforeach
                            <a href="{{ route('property.listing') }}" class="btn-specialist">
                                View Properties
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Subdomain Specialist Bar End -->

    <style>
    .specialist-bar {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 15px 0;
        position: relative;
        z-index: 999;
    }
    
    .specialist-bar.subdomain-income {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-bottom-color: #bbf7d0;
    }
    
    .specialist-bar.subdomain-taxbenefits {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        border-bottom-color: #93c5fd;
    }
    
    .specialist-bar.subdomain-fhb {
        background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
        border-bottom-color: #c4b5fd;
    }
    
    .specialist-bar.subdomain-projects {
        background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
        border-bottom-color: #fdba74;
    }
    
    .specialist-info {
        display: flex;
        align-items: center;
    }
    
    .specialist-badge {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .specialist-icon {
        font-size: 24px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .specialist-text strong {
        display: block;
        font-size: 14px;
        font-weight: 700;
        color: #374151;
        margin-bottom: 2px;
    }
    
    .specialist-text span {
        font-size: 12px;
        color: #6b7280;
    }
    
    .specialist-nav {
        text-align: right;
    }
    
    .specialist-menu {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 20px;
        flex-wrap: wrap;
    }
    
    .specialist-link {
        font-size: 14px;
        color: #374151;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
        white-space: nowrap;
    }
    
    .specialist-link:hover {
        color: #059669;
        text-decoration: none;
    }
    
    .subdomain-taxbenefits .specialist-link:hover {
        color: #2563eb;
    }
    
    .subdomain-fhb .specialist-link:hover {
        color: #7c3aed;
    }
    
    .subdomain-projects .specialist-link:hover {
        color: #ea580c;
    }
    
    .btn-specialist {
        background: #059669;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        white-space: nowrap;
    }
    
    .btn-specialist:hover {
        background: #047857;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    .subdomain-taxbenefits .btn-specialist {
        background: #2563eb;
    }
    
    .subdomain-taxbenefits .btn-specialist:hover {
        background: #1d4ed8;
    }
    
    .subdomain-fhb .btn-specialist {
        background: #7c3aed;
    }
    
    .subdomain-fhb .btn-specialist:hover {
        background: #6d28d9;
    }
    
    .subdomain-projects .btn-specialist {
        background: #ea580c;
    }
    
    .subdomain-projects .btn-specialist:hover {
        background: #dc2626;
    }
    
    @media (max-width: 991px) {
        .specialist-nav {
            text-align: left;
            margin-top: 15px;
        }
        
        .specialist-menu {
            justify-content: flex-start;
            gap: 15px;
        }
        
        .specialist-link {
            font-size: 13px;
        }
        
        .btn-specialist {
            padding: 6px 12px;
            font-size: 13px;
        }
    }
    
    @media (max-width: 576px) {
        .specialist-menu {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .btn-specialist {
            align-self: stretch;
            text-align: center;
        }
    }
    </style>
    @endif
@endif