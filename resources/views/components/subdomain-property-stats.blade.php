@if(config('current_subdomain'))
    @php
        $subdomain = config('current_subdomain');
        
        $statsConfig = [
            'income' => [
                'title' => 'Positive Cashflow Opportunities',
                'stats' => [
                    ['label' => 'Average Weekly Income', 'value' => '$230+', 'icon' => '💰'],
                    ['label' => 'SMSF Compliant', 'value' => '100%', 'icon' => '✅'],
                    ['label' => 'Average Yield', 'value' => '6.8%', 'icon' => '📈'],
                    ['label' => 'Regional Focus', 'value' => '85%', 'icon' => '🌍']
                ],
                'cta' => 'Download SMSF Investment Guide',
                'cta_link' => route('cms_page', 'smsf-investment-guides'),
                'bg_color' => 'bg-green-50',
                'text_color' => 'text-green-800',
                'accent_color' => 'bg-green-600'
            ],
            'taxbenefits' => [
                'title' => 'Tax Minimization Potential',
                'stats' => [
                    ['label' => 'Tax Bracket Savings', 'value' => '37-45%', 'icon' => '📊'],
                    ['label' => 'Depreciation Value', 'value' => '$25k+', 'icon' => '💸'],
                    ['label' => 'Metro Properties', 'value' => '78%', 'icon' => '🏙️'],
                    ['label' => 'High-Spec Builds', 'value' => '92%', 'icon' => '✨']
                ],
                'cta' => 'Get Tax Benefit Analysis',
                'cta_link' => route('cms_page', 'tax-benefit-calculator'),
                'bg_color' => 'bg-blue-50',
                'text_color' => 'text-blue-800',
                'accent_color' => 'bg-blue-600'
            ],
            'fhb' => [
                'title' => 'First Home Buyer Benefits',
                'stats' => [
                    ['label' => 'Min Deposit Required', 'value' => '5%', 'icon' => '💳'],
                    ['label' => 'Government Grants', 'value' => 'Up to $25k', 'icon' => '🎁'],
                    ['label' => 'Affordable Range', 'value' => '<$750k', 'icon' => '🏠'],
                    ['label' => 'Stamp Duty Savings', 'value' => '$15k+', 'icon' => '💰']
                ],
                'cta' => 'Check Grant Eligibility',
                'cta_link' => route('cms_page', 'government-grants'),
                'bg_color' => 'bg-purple-50',
                'text_color' => 'text-purple-800',
                'accent_color' => 'bg-purple-600'
            ],
            'projects' => [
                'title' => 'Custom Build Advantages',
                'stats' => [
                    ['label' => 'Land Size Range', 'value' => '300-600m²', 'icon' => '📐'],
                    ['label' => 'Builder Network', 'value' => '15+ Partners', 'icon' => '🏗️'],
                    ['label' => 'Council Approved', 'value' => '100%', 'icon' => '✅'],
                    ['label' => 'Design Flexibility', 'value' => 'Unlimited', 'icon' => '🎨']
                ],
                'cta' => 'Start Build Planning',
                'cta_link' => route('cms_page', 'build-cost-estimator'),
                'bg_color' => 'bg-orange-50',
                'text_color' => 'text-orange-800',
                'accent_color' => 'bg-orange-600'
            ]
        ];
        
        $config = $statsConfig[$subdomain->subdomain] ?? null;
    @endphp

    @if($config)
    <section class="subdomain-stats {{ $config['bg_color'] }} py-12 px-4 my-8">
        <div class="container mx-auto max-w-6xl">
            <div class="text-center mb-8">
                <h3 class="text-2xl md:text-3xl font-bold {{ $config['text_color'] }} mb-4">
                    {{ $config['title'] }}
                </h3>
            </div>
            
            <!-- Statistics Grid -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                @foreach($config['stats'] as $stat)
                    <div class="text-center">
                        <div class="text-3xl mb-2">{{ $stat['icon'] }}</div>
                        <div class="text-2xl font-bold {{ $config['text_color'] }}">{{ $stat['value'] }}</div>
                        <div class="text-sm {{ $config['text_color'] }} opacity-70">{{ $stat['label'] }}</div>
                    </div>
                @endforeach
            </div>
            
            <!-- CTA -->
            <div class="text-center">
                <a href="{{ $config['cta_link'] }}" 
                   class="inline-block {{ $config['accent_color'] }} text-white px-8 py-4 rounded-lg font-semibold hover:opacity-90 transition-opacity">
                    {{ $config['cta'] }}
                </a>
            </div>
        </div>
    </section>
    @endif
@endif