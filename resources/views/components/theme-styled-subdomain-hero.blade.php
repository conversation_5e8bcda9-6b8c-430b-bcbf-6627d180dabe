@if(config('current_subdomain'))
    @php
        $subdomain = config('current_subdomain');
        $heroConfig = [
            'income' => [
                'welcome_text' => 'Welcome to Income Specialist',
                'main_title' => 'Build Wealth Through Positive Cashflow Properties',
                'description' => 'Discover SMSF-compliant dual occupancy, co-living, and regional high-yield properties that generate $12k+ annual net income with rental yields exceeding 6.8%.',
                'primary_cta' => 'View SMSF Properties',
                'primary_link' => route('property.listing'),
                'secondary_cta' => 'SMSF Investment Guide',
                'secondary_link' => route('cms_page', 'smsf-investment-guides'),
                'stats' => [
                    ['icon' => 'icon-how-1.svg', 'title' => '$230+ Weekly', 'desc' => 'Average Income'],
                    ['icon' => 'icon-how-2.svg', 'title' => '6.8% Yield', 'desc' => 'Target Returns'],
                    ['icon' => 'icon-how-3.svg', 'title' => '100% SMSF', 'desc' => 'Compliant'],
                    ['icon' => 'icon-how-4.svg', 'title' => '500+ Properties', 'desc' => 'Delivered']
                ]
            ],
            'taxbenefits' => [
                'welcome_text' => 'Welcome to Tax Benefits Specialist',
                'main_title' => 'Minimize Your Tax Through Strategic Property Investment',
                'description' => 'High-income earners can reduce their tax burden by up to 47% through our carefully selected metro apartments and prestige properties with aggressive depreciation schedules.',
                'primary_cta' => 'View Tax Properties',
                'primary_link' => route('property.listing'),
                'secondary_cta' => 'Tax Calculator',
                'secondary_link' => route('cms_page', 'tax-benefit-calculator'),
                'stats' => [
                    ['icon' => 'icon-how-1.svg', 'title' => '37-47%', 'desc' => 'Tax Savings'],
                    ['icon' => 'icon-how-2.svg', 'title' => '$25k+', 'desc' => 'Depreciation'],
                    ['icon' => 'icon-how-3.svg', 'title' => '78% Metro', 'desc' => 'Properties'],
                    ['icon' => 'icon-how-4.svg', 'title' => '350+ Clients', 'desc' => 'Served']
                ]
            ],
            'fhb' => [
                'welcome_text' => 'Welcome to First Home Specialist',
                'main_title' => 'Your Complete First Home Buyer Journey',
                'description' => 'Access up to $55,000 in combined government grants and concessions. Our affordable properties under $750k come with 5% deposit options and comprehensive support.',
                'primary_cta' => 'View Starter Homes',
                'primary_link' => route('property.listing'),
                'secondary_cta' => 'Grant Checker',
                'secondary_link' => route('cms_page', 'government-grants'),
                'stats' => [
                    ['icon' => 'icon-how-1.svg', 'title' => '5% Deposit', 'desc' => 'Low Options'],
                    ['icon' => 'icon-how-2.svg', 'title' => 'Up to $55k', 'desc' => 'Grants Available'],
                    ['icon' => 'icon-how-3.svg', 'title' => '<$750k', 'desc' => 'Affordable Range'],
                    ['icon' => 'icon-how-4.svg', 'title' => '1200+ Homes', 'desc' => 'Delivered']
                ]
            ],
            'projects' => [
                'welcome_text' => 'Welcome to Custom Build Specialist',
                'main_title' => 'Design and Build Your Dream Home',
                'description' => 'Access premium land lots (300-600m²) with our network of 15+ award-winning builders. From knockdown-rebuilds to custom designs, we provide complete project management.',
                'primary_cta' => 'View Land Lots',
                'primary_link' => route('property.listing'),
                'secondary_cta' => 'Build Calculator',
                'secondary_link' => route('cms_page', 'build-cost-estimator'),
                'stats' => [
                    ['icon' => 'icon-how-1.svg', 'title' => '300-600m²', 'desc' => 'Premium Lots'],
                    ['icon' => 'icon-how-2.svg', 'title' => '15+ Builders', 'desc' => 'Partners'],
                    ['icon' => 'icon-how-3.svg', 'title' => '100% Approved', 'desc' => 'Council Ready'],
                    ['icon' => 'icon-how-4.svg', 'title' => '800+ Builds', 'desc' => 'Completed']
                ]
            ]
        ];
        
        $config = $heroConfig[$subdomain->subdomain] ?? null;
    @endphp

    @if($config)
    <!-- Subdomain Hero Section Start -->
    <div class="hero">
        <div class="hero-section parallaxie subdomain-{{ $subdomain->subdomain }}">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-12">
                        <!-- Hero Content Start -->
                        <div class="hero-content">
                            <div class="section-title">
                                <h3 class="wow fadeInUp">{{ $config['welcome_text'] }}</h3>
                                <h1 class="text-anime">{{ $config['main_title'] }}</h1>
                            </div>
                            <div class="hero-content-body wow fadeInUp" data-wow-delay="0.5s">
                                <p>{{ $config['description'] }}</p>
                            </div>

                            <div class="hero-content-footer wow fadeInUp" data-wow-delay="0.75s">
                                <a href="{{ $config['primary_link'] }}" class="btn-default">{{ $config['primary_cta'] }}</a>
                                <a href="{{ $config['secondary_link'] }}" class="btn-default btn-border">{{ $config['secondary_cta'] }}</a>
                            </div>
                        </div>
                        <!-- Hero Content End -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Subdomain Hero Section End -->

    <!-- Subdomain Stats Section Start -->
    <div class="how-it-works subdomain-stats">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp">Why Choose {{ ucfirst($subdomain->subdomain) }} Specialist</h3>
                        <h2 class="text-anime">Proven Results & Expert Service</h2>
                    </div>
                    <!-- Section Title End -->
                </div>
            </div>

            <div class="row">
                @foreach($config['stats'] as $index => $stat)
                    <div class="col-lg-3 col-md-6">
                        <!-- Stat Item Start -->
                        <div class="how-it-work-item wow fadeInUp" data-wow-delay="{{ 0.25 + ($index * 0.25) }}s">
                            <div class="icon-box">
                                <img src="{{ theme_asset('images/' . $stat['icon']) }}" alt="">
                            </div>

                            <h3>{{ $stat['title'] }}</h3>
                            <p>{{ $stat['desc'] }}</p>
                        </div>
                        <!-- Stat Item End -->
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <!-- Subdomain Stats Section End -->
    @endif
@endif