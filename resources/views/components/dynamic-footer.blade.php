@props(['subdomainData' => null])

@php
    $companyName = $subdomainData['name'] ?? 'Seamless Property Solutions';
    $footerStyle = $subdomainData['styling']['footer']['style'] ?? 'comprehensive';
    $showSocial = $subdomainData['social']['show_in_footer'] ?? true;
    $showNewsletter = $subdomainData['features']['newsletter'] ?? true;
    $currentYear = date('Y');
@endphp

<footer class="footer footer-style-{{ $footerStyle }}" role="contentinfo">
    
    @if($footerStyle === 'comprehensive')
    {{-- Comprehensive Footer --}}
    <div class="footer-main">
        <div class="container">
            <div class="row g-4">
                
                {{-- Company Info Column --}}
                <div class="col-lg-4 col-md-6">
                    <div class="footer-section">
                        <h5 class="footer-title">{{ $companyName }}</h5>
                        <p class="footer-description">
                            {{ $subdomainData['description'] ?? 'Your trusted partner in property investment, helping you build wealth through strategic real estate opportunities.' }}
                        </p>
                        
                        @if(isset($subdomainData['contact']))
                        <div class="footer-contact">
                            @if(isset($subdomainData['contact']['phone']))
                            <div class="contact-item">
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:{{ $subdomainData['contact']['phone'] }}">{{ $subdomainData['contact']['phone'] }}</a>
                            </div>
                            @endif
                            
                            @if(isset($subdomainData['contact']['email']))
                            <div class="contact-item">
                                <i class="fas fa-envelope me-2"></i>
                                <a href="mailto:{{ $subdomainData['contact']['email'] }}">{{ $subdomainData['contact']['email'] }}</a>
                            </div>
                            @endif
                            
                            @if(isset($subdomainData['contact']['address']))
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <span>{{ $subdomainData['contact']['address'] }}</span>
                            </div>
                            @endif
                        </div>
                        @endif
                    </div>
                </div>

                {{-- Quick Links Column --}}
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="footer-subtitle">Quick Links</h6>
                        <ul class="footer-links">
                            <li><a href="/">Home</a></li>
                            <li><a href="/properties">Properties</a></li>
                            <li><a href="/calculator">Calculator</a></li>
                            <li><a href="/about">About Us</a></li>
                            <li><a href="/contact">Contact</a></li>
                            @if(isset($subdomainData['features']['blog_enabled']) && $subdomainData['features']['blog_enabled'])
                            <li><a href="/blog">Blog</a></li>
                            @endif
                        </ul>
                    </div>
                </div>

                {{-- Services Column --}}
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="footer-subtitle">Services</h6>
                        <ul class="footer-links">
                            @if(isset($subdomainData['services']) && is_array($subdomainData['services']))
                                @foreach($subdomainData['services'] as $service)
                                <li><a href="{{ $service['url'] ?? '#' }}">{{ $service['name'] }}</a></li>
                                @endforeach
                            @else
                                <li><a href="/investment-properties">Investment Properties</a></li>
                                <li><a href="/property-management">Property Management</a></li>
                                <li><a href="/buyer-agents">Buyer's Agents</a></li>
                                <li><a href="/market-analysis">Market Analysis</a></li>
                                <li><a href="/financing">Financing Options</a></li>
                            @endif
                        </ul>
                    </div>
                </div>

                {{-- Newsletter Column --}}
                @if($showNewsletter)
                <div class="col-lg-4 col-md-6">
                    <div class="footer-section">
                        <h6 class="footer-subtitle">Stay Updated</h6>
                        <p class="newsletter-description">
                            Get the latest property investment insights and market updates delivered to your inbox.
                        </p>
                        
                        <form class="newsletter-form" action="/newsletter/subscribe" method="POST">
                            @csrf
                            <div class="input-group">
                                <input type="email" 
                                       class="form-control" 
                                       name="email" 
                                       placeholder="Enter your email" 
                                       required
                                       aria-label="Email address for newsletter">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-paper-plane"></i>
                                    <span class="d-none d-sm-inline ms-1">Subscribe</span>
                                </button>
                            </div>
                        </form>
                        
                        @if($showSocial && isset($subdomainData['social']))
                        <div class="social-links mt-3">
                            <span class="social-label">Follow Us:</span>
                            <div class="social-icons">
                                @if(isset($subdomainData['social']['facebook']))
                                <a href="{{ $subdomainData['social']['facebook'] }}" 
                                   class="social-icon" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   aria-label="Follow us on Facebook">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                @endif
                                
                                @if(isset($subdomainData['social']['instagram']))
                                <a href="{{ $subdomainData['social']['instagram'] }}" 
                                   class="social-icon" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   aria-label="Follow us on Instagram">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                @endif
                                
                                @if(isset($subdomainData['social']['linkedin']))
                                <a href="{{ $subdomainData['social']['linkedin'] }}" 
                                   class="social-icon" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   aria-label="Connect with us on LinkedIn">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                @endif
                                
                                @if(isset($subdomainData['social']['youtube']))
                                <a href="{{ $subdomainData['social']['youtube'] }}" 
                                   class="social-icon" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   aria-label="Subscribe to our YouTube channel">
                                    <i class="fab fa-youtube"></i>
                                </a>
                                @endif
                                
                                @if(isset($subdomainData['social']['twitter']))
                                <a href="{{ $subdomainData['social']['twitter'] }}" 
                                   class="social-icon" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   aria-label="Follow us on Twitter">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
                
            </div>
        </div>
    </div>
    @endif

    {{-- Footer Bottom --}}
    <div class="footer-bottom">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="copyright">
                        <p class="mb-0">
                            &copy; {{ $currentYear }} {{ $companyName }}. All rights reserved.
                        </p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="footer-legal">
                        <ul class="legal-links">
                            @if(isset($subdomainData['legal']['privacy_policy_url']))
                            <li><a href="{{ $subdomainData['legal']['privacy_policy_url'] }}">Privacy Policy</a></li>
                            @endif
                            
                            @if(isset($subdomainData['legal']['terms_of_service_url']))
                            <li><a href="{{ $subdomainData['legal']['terms_of_service_url'] }}">Terms of Service</a></li>
                            @endif
                            
                            @if(isset($subdomainData['legal']['disclaimer_url']))
                            <li><a href="{{ $subdomainData['legal']['disclaimer_url'] }}">Disclaimer</a></li>
                            @endif
                            
                            <li><a href="/sitemap.xml">Sitemap</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            @if(isset($subdomainData['legal']['licensing_info']))
            <div class="row mt-3">
                <div class="col-12">
                    <div class="licensing-info">
                        <small class="text-muted">
                            {{ $subdomainData['legal']['licensing_info'] }}
                        </small>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    {{-- Trust Badges --}}
    @if(isset($subdomainData['trust_badges']) && is_array($subdomainData['trust_badges']))
    <div class="trust-badges">
        <div class="container">
            <div class="row justify-content-center">
                @foreach($subdomainData['trust_badges'] as $badge)
                <div class="col-auto">
                    <div class="trust-badge">
                        @if(isset($badge['image']))
                        <img src="{{ $badge['image'] }}" 
                             alt="{{ $badge['alt'] ?? $badge['name'] ?? 'Trust badge' }}" 
                             class="trust-badge-image">
                        @endif
                        @if(isset($badge['text']))
                        <div class="trust-badge-text">{{ $badge['text'] }}</div>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

</footer>

{{-- Footer Styles --}}
@push('styles')
<style>
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
    margin-top: auto;
}

.footer-main {
    padding: 3rem 0 2rem;
}

.footer-bottom {
    background: rgba(0, 0, 0, 0.2);
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff;
}

.footer-subtitle {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.footer-description {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-contact .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: #bdc3c7;
}

.footer-contact .contact-item a {
    color: #ecf0f1;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-contact .contact-item a:hover {
    color: var(--bs-primary, #3498db);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
}

.footer-links a:hover {
    color: var(--bs-primary, #3498db);
    transform: translateX(5px);
}

.newsletter-form {
    margin-bottom: 1.5rem;
}

.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-radius: 6px 0 0 6px;
}

.newsletter-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--bs-primary, #3498db);
    color: #ffffff;
    box-shadow: none;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-form .btn {
    border-radius: 0 6px 6px 0;
    border-left: none;
}

.newsletter-description {
    color: #bdc3c7;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.social-label {
    color: #bdc3c7;
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.social-icons {
    display: flex;
    gap: 0.75rem;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.social-icon:hover {
    background: var(--bs-primary, #3498db);
    color: #ffffff;
    transform: translateY(-3px);
}

.copyright p {
    color: #bdc3c7;
    font-size: 0.9rem;
}

.legal-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 1.5rem;
}

.legal-links a {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.legal-links a:hover {
    color: var(--bs-primary, #3498db);
}

.licensing-info {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.trust-badges {
    background: rgba(0, 0, 0, 0.1);
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.trust-badge {
    text-align: center;
    padding: 0.5rem;
}

.trust-badge-image {
    max-height: 50px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.trust-badge:hover .trust-badge-image {
    opacity: 1;
}

.trust-badge-text {
    font-size: 0.8rem;
    color: #bdc3c7;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .footer-main {
        padding: 2rem 0 1.5rem;
    }
    
    .footer-section {
        margin-bottom: 2rem;
    }
    
    .legal-links {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .copyright {
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Minimal Footer Style */
.footer-style-minimal {
    background: #f8f9fa;
    color: #6c757d;
    border-top: 1px solid #dee2e6;
}

.footer-style-minimal .footer-bottom {
    background: transparent;
    border-top: none;
    padding: 1rem 0;
}

.footer-style-minimal .copyright p,
.footer-style-minimal .legal-links a {
    color: #6c757d;
}

.footer-style-minimal .legal-links a:hover {
    color: var(--bs-primary, #007bff);
}

/* Compact Footer Style */
.footer-style-compact .footer-main {
    display: none;
}

.footer-style-compact .footer-bottom {
    padding: 1rem 0;
}
</style>
@endpush