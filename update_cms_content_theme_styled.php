<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "Updating CMS pages with theme-styled content...\n";

// Get subdomain IDs
$subdomains = DB::table('sub_domains')->where('company_id', 9)->get()->keyBy('subdomain');

$themeStyledContent = [
    'income' => [
        'smsf-investment-guides' => [
            'content' => '
<!-- Page Header Section Start -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <!-- Subpage Header Box Start -->
                <div class="page-header-box">
                    <h1 class="text-anime">SMSF Investment Guides</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">SMSF Investment Guides</li>
                        </ol>
                    </nav>
                </div>
                <!-- Subpage Header Box End -->
            </div>
        </div>
    </div>
</div>
<!-- Page Header Section End -->

<!-- SMSF Guide Content Start -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <!-- Section Title Start -->
                <div class="section-title">
                    <h3 class="wow fadeInUp">Complete SMSF Property Investment Guide 2025</h3>
                    <h2 class="text-anime">Master SMSF Property Investment</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Build wealth through Self-Managed Super Fund property investments with expert guidance and proven strategies.</p>
                    </div>
                </div>
                <!-- Section Title End -->
            </div>
        </div>

        <!-- Key Benefits Section Start -->
        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="Tax Benefits">
                    </div>
                    <h3>Tax Advantages</h3>
                    <ul>
                        <li>15% tax rate during accumulation phase</li>
                        <li>0% tax during pension phase</li>
                        <li>Concessional rental income tax treatment</li>
                        <li>Reduced capital gains tax (33% discount after 12 months)</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Control">
                    </div>
                    <h3>Control & Flexibility</h3>
                    <ul>
                        <li>Direct control over investment decisions</li>
                        <li>Choose your own properties and locations</li>
                        <li>Tailor investment strategy to your goals</li>
                        <li>Potential for higher returns than traditional super</li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- Key Benefits Section End -->

        <!-- Requirements Section Start -->
        <div class="row">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.75s">
                    <h3>SMSF Property Investment Requirements</h3>
                    
                    <div class="alert alert-warning">
                        <h4>Compliance Essentials</h4>
                        <ul>
                            <li>Minimum 20% deposit required (plus costs)</li>
                            <li>Property must meet "sole purpose test"</li>
                            <li>Cannot be occupied by members or related parties</li>
                            <li>Cannot purchase from related parties</li>
                            <li>Limited recourse borrowing arrangement (LRBA) required</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- Requirements Section End -->
    </div>
</div>
<!-- SMSF Guide Content End -->

<!-- Statistics Section Start -->
<div class="how-it-works dark-head">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <!-- Section Title Start -->
                <div class="section-title">
                    <h3 class="wow fadeInUp">SMSF Investment Targets</h3>
                    <h2 class="text-anime">Achieve Your Financial Goals</h2>
                </div>
                <!-- Section Title End -->
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6">
                <!-- Stat Item Start -->
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="">
                    </div>
                    <h3>$250,000+</h3>
                    <p>Minimum SMSF Balance Recommended</p>
                </div>
                <!-- Stat Item End -->
            </div>

            <div class="col-lg-4 col-md-6">
                <!-- Stat Item Start -->
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="">
                    </div>
                    <h3>20%</h3>
                    <p>Minimum Deposit Required</p>
                </div>
                <!-- Stat Item End -->
            </div>

            <div class="col-lg-4 col-md-6">
                <!-- Stat Item Start -->
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-3.svg" alt="">
                    </div>
                    <h3>6.8%</h3>
                    <p>Target Rental Yield</p>
                </div>
                <!-- Stat Item End -->
            </div>
        </div>
    </div>
</div>
<!-- Statistics Section End -->

<!-- Property Types Section Start -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <!-- Section Title Start -->
                <div class="section-title">
                    <h3 class="wow fadeInUp">SMSF-Compliant Property Types</h3>
                    <h2 class="text-anime">Investment Opportunities</h2>
                </div>
                <!-- Section Title End -->
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="Dual Occupancy">
                    </div>
                    <h3>Dual Occupancy Properties</h3>
                    <p>Two separate dwellings on one title, maximizing rental income potential.</p>
                    <ul>
                        <li>Average yield: 7.2%</li>
                        <li>Dual income streams</li>
                        <li>Strong capital growth</li>
                        <li>SMSF compliant structure</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Co-Living">
                    </div>
                    <h3>Co-Living Properties</h3>
                    <p>Purpose-built for shared accommodation, generating premium yields.</p>
                    <ul>
                        <li>Average yield: 8.1%</li>
                        <li>Multiple income streams</li>
                        <li>High demand locations</li>
                        <li>Professional management available</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- CTA Section Start -->
        <div class="row">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.75s" style="text-align: center; background: #f8f9fa; padding: 40px; border-radius: 10px;">
                    <h3>Ready to Start Your SMSF Property Journey?</h3>
                    <p>Our SMSF specialists will guide you through every step of the process, ensuring compliance and maximizing your returns.</p>
                    <div style="margin-top: 30px;">
                        <a href="/page/content/book-strategy-call" class="btn-default" style="margin-right: 15px;">Book Free Strategy Call</a>
                        <a href="/page/content/cashflow-calculator" class="btn-default btn-border">Calculate Your Returns</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- CTA Section End -->
    </div>
</div>
<!-- Property Types Section End -->'
        ],
        
        'cashflow-calculator' => [
            'content' => '
<!-- Page Header Section Start -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <!-- Subpage Header Box Start -->
                <div class="page-header-box">
                    <h1 class="text-anime">Positive Cashflow Calculator</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Cashflow Calculator</li>
                        </ol>
                    </nav>
                </div>
                <!-- Subpage Header Box End -->
            </div>
        </div>
    </div>
</div>
<!-- Page Header Section End -->

<!-- Calculator Section Start -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <!-- Section Title Start -->
                <div class="section-title">
                    <h3 class="wow fadeInUp">Property Investment Calculator</h3>
                    <h2 class="text-anime">Calculate Your Potential Returns</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Discover your potential weekly passive income from our positive cashflow investment properties.</p>
                    </div>
                </div>
                <!-- Section Title End -->
            </div>
        </div>

        <div class="row">
            <!-- Calculator Form -->
            <div class="col-lg-6">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Investment Details</h3>
                    <form id="cashflow-calculator">
                        <div class="form-group">
                            <label>Property Value ($)</label>
                            <input type="number" id="property-value" class="form-control" placeholder="e.g., 450000" value="450000">
                        </div>
                        
                        <div class="form-group">
                            <label>Deposit Percentage (%)</label>
                            <input type="number" id="deposit-percent" class="form-control" placeholder="20" value="20">
                        </div>
                        
                        <div class="form-group">
                            <label>Weekly Rent ($)</label>
                            <input type="number" id="weekly-rent" class="form-control" placeholder="e.g., 580" value="580">
                        </div>
                        
                        <div class="form-group">
                            <label>Interest Rate (%)</label>
                            <input type="number" id="interest-rate" class="form-control" step="0.1" placeholder="6.5" value="6.5">
                        </div>
                        
                        <button type="button" onclick="calculateCashflow()" class="btn-default">Calculate Cashflow</button>
                    </form>
                </div>
            </div>
            
            <!-- Results -->
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Your Investment Results</h3>
                    
                    <div class="result-item">
                        <div class="icon-box">
                            <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="">
                        </div>
                        <div class="result-content">
                            <h4>Weekly Cashflow</h4>
                            <span id="weekly-cashflow" class="result-value">$0</span>
                        </div>
                    </div>
                    
                    <div class="result-item">
                        <div class="icon-box">
                            <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="">
                        </div>
                        <div class="result-content">
                            <h4>Annual Cashflow</h4>
                            <span id="annual-cashflow" class="result-value">$0</span>
                        </div>
                    </div>
                    
                    <div class="result-item">
                        <div class="icon-box">
                            <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-3.svg" alt="">
                        </div>
                        <div class="result-content">
                            <h4>Rental Yield</h4>
                            <span id="rental-yield" class="result-value">0%</span>
                        </div>
                    </div>
                    
                    <div class="result-item">
                        <div class="icon-box">
                            <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-4.svg" alt="">
                        </div>
                        <div class="result-content">
                            <h4>Deposit Required</h4>
                            <span id="deposit-required" class="result-value">$0</span>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h4>Next Steps</h4>
                        <p>Ready to turn these numbers into reality? Our property specialists can show you similar properties achieving these returns.</p>
                        <a href="/page/content/book-strategy-call" class="btn-default">Book Strategy Call</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Calculator Section End -->

<!-- Benefits Section Start -->
<div class="how-it-works dark-head">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <!-- Section Title Start -->
                <div class="section-title">
                    <h3 class="wow fadeInUp">Understanding Positive Cashflow</h3>
                    <h2 class="text-anime">The Path to Passive Income</h2>
                </div>
                <!-- Section Title End -->
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6">
                <!-- Benefit Item Start -->
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="">
                    </div>
                    <h3>Rental Income</h3>
                    <p>Weekly rent received from tenants provides consistent cash flow</p>
                </div>
                <!-- Benefit Item End -->
            </div>

            <div class="col-lg-4 col-md-6">
                <!-- Benefit Item Start -->
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="">
                    </div>
                    <h3>Property Expenses</h3>
                    <p>Loan interest, management fees, and maintenance costs</p>
                </div>
                <!-- Benefit Item End -->
            </div>

            <div class="col-lg-4 col-md-6">
                <!-- Benefit Item Start -->
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-3.svg" alt="">
                    </div>
                    <h3>Net Cashflow</h3>
                    <p>Profit after all expenses are paid - your passive income</p>
                </div>
                <!-- Benefit Item End -->
            </div>
        </div>
    </div>
</div>
<!-- Benefits Section End -->

<style>
.result-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.result-item .icon-box {
    margin-right: 15px;
    flex-shrink: 0;
}

.result-item .icon-box img {
    width: 40px;
    height: 40px;
}

.result-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #333;
}

.result-value {
    font-size: 24px;
    font-weight: bold;
    color: #059669;
}

.result-value.negative {
    color: #dc2626;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}
</style>

<script>
function calculateCashflow() {
    const propertyValue = parseFloat(document.getElementById("property-value").value) || 0;
    const depositPercent = parseFloat(document.getElementById("deposit-percent").value) || 0;
    const weeklyRent = parseFloat(document.getElementById("weekly-rent").value) || 0;
    const interestRate = parseFloat(document.getElementById("interest-rate").value) || 0;
    
    const deposit = propertyValue * (depositPercent / 100);
    const loanAmount = propertyValue - deposit;
    const weeklyInterest = (loanAmount * (interestRate / 100)) / 52;
    const weeklyExpenses = weeklyRent * 0.15; // Assume 15% for expenses
    const weeklyCashflow = weeklyRent - weeklyInterest - weeklyExpenses;
    const annualCashflow = weeklyCashflow * 52;
    const rentalYield = (weeklyRent * 52 / propertyValue) * 100;
    
    document.getElementById("weekly-cashflow").textContent = "$" + weeklyCashflow.toFixed(0);
    document.getElementById("annual-cashflow").textContent = "$" + annualCashflow.toFixed(0);
    document.getElementById("rental-yield").textContent = rentalYield.toFixed(1) + "%";
    document.getElementById("deposit-required").textContent = "$" + deposit.toLocaleString();
    
    // Color code based on positive/negative
    const cashflowElements = document.querySelectorAll("#weekly-cashflow, #annual-cashflow");
    cashflowElements.forEach(element => {
        if (weeklyCashflow > 0) {
            element.className = "result-value";
        } else {
            element.className = "result-value negative";
        }
    });
}

// Calculate on page load
document.addEventListener("DOMContentLoaded", function() {
    calculateCashflow();
});

// Recalculate when inputs change
document.querySelectorAll("#cashflow-calculator input").forEach(input => {
    input.addEventListener("input", calculateCashflow);
});
</script>'
        ]
    ]
];

$companyId = 9;
$userId = 1;

foreach ($themeStyledContent as $subdomainName => $pages) {
    $subdomainId = $subdomains[$subdomainName]->id ?? null;
    
    if (!$subdomainId) {
        echo "Subdomain $subdomainName not found, skipping...\n";
        continue;
    }
    
    echo "Updating theme-styled content for $subdomainName (ID: $subdomainId):\n";
    
    foreach ($pages as $slug => $page) {
        $updated = DB::table('cms_pages')
            ->where('slug', $slug)
            ->where('subdomain_id', $subdomainId)
            ->where('company_id', $companyId)
            ->update([
                'content' => $page['content'],
                'updated_at' => now(),
                'updated_by' => $userId
            ]);
            
        if ($updated) {
            echo "  ✓ Updated: $slug\n";
        } else {
            echo "  ✗ Failed to update: $slug\n";
        }
    }
    echo "\n";
}

echo "Theme-styled CMS content update completed!\n";