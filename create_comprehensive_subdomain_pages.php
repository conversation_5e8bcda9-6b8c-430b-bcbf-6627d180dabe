<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "Creating comprehensive subdomain pages based on requirements...\n";

$companyId = 9;
$userId = 1;

// Subdomain mapping
$subdomains = [
    'income' => 1,      // income.seamlesspropertysolutions.com.au
    'taxbenefits' => 3, // taxbenefits.seamlesspropertysolutions.com.au  
    'fhb' => 4,         // fhb.seamlesspropertysolutions.com.au
    'projects' => 5     // projects.seamlesspropertysolutions.com.au
];

// Income Subdomain Pages
$incomePages = [
    [
        'name' => 'Yield Comparison by State',
        'slug' => 'yield-comparison-states',
        'content' => '
<!-- Page Header Section Start -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Yield Comparison by State</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Yield Comparison</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Yield Analysis Section Start -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">2025 Property Yield Analysis</h3>
                    <h2 class="text-anime">Where Smart Investors Find The Best Returns</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Compare rental yields across Australian states to maximize your investment returns. Our data covers 12,000+ properties nationwide.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- State Comparison Grid -->
        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-1.svg" alt="Queensland">
                    </div>
                    <h3>Queensland</h3>
                    <div class="yield-stats">
                        <div class="yield-item">
                            <span class="yield-label">Average Yield:</span>
                            <span class="yield-value">7.2%</span>
                        </div>
                        <div class="yield-item">
                            <span class="yield-label">SMSF Properties:</span>
                            <span class="yield-value">8.1%</span>
                        </div>
                        <div class="yield-item">
                            <span class="yield-label">Dual Occupancy:</span>
                            <span class="yield-value">8.8%</span>
                        </div>
                    </div>
                    <ul>
                        <li>Strong mining town returns</li>
                        <li>Regional growth corridors</li>
                        <li>SMSF-compliant new builds</li>
                        <li>Positive cashflow focus</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-2.svg" alt="New South Wales">
                    </div>
                    <h3>New South Wales</h3>
                    <div class="yield-stats">
                        <div class="yield-item">
                            <span class="yield-label">Average Yield:</span>
                            <span class="yield-value">5.8%</span>
                        </div>
                        <div class="yield-item">
                            <span class="yield-label">Regional NSW:</span>
                            <span class="yield-value">7.4%</span>
                        </div>
                        <div class="yield-item">
                            <span class="yield-label">Co-living:</span>
                            <span class="yield-value">9.2%</span>
                        </div>
                    </div>
                    <ul>
                        <li>Premium co-living opportunities</li>
                        <li>Hunter Valley investments</li>
                        <li>Metro fringe development</li>
                        <li>Capital growth + yield balance</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-3.svg" alt="Victoria">
                    </div>
                    <h3>Victoria</h3>
                    <div class="yield-stats">
                        <div class="yield-item">
                            <span class="yield-label">Average Yield:</span>
                            <span class="yield-value">5.2%</span>
                        </div>
                        <div class="yield-item">
                            <span class="yield-label">Regional VIC:</span>
                            <span class="yield-value">6.9%</span>
                        </div>
                        <div class="yield-item">
                            <span class="yield-label">Student Accommodation:</span>
                            <span class="yield-value">8.5%</span>
                        </div>
                    </div>
                    <ul>
                        <li>Geelong corridor growth</li>
                        <li>Regional manufacturing hubs</li>
                        <li>University town yields</li>
                        <li>Infrastructure development</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Interactive Yield Calculator -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Find Your Ideal Investment State</h3>
                    <form id="yield-finder">
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Investment Budget</label>
                                    <select class="form-control" id="budget-range">
                                        <option value="300-450">$300k - $450k</option>
                                        <option value="450-600">$450k - $600k</option>
                                        <option value="600-800">$600k - $800k</option>
                                        <option value="800+">$800k+</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Target Yield</label>
                                    <select class="form-control" id="yield-target">
                                        <option value="6+">6%+ (Balanced)</option>
                                        <option value="7+">7%+ (High Yield)</option>
                                        <option value="8+">8%+ (Premium Yield)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Property Type</label>
                                    <select class="form-control" id="property-type">
                                        <option value="house-land">House & Land</option>
                                        <option value="dual-occupancy">Dual Occupancy</option>
                                        <option value="co-living">Co-living</option>
                                        <option value="townhouse">Townhouse</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn-default w-100" onclick="findIdealStates()">Find Best States</button>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div id="yield-results" style="margin-top: 30px; display: none;">
                        <h4>Recommended Investment Locations:</h4>
                        <div id="recommended-states"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.yield-stats {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
}

.yield-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.yield-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.yield-label {
    font-weight: 500;
    color: #6c757d;
}

.yield-value {
    font-weight: 700;
    color: #10b981;
    font-size: 18px;
}

.yield-recommendation {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.yield-recommendation h5 {
    color: #059669;
    margin-bottom: 8px;
}
</style>

<script>
function findIdealStates() {
    const budget = document.getElementById("budget-range").value;
    const yield = document.getElementById("yield-target").value;
    const propertyType = document.getElementById("property-type").value;
    
    const resultsDiv = document.getElementById("yield-results");
    const statesDiv = document.getElementById("recommended-states");
    
    let recommendations = [];
    
    // Logic for recommendations based on selections
    if (yield === "8+" && propertyType === "dual-occupancy") {
        recommendations = [
            { state: "Queensland", reason: "8.8% dual occupancy yields in mining towns", properties: "45 available" },
            { state: "Regional NSW", reason: "8.2% yields in Hunter Valley", properties: "23 available" }
        ];
    } else if (yield === "7+" && budget === "300-450") {
        recommendations = [
            { state: "Queensland", reason: "7.2% average with strong growth", properties: "67 available" },
            { state: "Regional Victoria", reason: "6.9% yields with infrastructure growth", properties: "34 available" }
        ];
    } else {
        recommendations = [
            { state: "Queensland", reason: "Best overall yields for your criteria", properties: "89 available" },
            { state: "Regional NSW", reason: "Strong rental demand", properties: "56 available" },
            { state: "Victoria", reason: "Balanced yield and growth", properties: "43 available" }
        ];
    }
    
    statesDiv.innerHTML = recommendations.map(rec => `
        <div class="yield-recommendation">
            <h5>${rec.state}</h5>
            <p>${rec.reason}</p>
            <small><strong>${rec.properties}</strong> - <a href="/property-listing?state=${rec.state.toLowerCase()}">View Properties</a></small>
        </div>
    `).join("");
    
    resultsDiv.style.display = "block";
}
</script>'
    ],

    [
        'name' => 'SMSF Setup Partners',
        'slug' => 'smsf-setup-partners',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">SMSF Setup Partners</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">SMSF Partners</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Partners Section -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Trusted SMSF Specialists Network</h3>
                    <h2 class="text-anime">Setup Your SMSF With Confidence</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Partner with our vetted SMSF professionals who specialize in property investment setups. Average setup time: 14 days.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="SMSF Accountants">
                    </div>
                    <h3>SMSF Accountants</h3>
                    <ul>
                        <li><strong>SMSF Pro</strong> - $2,200 setup + $3,300 annual</li>
                        <li><strong>Super Specialists</strong> - $1,950 setup + $2,900 annual</li>
                        <li><strong>Property SMSF Experts</strong> - $2,500 setup + $3,800 annual</li>
                    </ul>
                    <p>All partners are SMSF Auditors Australia approved and specialize in property investment compliance.</p>
                    <a href="#" class="btn-default" onclick="bookSMSFConsultation(\'accountant\')">Book Consultation</a>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="SMSF Lawyers">
                    </div>
                    <h3>SMSF Legal Services</h3>
                    <ul>
                        <li><strong>Trust Legal</strong> - $1,800 deed preparation</li>
                        <li><strong>SMSF Law Group</strong> - $2,100 comprehensive setup</li>
                        <li><strong>Property Law Specialists</strong> - $1,650 documents only</li>
                    </ul>
                    <p>Specializing in SMSF trust deeds, investment strategies, and compliance documentation for property acquisitions.</p>
                    <a href="#" class="btn-default" onclick="bookSMSFConsultation(\'legal\')">Book Consultation</a>
                </div>
            </div>
        </div>

        <!-- Setup Process -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">SMSF Setup Process</h3>
                    <h2 class="text-anime">Your 14-Day Journey to Property Investment</h2>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="Day 1-3">
                    </div>
                    <h3>Days 1-3</h3>
                    <h4>Initial Setup</h4>
                    <ul>
                        <li>Choose your SMSF name</li>
                        <li>Complete application forms</li>
                        <li>Submit to ATO</li>
                        <li>Receive ABN & TFN</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Day 4-7">
                    </div>
                    <h3>Days 4-7</h3>
                    <h4>Documentation</h4>
                    <ul>
                        <li>Trust deed preparation</li>
                        <li>Investment strategy</li>
                        <li>Banking setup</li>
                        <li>Insurance considerations</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-3.svg" alt="Day 8-11">
                    </div>
                    <h3>Days 8-11</h3>
                    <h4>Fund Transfer</h4>
                    <ul>
                        <li>Rollover existing super</li>
                        <li>Contribution planning</li>
                        <li>LRBA setup if needed</li>
                        <li>Property research</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="how-it-work-item wow fadeInUp" data-wow-delay="1s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-4.svg" alt="Day 12-14">
                    </div>
                    <h3>Days 12-14</h3>
                    <h4>Ready to Invest</h4>
                    <ul>
                        <li>Final compliance check</li>
                        <li>Property pre-approval</li>
                        <li>Investment strategy review</li>
                        <li>Begin property search</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function bookSMSFConsultation(type) {
    // In a real implementation, this would open a booking form
    alert(`Booking ${type} consultation. This would redirect to a calendar booking system.`);
}
</script>'
    ]
];

// Tax Benefits Subdomain Pages  
$taxBenefitsPages = [
    [
        'name' => 'ATO-Approved Depreciation Schedules',
        'slug' => 'ato-depreciation-schedules',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">ATO-Approved Depreciation Schedules</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Depreciation Schedules</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Depreciation Guide -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Maximize Your Tax Deductions</h3>
                    <h2 class="text-anime">Professional Depreciation Schedules</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Get ATO-compliant depreciation schedules prepared by Quantity Surveyors. Average first-year deductions: $8,000-$25,000.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-1.svg" alt="New Properties">
                    </div>
                    <h3>New Properties</h3>
                    <div class="depreciation-breakdown">
                        <div class="depr-item">
                            <span>Building Allowance (2.5%)</span>
                            <span>$10,000/year</span>
                        </div>
                        <div class="depr-item">
                            <span>Plant & Equipment</span>
                            <span>$8,000-$15,000</span>
                        </div>
                        <div class="depr-item total">
                            <span><strong>Total First Year</strong></span>
                            <span><strong>$18,000-$25,000</strong></span>
                        </div>
                    </div>
                    <p>New builds offer the highest depreciation benefits with full building allowances and modern fixtures.</p>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-2.svg" alt="Established Properties">
                    </div>
                    <h3>Established Properties</h3>
                    <div class="depreciation-breakdown">
                        <div class="depr-item">
                            <span>Plant & Equipment Only</span>
                            <span>$4,000-$8,000</span>
                        </div>
                        <div class="depr-item">
                            <span>Renovations/Additions</span>
                            <span>$2,000-$5,000</span>
                        </div>
                        <div class="depr-item total">
                            <span><strong>Total First Year</strong></span>
                            <span><strong>$6,000-$13,000</strong></span>
                        </div>
                    </div>
                    <p>Established properties built after 1987 still offer plant & equipment depreciation opportunities.</p>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-counter-3.svg" alt="Renovated Properties">
                    </div>
                    <h3>Renovated Properties</h3>
                    <div class="depreciation-breakdown">
                        <div class="depr-item">
                            <span>Recent Renovations</span>
                            <span>$5,000-$12,000</span>
                        </div>
                        <div class="depr-item">
                            <span>Kitchen/Bathroom</span>
                            <span>$3,000-$8,000</span>
                        </div>
                        <div class="depr-item total">
                            <span><strong>Total First Year</strong></span>
                            <span><strong>$8,000-$20,000</strong></span>
                        </div>
                    </div>
                    <p>Properties with substantial renovations can claim significant depreciation on new fixtures and fittings.</p>
                </div>
            </div>
        </div>

        <!-- QS Partners -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Our Quantity Surveyor Partners</h3>
                    <h2 class="text-anime">ATO-Registered Professionals</h2>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Premium QS Services</h3>
                    <div class="qs-partner">
                        <h4>Property Tax Specialists</h4>
                        <div class="qs-details">
                            <div class="qs-price">$660 inc GST</div>
                            <div class="qs-features">
                                <ul>
                                    <li>✓ Full ATO-compliant report</li>
                                    <li>✓ On-site inspection included</li>
                                    <li>✓ 40-page detailed schedule</li>
                                    <li>✓ 5-year audit protection</li>
                                    <li>✓ Annual depreciation tables</li>
                                </ul>
                            </div>
                        </div>
                        <p><strong>Turnaround:</strong> 5-7 business days</p>
                        <a href="#" class="btn-default" onclick="orderDepreciationReport(\'premium\')">Order Report</a>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Express QS Services</h3>
                    <div class="qs-partner">
                        <h4>Rapid Depreciation Co</h4>
                        <div class="qs-details">
                            <div class="qs-price">$495 inc GST</div>
                            <div class="qs-features">
                                <ul>
                                    <li>✓ ATO-compliant desktop report</li>
                                    <li>✓ Photo-based assessment</li>
                                    <li>✓ 25-page summary schedule</li>
                                    <li>✓ 3-year audit support</li>
                                    <li>✓ Digital delivery</li>
                                </ul>
                            </div>
                        </div>
                        <p><strong>Turnaround:</strong> 2-3 business days</p>
                        <a href="#" class="btn-default btn-border" onclick="orderDepreciationReport(\'express\')">Order Report</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.depreciation-breakdown {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
}

.depr-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.depr-item:last-child {
    border-bottom: none;
}

.depr-item.total {
    background: #2563eb;
    color: white;
    margin: 10px -20px -20px -20px;
    padding: 15px 20px;
    border-radius: 0 0 8px 8px;
}

.qs-partner {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
}

.qs-details {
    display: flex;
    align-items: center;
    margin: 15px 0;
}

.qs-price {
    font-size: 24px;
    font-weight: bold;
    color: #2563eb;
    margin-right: 20px;
}

.qs-features ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.qs-features li {
    margin-bottom: 5px;
    color: #059669;
}
</style>

<script>
function orderDepreciationReport(type) {
    alert(`Ordering ${type} depreciation report. This would redirect to the booking system.`);
}
</script>'
    ],

    [
        'name' => 'High-Tax Suburb Reports',
        'slug' => 'high-tax-suburb-reports',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">High-Tax Suburb Investment Reports</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Suburb Reports</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Suburb Analysis -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Strategic Tax-Minimizing Locations</h3>
                    <h2 class="text-anime">Where High Earners Invest Smart</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Target suburbs with optimal negative gearing benefits for $180k+ income earners. Maximize depreciation and minimize tax.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Suburbs -->
        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-property-type-1.svg" alt="Sydney Metro">
                    </div>
                    <h3>Sydney Metro Corridors</h3>
                    <div class="suburb-stats">
                        <div class="stat-item">
                            <span class="stat-label">Average Tax Benefit:</span>
                            <span class="stat-value">$15,200/year</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Depreciation:</span>
                            <span class="stat-value">$18,500</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Capital Growth:</span>
                            <span class="stat-value">4.2% p.a.</span>
                        </div>
                    </div>
                    <h4>Featured Suburbs:</h4>
                    <ul>
                        <li>Kellyville - New builds, high depreciation</li>
                        <li>Box Hill - Metro access, growth corridor</li>
                        <li>Cherrybrook - Premium finishes</li>
                        <li>Rouse Hill - Infrastructure development</li>
                    </ul>
                    <a href="#" class="btn-default" onclick="downloadSuburbReport(\'sydney\')">Download Report</a>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-property-type-2.svg" alt="Melbourne Growth">
                    </div>
                    <h3>Melbourne Growth Zones</h3>
                    <div class="suburb-stats">
                        <div class="stat-item">
                            <span class="stat-label">Average Tax Benefit:</span>
                            <span class="stat-value">$13,800/year</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Depreciation:</span>
                            <span class="stat-value">$16,200</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Capital Growth:</span>
                            <span class="stat-value">3.8% p.a.</span>
                        </div>
                    </div>
                    <h4>Featured Suburbs:</h4>
                    <ul>
                        <li>Cranbourne - New estate developments</li>
                        <li>Pakenham - Train line extension</li>
                        <li>Clyde North - Master-planned community</li>
                        <li>Officer - Family-focused development</li>
                    </ul>
                    <a href="#" class="btn-default" onclick="downloadSuburbReport(\'melbourne\')">Download Report</a>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-property-type-3.svg" alt="Brisbane Prestige">
                    </div>
                    <h3>Brisbane Prestige Markets</h3>
                    <div class="suburb-stats">
                        <div class="stat-item">
                            <span class="stat-label">Average Tax Benefit:</span>
                            <span class="stat-value">$12,400/year</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Depreciation:</span>
                            <span class="stat-value">$14,800</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Capital Growth:</span>
                            <span class="stat-value">5.1% p.a.</span>
                        </div>
                    </div>
                    <h4>Featured Suburbs:</h4>
                    <ul>
                        <li>Springfield Central - New city development</li>
                        <li>Yarrabilba - Large lot homes</li>
                        <li>Flagstone - Premium estates</li>
                        <li>Park Ridge - Infrastructure boom</li>
                    </ul>
                    <a href="#" class="btn-default" onclick="downloadSuburbReport(\'brisbane\')">Download Report</a>
                </div>
            </div>
        </div>

        <!-- Tax Calculator -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Suburb Tax Benefit Calculator</h3>
                    <p>Calculate your potential tax savings based on income and target suburb investment.</p>
                    
                    <form id="tax-suburb-calculator">
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Annual Income</label>
                                    <select class="form-control" id="income-bracket">
                                        <option value="120000">$120,000</option>
                                        <option value="150000">$150,000</option>
                                        <option value="180000" selected>$180,000</option>
                                        <option value="220000">$220,000</option>
                                        <option value="300000">$300,000+</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Target Suburb</label>
                                    <select class="form-control" id="target-suburb">
                                        <option value="kellyville">Kellyville, NSW</option>
                                        <option value="cranbourne">Cranbourne, VIC</option>
                                        <option value="springfield">Springfield Central, QLD</option>
                                        <option value="harrisdale">Harrisdale, WA</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Property Value</label>
                                    <input type="number" class="form-control" id="property-value" placeholder="750000" value="750000">
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn-default w-100" onclick="calculateSuburbTaxBenefit()">Calculate Savings</button>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div id="tax-suburb-results" style="margin-top: 30px; display: none;">
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="tax-result-box">
                                    <h4>Annual Tax Saving</h4>
                                    <div id="annual-tax-saving" class="tax-result-value">$0</div>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="tax-result-box">
                                    <h4>Weekly Benefit</h4>
                                    <div id="weekly-benefit" class="tax-result-value">$0</div>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="tax-result-box">
                                    <h4>Marginal Tax Rate</h4>
                                    <div id="marginal-rate" class="tax-result-value">0%</div>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="tax-result-box">
                                    <h4>Available Properties</h4>
                                    <div id="available-props" class="tax-result-value">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.suburb-stats {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.stat-label {
    font-weight: 500;
    color: #6c757d;
}

.stat-value {
    font-weight: 700;
    color: #2563eb;
}

.tax-result-box {
    background: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.tax-result-box h4 {
    margin-bottom: 10px;
    color: #1e40af;
    font-size: 14px;
    font-weight: 600;
}

.tax-result-value {
    font-size: 24px;
    font-weight: 700;
    color: #2563eb;
}
</style>

<script>
function downloadSuburbReport(city) {
    alert(`Downloading ${city} suburb investment report. This would trigger a PDF download.`);
}

function calculateSuburbTaxBenefit() {
    const income = parseInt(document.getElementById("income-bracket").value);
    const suburb = document.getElementById("target-suburb").value;
    const propertyValue = parseInt(document.getElementById("property-value").value);
    
    // Simplified tax calculation
    let marginalRate = 0.37; // Default for $180k+
    if (income >= 300000) marginalRate = 0.45;
    else if (income >= 180000) marginalRate = 0.37;
    else if (income >= 120000) marginalRate = 0.32;
    
    // Estimated annual deductions based on suburb and property value
    let annualDeductions = 22000; // Base deductions
    if (suburb.includes("kellyville")) annualDeductions = 28500;
    else if (suburb.includes("cranbourne")) annualDeductions = 26200;
    else if (suburb.includes("springfield")) annualDeductions = 24800;
    
    // Adjust for property value
    annualDeductions = annualDeductions * (propertyValue / 750000);
    
    const annualTaxSaving = annualDeductions * marginalRate;
    const weeklyBenefit = annualTaxSaving / 52;
    
    // Mock available properties
    const availableProps = Math.floor(Math.random() * 25) + 15;
    
    document.getElementById("annual-tax-saving").textContent = "$" + Math.round(annualTaxSaving).toLocaleString();
    document.getElementById("weekly-benefit").textContent = "$" + Math.round(weeklyBenefit);
    document.getElementById("marginal-rate").textContent = (marginalRate * 100) + "%";
    document.getElementById("available-props").textContent = availableProps;
    
    document.getElementById("tax-suburb-results").style.display = "block";
}
</script>'
    ],

    [
        'name' => 'Accountant Webinar Replays',
        'slug' => 'accountant-webinar-replays',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Expert Accountant Webinar Library</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Webinar Replays</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webinar Library -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Expert Tax Strategy Sessions</h3>
                    <h2 class="text-anime">Learn From Specialist Property Accountants</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Watch recorded sessions with leading property tax specialists. Over 25 hours of expert insights for high-income investors.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Webinars -->
        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s" style="height: 420px;">
                    <div class="webinar-thumbnail">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/video-img-1.jpg" alt="Negative Gearing 2025" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px;">
                        <div class="play-overlay" onclick="playWebinar(1)">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="webinar-duration">58:32</div>
                    </div>
                    <h3>Negative Gearing Strategies 2025</h3>
                    <p class="webinar-speaker">With <strong>Sarah Chen CPA</strong> - Property Tax Specialist</p>
                    <ul style="font-size: 14px;">
                        <li>New ATO ruling updates</li>
                        <li>$180k+ income optimization</li>
                        <li>Metro vs regional strategies</li>
                        <li>Depreciation maximization</li>
                    </ul>
                    <div class="webinar-stats">
                        <span>Views: 2,847</span> | <span>Rating: 4.9/5</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s" style="height: 420px;">
                    <div class="webinar-thumbnail">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/video-img-2.jpg" alt="Building Allowances" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px;">
                        <div class="play-overlay" onclick="playWebinar(2)">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="webinar-duration">43:15</div>
                    </div>
                    <h3>Building Allowances Explained</h3>
                    <p class="webinar-speaker">With <strong>Michael Rodriguez</strong> - Quantity Surveyor</p>
                    <ul style="font-size: 14px;">
                        <li>2.5% building allowance rules</li>
                        <li>Plant & equipment schedules</li>
                        <li>New vs established properties</li>
                        <li>Common ATO audit triggers</li>
                    </ul>
                    <div class="webinar-stats">
                        <span>Views: 1,923</span> | <span>Rating: 4.8/5</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s" style="height: 420px;">
                    <div class="webinar-thumbnail">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/hero-1.jpg" alt="High Income Tax Planning" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px;">
                        <div class="play-overlay" onclick="playWebinar(3)">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="webinar-duration">67:41</div>
                    </div>
                    <h3>High Income Tax Planning</h3>
                    <p class="webinar-speaker">With <strong>Jennifer Walsh CA</strong> - Senior Tax Advisor</p>
                    <ul style="font-size: 14px;">
                        <li>$300k+ income strategies</li>
                        <li>Trust structure benefits</li>
                        <li>Multiple property portfolios</li>
                        <li>Capital gains planning</li>
                    </ul>
                    <div class="webinar-stats">
                        <span>Views: 3,156</span> | <span>Rating: 5.0/5</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- More Webinars -->
        <div class="row" style="margin-top: 30px;">
            <div class="col-lg-6 col-md-6">
                <div class="webinar-list-item wow fadeInUp" data-wow-delay="0.25s">
                    <div class="webinar-meta">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-play.svg" alt="Play" class="play-icon" onclick="playWebinar(4)">
                        <div class="webinar-info">
                            <h4>Renovation Deductions Deep Dive</h4>
                            <p>Tom Brady CPA • 41:23 • 1,567 views</p>
                            <span class="webinar-tags">Kitchen renovations, Bathroom upgrades, Capital vs repair</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6">
                <div class="webinar-list-item wow fadeInUp" data-wow-delay="0.5s">
                    <div class="webinar-meta">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-play.svg" alt="Play" class="play-icon" onclick="playWebinar(5)">
                        <div class="webinar-info">
                            <h4>Property Trust Structures</h4>
                            <p>Amanda Foster • 52:18 • 2,234 views</p>
                            <span class="webinar-tags">Family trusts, Unit trusts, Tax minimization</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6">
                <div class="webinar-list-item wow fadeInUp" data-wow-delay="0.75s">
                    <div class="webinar-meta">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-play.svg" alt="Play" class="play-icon" onclick="playWebinar(6)">
                        <div class="webinar-info">
                            <h4>ATO Audit Defense Strategies</h4>
                            <p>David Kim • 39:47 • 1,812 views</p>
                            <span class="webinar-tags">Audit preparation, Record keeping, Common issues</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6">
                <div class="webinar-list-item wow fadeInUp" data-wow-delay="1s">
                    <div class="webinar-meta">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-play.svg" alt="Play" class="play-icon" onclick="playWebinar(7)">
                        <div class="webinar-info">
                            <h4>Commercial Property Tax Benefits</h4>
                            <p>Lisa Zhang CPA • 44:32 • 987 views</p>
                            <span class="webinar-tags">Commercial depreciation, GST claims, Business use</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s" style="text-align: center; background: #f8f9fa; padding: 40px;">
                    <h3>Get Personal Tax Strategy Advice</h3>
                    <p>Ready to implement these strategies for your property portfolio? Book a one-on-one session with our specialist accountants.</p>
                    <div style="margin-top: 30px;">
                        <a href="/page/content/book-tax-strategy" class="btn-default" style="margin-right: 15px;">Book Tax Strategy Session</a>
                        <a href="/property-listing" class="btn-default btn-border">View Tax-Optimized Properties</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.webinar-thumbnail {
    position: relative;
    margin-bottom: 15px;
}

.play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(37, 99, 235, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-overlay:hover {
    background: rgba(37, 99, 235, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-overlay i {
    color: white;
    font-size: 20px;
    margin-left: 3px;
}

.webinar-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.webinar-speaker {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 10px;
}

.webinar-stats {
    font-size: 12px;
    color: #6c757d;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

.webinar-list-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
}

.webinar-meta {
    display: flex;
    align-items: flex-start;
}

.play-icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.play-icon:hover {
    opacity: 1;
}

.webinar-info h4 {
    margin-bottom: 5px;
    color: #333;
}

.webinar-info p {
    margin-bottom: 8px;
    color: #6c757d;
    font-size: 14px;
}

.webinar-tags {
    font-size: 12px;
    color: #2563eb;
    font-style: italic;
}
</style>

<script>
function playWebinar(id) {
    // In a real implementation, this would open a video player
    alert(`Playing webinar ${id}. This would open a video player modal or redirect to a video page.`);
}
</script>'
    ]
];

echo "Creating Income subdomain pages...\n";
foreach ($incomePages as $page) {
    DB::table('cms_pages')->insert([
        'name' => $page['name'],
        'slug' => $page['slug'],
        'content' => $page['content'],
        'subdomain_id' => $subdomains['income'],
        'company_id' => $companyId,
        'status' => 1,
        'created_at' => now(),
        'updated_at' => now(),
        'created_by' => $userId,
        'updated_by' => $userId
    ]);
    echo "  ✓ Created: {$page['name']}\n";
}

echo "\nCreating Tax Benefits subdomain pages...\n";
foreach ($taxBenefitsPages as $page) {
    DB::table('cms_pages')->insert([
        'name' => $page['name'],
        'slug' => $page['slug'],
        'content' => $page['content'],
        'subdomain_id' => $subdomains['taxbenefits'],
        'company_id' => $companyId,
        'status' => 1,
        'created_at' => now(),
        'updated_at' => now(),
        'created_by' => $userId,
        'updated_by' => $userId
    ]);
    echo "  ✓ Created: {$page['name']}\n";
}

// FHB Subdomain Pages
$fhbPages = [
    [
        'name' => 'Finance Pre-Approval Partners',
        'slug' => 'finance-preapproval-partners',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Finance Pre-Approval Partners</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Finance Partners</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Finance Partners -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">First Home Buyer Finance Specialists</h3>
                    <h2 class="text-anime">Get Pre-Approved in 48 Hours</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Connect with mortgage brokers who specialize in first home buyer loans, grants, and low deposit options.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="First National">
                    </div>
                    <h3>First National Finance</h3>
                    <div class="finance-features">
                        <ul>
                            <li>✓ 5% deposit loans</li>
                            <li>✓ Family guarantee options</li>
                            <li>✓ FHLDS specialist</li>
                            <li>✓ Grant application assistance</li>
                        </ul>
                    </div>
                    <div class="broker-stats">
                        <div class="stat-row">
                            <span>Success Rate:</span>
                            <span>94%</span>
                        </div>
                        <div class="stat-row">
                            <span>Avg. Pre-approval:</span>
                            <span>36 hours</span>
                        </div>
                        <div class="stat-row">
                            <span>Lenders:</span>
                            <span>35+ banks</span>
                        </div>
                    </div>
                    <a href="#" class="btn-default" onclick="bookFinanceConsult(\'firstnational\')">Get Pre-Approved</a>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Home Loan Experts">
                    </div>
                    <h3>Home Loan Experts</h3>
                    <div class="finance-features">
                        <ul>
                            <li>✓ Shared equity loans</li>
                            <li>✓ Regional scheme access</li>
                            <li>✓ Single parent support</li>
                            <li>✓ Credit repair services</li>
                        </ul>
                    </div>
                    <div class="broker-stats">
                        <div class="stat-row">
                            <span>Success Rate:</span>
                            <span>91%</span>
                        </div>
                        <div class="stat-row">
                            <span>Avg. Pre-approval:</span>
                            <span>48 hours</span>
                        </div>
                        <div class="stat-row">
                            <span>Lenders:</span>
                            <span>28+ banks</span>
                        </div>
                    </div>
                    <a href="#" class="btn-default" onclick="bookFinanceConsult(\'homeloan\')">Get Pre-Approved</a>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-3.svg" alt="FHB Finance Pro">
                    </div>
                    <h3>FHB Finance Pro</h3>
                    <div class="finance-features">
                        <ul>
                            <li>✓ Building loan specialist</li>
                            <li>✓ Off-the-plan expertise</li>
                            <li>✓ Construction bridging</li>
                            <li>✓ Progress payment support</li>
                        </ul>
                    </div>
                    <div class="broker-stats">
                        <div class="stat-row">
                            <span>Success Rate:</span>
                            <span>89%</span>
                        </div>
                        <div class="stat-row">
                            <span>Avg. Pre-approval:</span>
                            <span>24 hours</span>
                        </div>
                        <div class="stat-row">
                            <span>Lenders:</span>
                            <span>22+ banks</span>
                        </div>
                    </div>
                    <a href="#" class="btn-default" onclick="bookFinanceConsult(\'fhbpro\')">Get Pre-Approved</a>
                </div>
            </div>
        </div>

        <!-- Pre-approval Calculator -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Pre-Approval Calculator</h3>
                    <p>Get an instant estimate of your borrowing capacity and required deposit.</p>
                    
                    <form id="preapproval-calculator">
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Combined Income</label>
                                    <input type="number" class="form-control" id="combined-income" placeholder="85000" value="85000">
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Monthly Expenses</label>
                                    <input type="number" class="form-control" id="monthly-expenses" placeholder="3500" value="3500">
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>Current Savings</label>
                                    <input type="number" class="form-control" id="current-savings" placeholder="45000" value="45000">
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn-default w-100" onclick="calculatePreApproval()">Calculate</button>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div id="preapproval-results" style="margin-top: 30px; display: none;">
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="calc-result-box">
                                    <h4>Borrowing Capacity</h4>
                                    <div id="borrowing-capacity" class="calc-result-value">$0</div>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="calc-result-box">
                                    <h4>5% Deposit Required</h4>
                                    <div id="deposit-5percent" class="calc-result-value">$0</div>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="calc-result-box">
                                    <h4>Total Purchase Price</h4>
                                    <div id="total-purchase" class="calc-result-value">$0</div>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="calc-result-box">
                                    <h4>Suitable Properties</h4>
                                    <div id="suitable-props" class="calc-result-value">0</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="preapproval-actions" style="margin-top: 30px; text-align: center;">
                            <a href="#" class="btn-default" onclick="startPreApproval()">Start Pre-Approval Process</a>
                            <a href="/property-listing" class="btn-default btn-border">View Suitable Properties</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.finance-features ul {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.finance-features li {
    margin-bottom: 8px;
    color: #7c3aed;
    font-weight: 500;
}

.broker-stats {
    background: #faf5ff;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e9ecef;
}

.stat-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.calc-result-box {
    background: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.calc-result-box h4 {
    margin-bottom: 10px;
    color: #7c3aed;
    font-size: 14px;
    font-weight: 600;
}

.calc-result-value {
    font-size: 24px;
    font-weight: 700;
    color: #7c3aed;
}
</style>

<script>
function bookFinanceConsult(broker) {
    alert(`Booking consultation with ${broker}. This would redirect to a calendar booking system.`);
}

function calculatePreApproval() {
    const income = parseInt(document.getElementById("combined-income").value);
    const expenses = parseInt(document.getElementById("monthly-expenses").value);
    const savings = parseInt(document.getElementById("current-savings").value);
    
    // Simplified borrowing capacity calculation
    const netMonthlyIncome = (income / 12) - expenses;
    const borrowingCapacity = Math.min(income * 6, netMonthlyIncome * 12 * 5); // Conservative estimate
    
    const deposit5Percent = borrowingCapacity * 0.05;
    const totalPurchase = borrowingCapacity;
    
    // Estimate suitable properties count
    const suitableProps = Math.floor(borrowingCapacity / 50000) + 15;
    
    document.getElementById("borrowing-capacity").textContent = "$" + Math.round(borrowingCapacity).toLocaleString();
    document.getElementById("deposit-5percent").textContent = "$" + Math.round(deposit5Percent).toLocaleString();
    document.getElementById("total-purchase").textContent = "$" + Math.round(totalPurchase).toLocaleString();
    document.getElementById("suitable-props").textContent = suitableProps;
    
    document.getElementById("preapproval-results").style.display = "block";
}

function startPreApproval() {
    alert("Starting pre-approval process. This would redirect to a loan application form.");
}
</script>'
    ],

    [
        'name' => 'Download First Buyer Starter Packs',
        'slug' => 'fhb-starter-packs',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">First Home Buyer Starter Packs</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Starter Packs</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Starter Packs -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Complete First Home Buyer Guides</h3>
                    <h2 class="text-anime">Everything You Need to Get Started</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Download comprehensive guides, checklists, and calculators to navigate your first home purchase with confidence.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="Essential Guide">
                    </div>
                    <h3>Essential FHB Guide 2025</h3>
                    <div class="pack-contents">
                        <h4>45-page comprehensive guide includes:</h4>
                        <ul>
                            <li>✓ Government grants by state (worth up to $55,000)</li>
                            <li>✓ Stamp duty concessions and exemptions</li>
                            <li>✓ 5% deposit loan schemes explained</li>
                            <li>✓ Shared equity programs overview</li>
                            <li>✓ Pre-approval checklist and documents</li>
                            <li>✓ Settlement timeline and milestones</li>
                            <li>✓ Common mistakes and how to avoid them</li>
                        </ul>
                    </div>
                    <div class="pack-stats">
                        <span>Downloaded 8,457 times</span> • <span>Updated Jan 2025</span>
                    </div>
                    <a href="#" class="btn-default" onclick="downloadPack(\'essential\')">Download Free Guide</a>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Finance Pack">
                    </div>
                    <h3>Finance & Grants Pack</h3>
                    <div class="pack-contents">
                        <h4>32-page finance-focused pack includes:</h4>
                        <ul>
                            <li>✓ Borrowing capacity calculator (Excel)</li>
                            <li>✓ Comparison of 15+ lenders</li>
                            <li>✓ Grant application forms and guidelines</li>
                            <li>✓ Pre-approval application templates</li>
                            <li>✓ Budget planner and savings tracker</li>
                            <li>✓ Credit score improvement strategies</li>
                            <li>✓ Family guarantee options explained</li>
                        </ul>
                    </div>
                    <div class="pack-stats">
                        <span>Downloaded 6,234 times</span> • <span>Updated Jan 2025</span>
                    </div>
                    <a href="#" class="btn-default" onclick="downloadPack(\'finance\')">Download Free Pack</a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-3.svg" alt="Property Search">
                    </div>
                    <h3>Property Search Toolkit</h3>
                    <div class="pack-contents">
                        <h4>28-page property selection guide includes:</h4>
                        <ul>
                            <li>✓ Suburb research checklist (20+ factors)</li>
                            <li>✓ Property inspection checklist</li>
                            <li>✓ Building and pest inspection guide</li>
                            <li>✓ Negotiation strategies for first buyers</li>
                            <li>✓ Auction vs private sale comparison</li>
                            <li>✓ Off-the-plan vs established guide</li>
                            <li>✓ Red flags and warning signs</li>
                        </ul>
                    </div>
                    <div class="pack-stats">
                        <span>Downloaded 4,567 times</span> • <span>Updated Dec 2024</span>
                    </div>
                    <a href="#" class="btn-default" onclick="downloadPack(\'search\')">Download Free Toolkit</a>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="1s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-4.svg" alt="Legal Pack">
                    </div>
                    <h3>Legal & Settlement Pack</h3>
                    <div class="pack-contents">
                        <h4>22-page legal essentials includes:</h4>
                        <ul>
                            <li>✓ Contract of sale explanation</li>
                            <li>✓ Conveyancer vs solicitor guide</li>
                            <li>✓ Settlement timeline and checklist</li>
                            <li>✓ Title search and property check guide</li>
                            <li>✓ Insurance requirements and options</li>
                            <li>✓ Strata report interpretation guide</li>
                            <li>✓ Final walkthrough checklist</li>
                        </ul>
                    </div>
                    <div class="pack-stats">
                        <span>Downloaded 3,892 times</span> • <span>Updated Nov 2024</span>
                    </div>
                    <a href="#" class="btn-default" onclick="downloadPack(\'legal\')">Download Free Pack</a>
                </div>
            </div>
        </div>

        <!-- Premium Bundle -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s" style="background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%); border: 2px solid #c4b5fd;">
                    <div style="text-align: center;">
                        <h3>Complete First Home Buyer Bundle</h3>
                        <p style="font-size: 18px; margin-bottom: 30px;">Get all 4 packs + exclusive bonuses worth $497 - FREE for a limited time!</p>
                        
                        <div class="bundle-features" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;">
                            <div class="bundle-item">
                                <h4>✓ All 4 Starter Packs</h4>
                                <p>Essential Guide + Finance Pack + Search Toolkit + Legal Pack</p>
                            </div>
                            <div class="bundle-item">
                                <h4>✓ Exclusive Calculator Suite</h4>
                                <p>Advanced Excel calculators for borrowing, grants, and affordability</p>
                            </div>
                            <div class="bundle-item">
                                <h4>✓ Video Tutorial Series</h4>
                                <p>6-part video series covering the entire FHB journey</p>
                            </div>
                            <div class="bundle-item">
                                <h4>✓ Email Support Course</h4>
                                <p>12-week automated email course with tips and reminders</p>
                            </div>
                        </div>
                        
                        <form id="bundle-download-form" style="max-width: 500px; margin: 0 auto;">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" placeholder="First Name" required>
                                </div>
                                <div class="col-md-6">
                                    <input type="email" class="form-control" placeholder="Email Address" required>
                                </div>
                            </div>
                            <div style="margin-top: 20px;">
                                <button type="submit" class="btn-default" style="font-size: 18px; padding: 12px 40px;">Download Complete Bundle FREE</button>
                            </div>
                        </form>
                        
                        <p style="font-size: 12px; color: #6c757d; margin-top: 15px;">
                            No spam. Unsubscribe anytime. Your data is protected and never shared.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testimonials -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Success Stories</h3>
                    <h2 class="text-anime">First Home Buyers Who Used Our Guides</h2>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4">
                <div class="testimonial-box wow fadeInUp" data-wow-delay="0.25s">
                    <p>"The finance pack saved us $8,000 in unnecessary fees. We found a better lender and got $25,000 in grants we didn\'t know existed!"</p>
                    <div class="testimonial-author">
                        <strong>Sarah & Mark T.</strong><br>
                        <small>Purchased in Pakenham, VIC - $680,000</small>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="testimonial-box wow fadeInUp" data-wow-delay="0.5s">
                    <p>"The property search toolkit helped us avoid 3 dodgy properties. The inspection checklist caught major issues the agent didn\'t mention."</p>
                    <div class="testimonial-author">
                        <strong>Jessica M.</strong><br>
                        <small>Purchased in Logan, QLD - $520,000</small>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="testimonial-box wow fadeInUp" data-wow-delay="0.75s">
                    <p>"We got pre-approved in 2 days using their checklist. The bundle made the whole process so much less stressful!"</p>
                    <div class="testimonial-author">
                        <strong>David & Emma K.</strong><br>
                        <small>Purchased in Melton, VIC - $590,000</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.pack-contents {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
}

.pack-contents h4 {
    margin-bottom: 15px;
    color: #7c3aed;
}

.pack-contents ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.pack-contents li {
    margin-bottom: 8px;
    color: #059669;
    font-weight: 500;
}

.pack-stats {
    font-size: 12px;
    color: #6c757d;
    margin: 15px 0;
    padding: 10px;
    background: #e9ecef;
    border-radius: 4px;
    text-align: center;
}

.bundle-item {
    text-align: left;
    padding: 15px;
    background: white;
    border-radius: 8px;
}

.bundle-item h4 {
    color: #7c3aed;
    margin-bottom: 8px;
}

.bundle-item p {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

.testimonial-box {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.testimonial-box p {
    font-style: italic;
    color: #495057;
    margin-bottom: 15px;
}

.testimonial-author {
    text-align: right;
}

.testimonial-author strong {
    color: #7c3aed;
}
</style>

<script>
function downloadPack(packType) {
    // In a real implementation, this would trigger a download
    alert(`Downloading ${packType} pack. This would trigger a PDF download and optionally capture email.`);
}

document.getElementById("bundle-download-form").addEventListener("submit", function(e) {
    e.preventDefault();
    alert("Downloading complete bundle. This would process the form and send download links via email.");
});
</script>'
    ]
];

// Projects Subdomain Pages
$projectsPages = [
    [
        'name' => 'Council Zoning Information',
        'slug' => 'council-zoning-planning',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Council Zoning & Planning Information</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Zoning & Planning</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Zoning Guide -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Understanding Zoning for Your Build</h3>
                    <h2 class="text-anime">Navigate Council Requirements with Confidence</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Essential zoning and planning information for land buyers, custom builders, and dual occupancy developers.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Zoning Types -->
        <div class="row">
            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-property-type-1.svg" alt="Residential Zoning">
                    </div>
                    <h3>Residential Zones (R1-R5)</h3>
                    <div class="zoning-details">
                        <div class="zone-type">
                            <h4>R1 - General Residential</h4>
                            <ul>
                                <li>Single dwelling homes</li>
                                <li>Minimum lot: 400m² (varies by council)</li>
                                <li>Building height: 8.5m max</li>
                                <li>Front setback: 6m typical</li>
                            </ul>
                        </div>
                        <div class="zone-type">
                            <h4>R2 - Low Density</h4>
                            <ul>
                                <li>Dual occupancy permitted</li>
                                <li>Minimum lot: 450m²</li>
                                <li>Secondary dwelling allowed</li>
                                <li>Side setback: 0.9m minimum</li>
                            </ul>
                        </div>
                    </div>
                    <a href="#" class="btn-default" onclick="searchZoning(\'residential\')">Search R-Zone Land</a>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-property-type-2.svg" alt="Mixed Use">
                    </div>
                    <h3>Mixed Use Zones</h3>
                    <div class="zoning-details">
                        <div class="zone-type">
                            <h4>B4 - Mixed Use</h4>
                            <ul>
                                <li>Commercial + residential</li>
                                <li>No minimum lot size</li>
                                <li>Height: varies 12-20m</li>
                                <li>Home business permitted</li>
                            </ul>
                        </div>
                        <div class="zone-type">
                            <h4>RU5 - Village</h4>
                            <ul>
                                <li>Rural residential style</li>
                                <li>Minimum lot: 2000m²</li>
                                <li>Agricultural use permitted</li>
                                <li>Reduced setbacks</li>
                            </ul>
                        </div>
                    </div>
                    <a href="#" class="btn-default" onclick="searchZoning(\'mixed\')">Search Mixed-Use Land</a>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-property-type-3.svg" alt="Special Zones">
                    </div>
                    <h3>Special Development Zones</h3>
                    <div class="zoning-details">
                        <div class="zone-type">
                            <h4>E4 - Environmental Living</h4>
                            <ul>
                                <li>Large lots with trees</li>
                                <li>Minimum lot: 4000m²</li>
                                <li>Tree preservation required</li>
                                <li>Special design controls</li>
                            </ul>
                        </div>
                        <div class="zone-type">
                            <h4>SP1 - Special Activities</h4>
                            <ul>
                                <li>Master-planned estates</li>
                                <li>Custom zoning rules</li>
                                <li>Staged development</li>
                                <li>Infrastructure contributions</li>
                            </ul>
                        </div>
                    </div>
                    <a href="#" class="btn-default" onclick="searchZoning(\'special\')">Search Special Zones</a>
                </div>
            </div>
        </div>

        <!-- Development Controls -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Key Development Controls</h3>
                    <h2 class="text-anime">What You Need to Know Before You Build</h2>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Building Controls Checklist</h3>
                    <div class="controls-list">
                        <div class="control-item">
                            <h4>Height Limits</h4>
                            <p>Most residential zones: 8.5m (2 storey)<br>
                            Some areas allow 9.5m with pitched roof<br>
                            Mixed use can go 12-20m depending on zone</p>
                        </div>
                        
                        <div class="control-item">
                            <h4>Setback Requirements</h4>
                            <p>Front: 6m typical (varies 4-10m)<br>
                            Side: 0.9m (single), 1.2m+ (double storey)<br>
                            Rear: 6m standard, 3m for single storey</p>
                        </div>
                        
                        <div class="control-item">
                            <h4>Floor Space Ratio (FSR)</h4>
                            <p>R2 zones: 0.5:1 typical<br>
                            R3/R4 zones: 0.6-0.8:1<br>
                            Includes all covered areas and garages</p>
                        </div>
                        
                        <div class="control-item">
                            <h4>Landscaping Requirements</h4>
                            <p>Minimum 25-30% deep soil planting<br>
                            Tree preservation may apply<br>
                            Front landscaping requirements</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.5s">
                    <h3>Dual Occupancy Specific Rules</h3>
                    <div class="controls-list">
                        <div class="control-item">
                            <h4>Minimum Lot Size</h4>
                            <p>NSW: 450m² typical (varies by council)<br>
                            QLD: 400m² in some areas<br>
                            VIC: 300m² in growth areas</p>
                        </div>
                        
                        <div class="control-item">
                            <h4>Design Requirements</h4>
                            <p>Each dwelling minimum 60m² floor area<br>
                            Separate entries required<br>
                            Privacy controls between dwellings</p>
                        </div>
                        
                        <div class="control-item">
                            <h4>Parking Requirements</h4>
                            <p>2 spaces per dwelling minimum<br>
                            1 covered space per dwelling<br>
                            Additional visitor parking may be required</p>
                        </div>
                        
                        <div class="control-item">
                            <h4>Stormwater & Services</h4>
                            <p>On-site detention may be required<br>
                            Separate utility connections<br>
                            Compliance with BASIX (energy/water)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Council Resources -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s">
                    <h3>Council Resources & Tools</h3>
                    <p>Access official council planning tools and resources for your development project.</p>
                    
                    <div class="council-tools" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0;">
                        <div class="tool-item">
                            <h4>🔍 Zoning Maps</h4>
                            <p>Interactive online maps showing zoning for every property in major councils.</p>
                            <a href="#" onclick="openCouncilTool(\'zoning\')" class="tool-link">Access Zoning Maps</a>
                        </div>
                        
                        <div class="tool-item">
                            <h4>📋 Development Applications</h4>
                            <p>Search approved DAs to see what\'s been built on similar lots in your area.</p>
                            <a href="#" onclick="openCouncilTool(\'da\')" class="tool-link">Search DA Database</a>
                        </div>
                        
                        <div class="tool-item">
                            <h4>📏 Planning Controls</h4>
                            <p>Detailed LEP and DCP documents for height, setbacks, and design controls.</p>
                            <a href="#" onclick="openCouncilTool(\'controls\')" class="tool-link">View Planning Controls</a>
                        </div>
                        
                        <div class="tool-item">
                            <h4>💰 Contributions Calculator</h4>
                            <p>Calculate Section 7.11/7.12 contributions required for your development.</p>
                            <a href="#" onclick="openCouncilTool(\'contributions\')" class="tool-link">Calculate Contributions</a>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <a href="/page/content/book-build-consultation" class="btn-default">Book Planning Consultation</a>
                        <a href="/property-listing?type=land" class="btn-default btn-border">View Available Land</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.zoning-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
}

.zone-type {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.zone-type:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.zone-type h4 {
    color: #ea580c;
    margin-bottom: 10px;
    font-size: 16px;
}

.zone-type ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.zone-type li {
    margin-bottom: 5px;
    color: #6c757d;
    font-size: 14px;
}

.controls-list {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.control-item {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.control-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.control-item h4 {
    color: #ea580c;
    margin-bottom: 8px;
    font-size: 16px;
}

.control-item p {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

.tool-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    text-align: center;
}

.tool-item h4 {
    color: #ea580c;
    margin-bottom: 10px;
}

.tool-item p {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
}

.tool-link {
    color: #ea580c;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
}

.tool-link:hover {
    color: #dc2626;
    text-decoration: underline;
}
</style>

<script>
function searchZoning(zoneType) {
    alert(`Searching for ${zoneType} zoned land. This would filter the property listings.`);
}

function openCouncilTool(toolType) {
    const tools = {
        zoning: "This would open an interactive zoning map tool",
        da: "This would open the development application search database", 
        controls: "This would open planning control documents",
        contributions: "This would open the contributions calculator"
    };
    alert(tools[toolType] || "Opening council planning tool");
}
</script>'
    ],

    [
        'name' => 'Custom Floorplan Libraries',
        'slug' => 'custom-floorplan-library',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Custom Floorplan Library</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Floorplan Library</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floorplan Library -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Architect-Designed Floorplans</h3>
                    <h2 class="text-anime">500+ Premium Designs Ready to Build</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Browse our extensive library of custom floorplans from award-winning architects. All designs include engineering and can be modified to suit your block.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="row" style="margin-bottom: 40px;">
            <div class="col-lg-12">
                <div class="floorplan-filters">
                    <div class="filter-group">
                        <label>Home Type:</label>
                        <select id="home-type" onchange="filterFloorplans()">
                            <option value="all">All Types</option>
                            <option value="single">Single Storey</option>
                            <option value="double">Double Storey</option>
                            <option value="dual">Dual Occupancy</option>
                            <option value="knockdown">Knockdown Rebuild</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Bedrooms:</label>
                        <select id="bedrooms" onchange="filterFloorplans()">
                            <option value="all">Any</option>
                            <option value="3">3 Bedrooms</option>
                            <option value="4">4 Bedrooms</option>
                            <option value="5">5+ Bedrooms</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Block Width:</label>
                        <select id="block-width" onchange="filterFloorplans()">
                            <option value="all">Any Width</option>
                            <option value="narrow">Narrow (10-12.5m)</option>
                            <option value="standard">Standard (12.5-15m)</option>
                            <option value="wide">Wide (15m+)</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Style:</label>
                        <select id="style" onchange="filterFloorplans()">
                            <option value="all">All Styles</option>
                            <option value="modern">Modern</option>
                            <option value="hamptons">Hamptons</option>
                            <option value="traditional">Traditional</option>
                            <option value="farmhouse">Farmhouse</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Floorplans -->
        <div class="row floorplan-grid">
            <div class="col-lg-4 col-md-6 floorplan-item" data-type="double" data-bedrooms="4" data-width="standard" data-style="modern">
                <div class="floorplan-card wow fadeInUp" data-wow-delay="0.25s">
                    <div class="floorplan-image">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/property-1.jpg" alt="The Madison" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="floorplan-overlay">
                            <button class="btn-view" onclick="viewFloorplan(1)">View Details</button>
                            <button class="btn-download" onclick="downloadFloorplan(1)">Download PDF</button>
                        </div>
                    </div>
                    <div class="floorplan-details">
                        <h3>The Madison</h3>
                        <div class="floorplan-specs">
                            <span>🛏️ 4 Bed</span>
                            <span>🚿 2.5 Bath</span>
                            <span>🚗 2 Car</span>
                            <span>📐 280m²</span>
                        </div>
                        <div class="floorplan-features">
                            <ul>
                                <li>✓ Master with walk-in robe & ensuite</li>
                                <li>✓ Open plan living & dining</li>
                                <li>✓ Butler\'s pantry</li>
                                <li>✓ Alfresco entertaining area</li>
                            </ul>
                        </div>
                        <div class="floorplan-pricing">
                            <span class="build-cost">From $420,000*</span>
                            <span class="lot-suitable">Suits 12.5m+ lots</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 floorplan-item" data-type="dual" data-bedrooms="6" data-width="wide" data-style="modern">
                <div class="floorplan-card wow fadeInUp" data-wow-delay="0.5s">
                    <div class="floorplan-image">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/property-2.jpg" alt="The Duplex Pro" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="floorplan-overlay">
                            <button class="btn-view" onclick="viewFloorplan(2)">View Details</button>
                            <button class="btn-download" onclick="downloadFloorplan(2)">Download PDF</button>
                        </div>
                        <div class="investment-badge">INVESTMENT</div>
                    </div>
                    <div class="floorplan-details">
                        <h3>The Duplex Pro</h3>
                        <div class="floorplan-specs">
                            <span>🛏️ 3+3 Bed</span>
                            <span>🚿 2+2 Bath</span>
                            <span>🚗 2+2 Car</span>
                            <span>📐 320m²</span>
                        </div>
                        <div class="floorplan-features">
                            <ul>
                                <li>✓ Two separate 3BR dwellings</li>
                                <li>✓ Private courtyards each side</li>
                                <li>✓ Individual driveways</li>
                                <li>✓ Estimated 7.2% rental yield</li>
                            </ul>
                        </div>
                        <div class="floorplan-pricing">
                            <span class="build-cost">From $580,000*</span>
                            <span class="lot-suitable">Suits 16m+ lots</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 floorplan-item" data-type="single" data-bedrooms="3" data-width="narrow" data-style="hamptons">
                <div class="floorplan-card wow fadeInUp" data-wow-delay="0.75s">
                    <div class="floorplan-image">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/property-3.jpg" alt="The Coastal" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="floorplan-overlay">
                            <button class="btn-view" onclick="viewFloorplan(3)">View Details</button>
                            <button class="btn-download" onclick="downloadFloorplan(3)">Download PDF</button>
                        </div>
                        <div class="narrow-lot-badge">NARROW LOT</div>
                    </div>
                    <div class="floorplan-details">
                        <h3>The Coastal</h3>
                        <div class="floorplan-specs">
                            <span>🛏️ 3 Bed</span>
                            <span>🚿 2 Bath</span>
                            <span>🚗 1 Car</span>
                            <span>📐 185m²</span>
                        </div>
                        <div class="floorplan-features">
                            <ul>
                                <li>✓ Perfect for 10m wide lots</li>
                                <li>✓ Hamptons-style facade</li>
                                <li>✓ Courtyard garden design</li>
                                <li>✓ First home buyer friendly</li>
                            </ul>
                        </div>
                        <div class="floorplan-pricing">
                            <span class="build-cost">From $285,000*</span>
                            <span class="lot-suitable">Suits 10m+ lots</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 floorplan-item" data-type="double" data-bedrooms="5" data-width="wide" data-style="farmhouse">
                <div class="floorplan-card wow fadeInUp" data-wow-delay="1s">
                    <div class="floorplan-image">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/property-4.jpg" alt="The Farmhouse Estate" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="floorplan-overlay">
                            <button class="btn-view" onclick="viewFloorplan(4)">View Details</button>
                            <button class="btn-download" onclick="downloadFloorplan(4)">Download PDF</button>
                        </div>
                        <div class="luxury-badge">LUXURY</div>
                    </div>
                    <div class="floorplan-details">
                        <h3>The Farmhouse Estate</h3>
                        <div class="floorplan-specs">
                            <span>🛏️ 5 Bed</span>
                            <span>🚿 3 Bath</span>
                            <span>🚗 3 Car</span>
                            <span>📐 385m²</span>
                        </div>
                        <div class="floorplan-features">
                            <ul>
                                <li>✓ Grand master suite with sitting area</li>
                                <li>✓ Separate media room</li>
                                <li>✓ Butler\'s pantry & scullery</li>
                                <li>✓ Wraparound verandah</li>
                            </ul>
                        </div>
                        <div class="floorplan-pricing">
                            <span class="build-cost">From $680,000*</span>
                            <span class="lot-suitable">Suits 18m+ lots</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 floorplan-item" data-type="knockdown" data-bedrooms="4" data-width="standard" data-style="modern">
                <div class="floorplan-card wow fadeInUp" data-wow-delay="1.25s">
                    <div class="floorplan-image">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/property-5.jpg" alt="The Urban Rebuild" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="floorplan-overlay">
                            <button class="btn-view" onclick="viewFloorplan(5)">View Details</button>
                            <button class="btn-download" onclick="downloadFloorplan(5)">Download PDF</button>
                        </div>
                        <div class="knockdown-badge">KNOCKDOWN</div>
                    </div>
                    <div class="floorplan-details">
                        <h3>The Urban Rebuild</h3>
                        <div class="floorplan-specs">
                            <span>🛏️ 4 Bed</span>
                            <span>🚿 2.5 Bath</span>
                            <span>🚗 2 Car</span>
                            <span>📐 295m²</span>
                        </div>
                        <div class="floorplan-features">
                            <ul>
                                <li>✓ Maximizes existing boundary setbacks</li>
                                <li>✓ Architecturally designed facade</li>
                                <li>✓ Sustainable design features</li>
                                <li>✓ Perfect for established suburbs</li>
                            </ul>
                        </div>
                        <div class="floorplan-pricing">
                            <span class="build-cost">From $475,000*</span>
                            <span class="lot-suitable">Suits existing lots</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 floorplan-item" data-type="single" data-bedrooms="4" data-width="standard" data-style="traditional">
                <div class="floorplan-card wow fadeInUp" data-wow-delay="1.5s">
                    <div class="floorplan-image">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/property-6.jpg" alt="The Family Classic" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="floorplan-overlay">
                            <button class="btn-view" onclick="viewFloorplan(6)">View Details</button>
                            <button class="btn-download" onclick="downloadFloorplan(6)">Download PDF</button>
                        </div>
                    </div>
                    <div class="floorplan-details">
                        <h3>The Family Classic</h3>
                        <div class="floorplan-specs">
                            <span>🛏️ 4 Bed</span>
                            <span>🚿 2 Bath</span>
                            <span>🚗 2 Car</span>
                            <span>📐 245m²</span>
                        </div>
                        <div class="floorplan-features">
                            <ul>
                                <li>✓ Traditional family layout</li>
                                <li>✓ Separate formal & informal living</li>
                                <li>✓ Large family kitchen</li>
                                <li>✓ Covered outdoor entertaining</li>
                            </ul>
                        </div>
                        <div class="floorplan-pricing">
                            <span class="build-cost">From $365,000*</span>
                            <span class="lot-suitable">Suits 12.5m+ lots</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom Design Service -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s" style="background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%); border: 2px solid #fdba74;">
                    <div style="text-align: center;">
                        <h3>Can\'t Find Your Perfect Design?</h3>
                        <p style="font-size: 18px; margin-bottom: 30px;">Our architects can create a custom floorplan tailored to your specific lot and lifestyle needs.</p>
                        
                        <div class="custom-design-features" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;">
                            <div class="design-feature">
                                <h4>🎨 Custom Architecture</h4>
                                <p>Tailored to your lot size, orientation, and lifestyle requirements</p>
                            </div>
                            <div class="design-feature">
                                <h4>📋 Council Approval</h4>
                                <p>We handle all council submissions and approvals for you</p>
                            </div>
                            <div class="design-feature">
                                <h4>⚡ 14-Day Turnaround</h4>
                                <p>Initial concepts delivered within 2 weeks</p>
                            </div>
                            <div class="design-feature">
                                <h4>💰 Fixed Price</h4>
                                <p>$3,500 for complete architectural drawings</p>
                            </div>
                        </div>
                        
                        <div style="margin-top: 30px;">
                            <a href="/page/content/book-build-consultation" class="btn-default" style="margin-right: 15px;">Book Design Consultation</a>
                            <a href="/property-listing?type=land" class="btn-default btn-border">Find Your Perfect Block</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.floorplan-filters {
    display: flex;
    gap: 20px;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    flex-wrap: wrap;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #ea580c;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 150px;
}

.floorplan-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 30px;
}

.floorplan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.floorplan-image {
    position: relative;
    overflow: hidden;
}

.floorplan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(234, 88, 12, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.floorplan-image:hover .floorplan-overlay {
    opacity: 1;
}

.btn-view, .btn-download {
    background: white;
    color: #ea580c;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-view:hover, .btn-download:hover {
    background: #f8f9fa;
}

.investment-badge, .narrow-lot-badge, .luxury-badge, .knockdown-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ea580c;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
}

.narrow-lot-badge {
    background: #7c3aed;
}

.luxury-badge {
    background: #059669;
}

.knockdown-badge {
    background: #dc2626;
}

.floorplan-details {
    padding: 20px;
}

.floorplan-details h3 {
    color: #ea580c;
    margin-bottom: 10px;
}

.floorplan-specs {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.floorplan-specs span {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #6c757d;
}

.floorplan-features ul {
    margin: 0 0 15px 0;
    padding: 0;
    list-style: none;
}

.floorplan-features li {
    margin-bottom: 5px;
    font-size: 14px;
    color: #6c757d;
}

.floorplan-pricing {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.build-cost {
    font-weight: 700;
    color: #ea580c;
    font-size: 18px;
}

.lot-suitable {
    font-size: 12px;
    color: #6c757d;
}

.design-feature {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: left;
}

.design-feature h4 {
    color: #ea580c;
    margin-bottom: 8px;
}

.design-feature p {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}
</style>

<script>
function filterFloorplans() {
    const homeType = document.getElementById("home-type").value;
    const bedrooms = document.getElementById("bedrooms").value;
    const blockWidth = document.getElementById("block-width").value;
    const style = document.getElementById("style").value;
    
    const items = document.querySelectorAll(".floorplan-item");
    
    items.forEach(item => {
        let show = true;
        
        if (homeType !== "all" && item.dataset.type !== homeType) show = false;
        if (bedrooms !== "all" && item.dataset.bedrooms !== bedrooms) show = false;
        if (blockWidth !== "all" && item.dataset.width !== blockWidth) show = false;
        if (style !== "all" && item.dataset.style !== style) show = false;
        
        item.style.display = show ? "block" : "none";
    });
}

function viewFloorplan(id) {
    alert(`Viewing detailed floorplan ${id}. This would open a modal with larger images and full specifications.`);
}

function downloadFloorplan(id) {
    alert(`Downloading floorplan ${id} PDF. This would trigger a PDF download.`);
}
</script>'
    ],

    [
        'name' => 'Download Build Smarter 2025 Packs',
        'slug' => 'build-smarter-packs',
        'content' => '
<!-- Page Header -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="page-header-box">
                    <h1 class="text-anime">Build Smarter 2025 Packs</h1>
                    <nav class="wow fadeInUp" data-wow-delay="0.25s">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item active">Build Smarter Packs</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Build Smarter Packs -->
<div class="contact-us">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Complete Building & Development Guides</h3>
                    <h2 class="text-anime">Master the Art of Property Development</h2>
                    <div class="wow fadeInUp" data-wow-delay="0.25s">
                        <p>Comprehensive guides for land buyers, custom builders, and property developers. Save thousands and avoid costly mistakes.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.25s" style="height: 420px;">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-1.svg" alt="Land Buyer Guide">
                    </div>
                    <h3>Smart Land Buyer\'s Guide</h3>
                    <div class="pack-contents">
                        <h4>52-page comprehensive guide includes:</h4>
                        <ul>
                            <li>✓ Due diligence checklist (35+ items)</li>
                            <li>✓ Council zoning and planning basics</li>
                            <li>✓ Soil testing and site evaluation</li>
                            <li>✓ Utility connection costs and timelines</li>
                            <li>✓ Slope analysis and retaining wall costs</li>
                            <li>✓ Easement and covenant implications</li>
                            <li>✓ Land contract negotiation strategies</li>
                        </ul>
                    </div>
                    <div class="pack-bonus">
                        <strong>BONUS:</strong> Land evaluation spreadsheet + Council contact database
                    </div>
                    <a href="#" class="btn-default" onclick="downloadBuildPack(\'land\')">Download Free Guide</a>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.5s" style="height: 420px;">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-2.svg" alt="Builder Selection">
                    </div>
                    <h3>Builder Selection Toolkit</h3>
                    <div class="pack-contents">
                        <h4>38-page builder comparison pack includes:</h4>
                        <ul>
                            <li>✓ Builder evaluation scorecard</li>
                            <li>✓ Contract comparison template</li>
                            <li>✓ Red flags and warning signs</li>
                            <li>✓ Progress payment schedule guide</li>
                            <li>✓ Variation management strategies</li>
                            <li>✓ Quality control checklists</li>
                            <li>✓ Dispute resolution procedures</li>
                        </ul>
                    </div>
                    <div class="pack-bonus">
                        <strong>BONUS:</strong> Builder interview questions + Reference check template
                    </div>
                    <a href="#" class="btn-default" onclick="downloadBuildPack(\'builder\')">Download Free Toolkit</a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="0.75s" style="height: 420px;">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-3.svg" alt="Development Finance">
                    </div>
                    <h3>Development Finance Mastery</h3>
                    <div class="pack-contents">
                        <h4>44-page finance strategy guide includes:</h4>
                        <ul>
                            <li>✓ Construction loan vs traditional loan</li>
                            <li>✓ Progress payment structures</li>
                            <li>✓ Interest-only vs P&I strategies</li>
                            <li>✓ Bridging finance for knockdown-rebuild</li>
                            <li>✓ Tax implications of building vs buying</li>
                            <li>✓ Off-the-plan finance considerations</li>
                            <li>✓ Dual occupancy funding strategies</li>
                        </ul>
                    </div>
                    <div class="pack-bonus">
                        <strong>BONUS:</strong> Cash flow calculator + Lender comparison matrix
                    </div>
                    <a href="#" class="btn-default" onclick="downloadBuildPack(\'finance\')">Download Free Guide</a>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="contact-info-box wow fadeInUp" data-wow-delay="1s" style="height: 420px;">
                    <div class="icon-box">
                        <img src="/themes/sunshine/seamlessproperty/assets/images/icon-how-4.svg" alt="Dual Occupancy">
                    </div>
                    <h3>Dual Occupancy Development Kit</h3>
                    <div class="pack-contents">
                        <h4>48-page dual occupancy guide includes:</h4>
                        <ul>
                            <li>✓ Lot size and zoning requirements</li>
                            <li>✓ Design principles and layouts</li>
                            <li>✓ Council approval processes</li>
                            <li>✓ Strata titling vs single title</li>
                            <li>✓ Rental yield optimization</li>
                            <li>✓ Construction cost breakdowns</li>
                            <li>✓ Exit strategies and subdivision</li>
                        </ul>
                    </div>
                    <div class="pack-bonus">
                        <strong>BONUS:</strong> ROI calculator + Case study analysis
                    </div>
                    <a href="#" class="btn-default" onclick="downloadBuildPack(\'dual\')">Download Free Kit</a>
                </div>
            </div>
        </div>

        <!-- Premium Master Pack -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="contact-form-box wow fadeInUp" data-wow-delay="0.25s" style="background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%); border: 3px solid #ea580c;">
                    <div style="text-align: center;">
                        <h3 style="color: #ea580c;">Complete Build Smarter Master Pack</h3>
                        <p style="font-size: 20px; margin-bottom: 30px; color: #dc2626;">Everything you need to build wealth through property development - Value $1,247, FREE for limited time!</p>
                        
                        <div class="master-pack-contents" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 25px; margin: 40px 0;">
                            <div class="master-item">
                                <h4>📚 All 4 Core Guides</h4>
                                <p>Land Buyer + Builder Selection + Finance Mastery + Dual Occupancy Kit</p>
                                <small>182 pages of expert knowledge</small>
                            </div>
                            <div class="master-item">
                                <h4>📊 Advanced Calculator Suite</h4>
                                <p>15 Excel calculators for land evaluation, build costs, ROI, and cash flow analysis</p>
                                <small>Professional-grade tools</small>
                            </div>
                            <div class="master-item">
                                <h4>🎥 Video Masterclass Series</h4>
                                <p>12-part video series covering every aspect of property development</p>
                                <small>8+ hours of expert training</small>
                            </div>
                            <div class="master-item">
                                <h4>📋 Legal Document Templates</h4>
                                <p>Contracts, checklists, and agreements reviewed by property lawyers</p>
                                <small>Save $1000s in legal fees</small>
                            </div>
                            <div class="master-item">
                                <h4>🏗️ Case Study Library</h4>
                                <p>25+ real development projects with costs, timelines, and profit analysis</p>
                                <small>Learn from real success stories</small>
                            </div>
                            <div class="master-item">
                                <h4>📞 Expert Support Access</h4>
                                <p>Direct access to our development specialists for 90 days</p>
                                <small>Guidance when you need it most</small>
                            </div>
                        </div>
                        
                        <form id="master-pack-form" style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px;">
                            <h4 style="margin-bottom: 20px; color: #ea580c;">Get the Complete Master Pack FREE</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" placeholder="First Name" required style="margin-bottom: 15px;">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control" placeholder="Last Name" required style="margin-bottom: 15px;">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="email" class="form-control" placeholder="Email Address" required style="margin-bottom: 15px;">
                                </div>
                                <div class="col-md-6">
                                    <input type="tel" class="form-control" placeholder="Phone Number" required style="margin-bottom: 15px;">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <select class="form-control" required style="margin-bottom: 20px;">
                                        <option value="">What\'s your main development goal?</option>
                                        <option value="first-build">First time building</option>
                                        <option value="investment">Investment property development</option>
                                        <option value="dual-occupancy">Dual occupancy project</option>
                                        <option value="knockdown-rebuild">Knockdown rebuild</option>
                                        <option value="multiple-projects">Multiple development projects</option>
                                    </select>
                                </div>
                            </div>
                            <button type="submit" class="btn-default" style="font-size: 18px; padding: 15px 40px; background: #ea580c; border-color: #ea580c;">
                                Get My FREE Master Pack (Value $1,247)
                            </button>
                        </form>
                        
                        <div style="margin-top: 20px;">
                            <small style="color: #6c757d;">
                                ✓ Instant download access ✓ No hidden costs ✓ Unsubscribe anytime<br>
                                ✓ Your data is secure and never shared ✓ Australian-specific content
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Stories -->
        <div class="row" style="margin-top: 50px;">
            <div class="col-lg-12">
                <div class="section-title">
                    <h3 class="wow fadeInUp">Development Success Stories</h3>
                    <h2 class="text-anime">Developers Who Used Our Build Smarter System</h2>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4">
                <div class="success-story wow fadeInUp" data-wow-delay="0.25s">
                    <div class="story-header">
                        <h4>James & Sarah K.</h4>
                        <p class="story-location">Dual Occupancy - Pakenham, VIC</p>
                    </div>
                    <div class="story-stats">
                        <div class="stat">
                            <span class="stat-label">Total Investment:</span>
                            <span class="stat-value">$680,000</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Current Value:</span>
                            <span class="stat-value">$820,000</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Rental Income:</span>
                            <span class="stat-value">$650/week</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">ROI:</span>
                            <span class="stat-value highlight">22%</span>
                        </div>
                    </div>
                    <blockquote>
                        "The dual occupancy guide saved us from making a $35,000 mistake with the wrong zoning. Now we\'re building our second project!"
                    </blockquote>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="success-story wow fadeInUp" data-wow-delay="0.5s">
                    <div class="story-header">
                        <h4>Michael T.</h4>
                        <p class="story-location">Custom Build - Kellyville, NSW</p>
                    </div>
                    <div class="story-stats">
                        <div class="stat">
                            <span class="stat-label">Land Cost:</span>
                            <span class="stat-value">$450,000</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Build Cost:</span>
                            <span class="stat-value">$420,000</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Current Value:</span>
                            <span class="stat-value">$1,050,000</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Equity Gain:</span>
                            <span class="stat-value highlight">$180,000</span>
                        </div>
                    </div>
                    <blockquote>
                        "The builder selection toolkit helped me avoid a dodgy builder and find an award-winning team. Best decision ever!"
                    </blockquote>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="success-story wow fadeInUp" data-wow-delay="0.75s">
                    <div class="story-header">
                        <h4>Lisa & David M.</h4>
                        <p class="story-location">Knockdown Rebuild - Coorparoo, QLD</p>
                    </div>
                    <div class="story-stats">
                        <div class="stat">
                            <span class="stat-label">Existing Home:</span>
                            <span class="stat-value">$580,000</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Build Cost:</span>
                            <span class="stat-value">$380,000</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">New Value:</span>
                            <span class="stat-value">$1,150,000</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Equity Gain:</span>
                            <span class="stat-value highlight">$190,000</span>
                        </div>
                    </div>
                    <blockquote>
                        "The finance mastery guide helped us structure the loan perfectly. We saved $8,000 in interest during construction!"
                    </blockquote>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.pack-contents {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
}

.pack-contents h4 {
    margin-bottom: 15px;
    color: #ea580c;
}

.pack-contents ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.pack-contents li {
    margin-bottom: 8px;
    color: #059669;
    font-weight: 500;
    font-size: 14px;
}

.pack-bonus {
    background: #fff7ed;
    border: 1px solid #fdba74;
    border-radius: 6px;
    padding: 12px;
    margin: 15px 0;
    font-size: 14px;
    color: #ea580c;
}

.master-item {
    background: white;
    padding: 25px;
    border-radius: 8px;
    text-align: left;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.master-item h4 {
    color: #ea580c;
    margin-bottom: 10px;
    font-size: 16px;
}

.master-item p {
    color: #333;
    font-size: 14px;
    margin-bottom: 8px;
}

.master-item small {
    color: #6c757d;
    font-style: italic;
}

.success-story {
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.story-header h4 {
    color: #ea580c;
    margin-bottom: 5px;
}

.story-location {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 20px;
}

.story-stats {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e9ecef;
}

.stat:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
}

.stat-value {
    font-weight: 600;
    color: #333;
}

.stat-value.highlight {
    color: #059669;
    font-size: 16px;
}

.success-story blockquote {
    font-style: italic;
    color: #495057;
    margin: 0;
    padding: 15px;
    background: #f8f9fa;
    border-left: 4px solid #ea580c;
    border-radius: 0 6px 6px 0;
}
</style>

<script>
function downloadBuildPack(packType) {
    alert(`Downloading ${packType} pack. This would trigger a PDF download and optionally capture email for follow-up.`);
}

document.getElementById("master-pack-form").addEventListener("submit", function(e) {
    e.preventDefault();
    alert("Thank you! Your Build Smarter Master Pack is being prepared. Check your email for download links in the next 5 minutes.");
});
</script>'
    ]
];

echo "\nCreating FHB subdomain pages...\n";
foreach ($fhbPages as $page) {
    DB::table('cms_pages')->insert([
        'name' => $page['name'],
        'slug' => $page['slug'],
        'content' => $page['content'],
        'subdomain_id' => $subdomains['fhb'],
        'company_id' => $companyId,
        'status' => 1,
        'created_at' => now(),
        'updated_at' => now(),
        'created_by' => $userId,
        'updated_by' => $userId
    ]);
    echo "  ✓ Created: {$page['name']}\n";
}

echo "\nCreating Projects subdomain pages...\n";
foreach ($projectsPages as $page) {
    DB::table('cms_pages')->insert([
        'name' => $page['name'],
        'slug' => $page['slug'],
        'content' => $page['content'],
        'subdomain_id' => $subdomains['projects'],
        'company_id' => $companyId,
        'status' => 1,
        'created_at' => now(),
        'updated_at' => now(),
        'created_by' => $userId,
        'updated_by' => $userId
    ]);
    echo "  ✓ Created: {$page['name']}\n";
}

echo "\nComprehensive subdomain pages creation completed!\n";