<?php

namespace Modules\Deepfake\Livewire;

use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Mary\Traits\WithMediaSync;

class DeepfakeForm extends Component
{
    use WithFileUploads, WithMediaSync;

    #[Rule('required')]
    public $userPhoto;

    public User $user;

    public function render()
    {
        return view('deepfake::livewire.deepfake-form');
    }
}
