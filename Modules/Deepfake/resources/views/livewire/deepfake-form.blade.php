<div>
    <script type="module">
        import * as LR from "https://cdn.jsdelivr.net/npm/@uploadcare/blocks@0.35.2/web/lr-file-uploader-regular.min.js";

        LR.registerBlocks(LR);
        alert('LR.registerBlocks(LR) has been called');
    </script>

    <lr-config
        ctx-name="my-uploader"
        pubkey="ab04c86f51d5a567f23a"
        max-local-file-size-bytes="10000000"
        img-only="true"
        source-list="local, url, camera, dropbox"
    ></lr-config>

    <h3>The <code>DeepfakeForm</code> livewire component is loaded from the <code>Deepfake</code> module.</h3>

    <lr-file-uploader-regular
        css-src="https://cdn.jsdelivr.net/npm/@uploadcare/blocks@0.35.2/web/lr-file-uploader-regular.min.css"
        ctx-name="my-uploader"
        class="my-config"
    >
    </lr-file-uploader-regular>

</div>
