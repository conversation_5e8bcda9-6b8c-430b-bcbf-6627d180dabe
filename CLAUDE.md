# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This is a comprehensive real estate CRM platform consisting of three main components:

- **`crm-backend/`** - Laravel 10 backend with modular architecture (admin panel, API)
- **`crm-frontend/`** - <PERSON>vel frontend application (public-facing website)  
- **`seamlesshtml/`** - Static HTML template assets for theming

## Development Commands

### Backend (crm-backend/)

**Development & Build:**
- `npm run dev` - Start Vite development server
- `npm run build` - Build assets for production  
- `php artisan serve` - Start Laravel development server
- `php artisan octane:start` - Start with Laravel Octane for better performance

**Testing:**
- `vendor/bin/pest` - Run PHP tests using Pest framework
- `php artisan test` - Alternative test command
- `vendor/bin/pest --watch` - Run tests in watch mode

**Code Quality:**
- `vendor/bin/pint` - Format PHP code using Laravel Pint
- `vendor/bin/pest --type-coverage` - Check type coverage

**Database:**
- `php artisan migrate:fresh --seed` - Fresh database with seeding
- `php artisan migrate` - Run database migrations
- `php artisan db:seed` - Run database seeders
- `php artisan storage:link` - Link storage directory (required for file uploads)

**Module Management:**
- `php artisan module:make ModuleName` - Create new module
- `php artisan module:publish` - Publish module assets (**required after JS file changes**)

### Frontend (crm-frontend/)

**Development & Build:**
- `npm run dev` - Start Vite development server for frontend
- `npm run build` - Build frontend assets for production

### Testing Single Module/Feature

**Run tests for specific module:**
- `vendor/bin/pest tests/Feature/ModuleName/`
- `vendor/bin/pest --filter=PropertyTest`

## Architecture Overview

### Multi-Application Laravel System

**Backend Application (crm-backend/):**
- Admin dashboard and CRM functionality
- API endpoints for frontend consumption
- Multi-tenant architecture with company-based isolation
- Modular structure using nwidart/laravel-modules

**Frontend Application (crm-frontend/):**  
- Public-facing real estate website
- Property listings and search functionality
- Theme system with customizable layouts
- MaryUI component library integration

### Core Architecture Patterns

**Modular Architecture:** Both applications use Laravel Modules package. Each module is self-contained with routes, controllers, models, views, and assets.

**Multi-Tenancy Implementation:**
- Company-based data isolation using `company_id` on most tables
- `SetCompanyTeamId` middleware for company context
- Spatie Permission with teams feature for scoped permissions
- White-label support via subdomain configuration

**Permission & Feature System:**
- Feature-based access control stored in database
- `check_feature:FeatureName` middleware for feature checks
- Company-scoped permissions using Spatie Permission teams

### Key Backend Modules

**Core Business Logic:**
- `Property` - Property management, listings, PIAB integration
- `Client` - Customer relationship management
- `Company` - Multi-tenant company management and settings
- `Agent` - Real estate agent functionality and profiles
- `Project` - Development project management
- `Subscription` - Stripe billing and subscription management

**System Modules:**
- `Auth` - Authentication with social login (Google/Facebook)
- `Dashboard` - Analytics and overview functionality
- `WhiteLabel` - Custom branding and subdomain management
- `Blog` - Content management system

### External Integrations

**PIAB Property Integration:**
- Bulk property import: `/piab-import-all?start_date=2024-03-04&limit=25&page=1`
- Status-specific imports: `/piab-import-all/79` (available), `/piab-import-all/82` (unavailable)
- Client synchronization: `/piab-property-sync/{company}/{status}`
- Job-based processing for bulk operations

**Stripe Payment Processing:**
- Webhook endpoints for subscription lifecycle events
- Plan management with monthly/yearly billing
- Trial period and cancellation handling
- Price ID configuration (not product ID)

### Critical Middleware Stack

**Backend Middleware (must be used in order):**
1. Authentication middleware
2. `set_company_id` - **CRITICAL**: Sets company context for multi-tenancy
3. `check_feature:FeatureName` - Feature access control
4. `check_subscription` - Active subscription verification
5. `permission:PermissionName` - User permissions

**Frontend Middleware:**
- `SetSubdomainConfiguration` - Subdomain-based theming
- `SetCompanyUsingWhiteLabelMiddleware` - White-label configuration

### Development Patterns & Standards

**Service Layer Pattern:**
- Business logic in service classes: `Modules/{Module}/App/Models/Services/{Module}Class.php`
- Controllers focus on request/response handling
- Services handle complex business logic and external integrations

**Error Handling:**
- Use `CommonCustomErrorExecption` for standardized errors across modules
- Proper exception types for different error scenarios

**New Feature Implementation:**
1. Create database feature record via migration
2. Add permission entries via migration  
3. Apply `check_feature` middleware in routes
4. Add menu conditional: `auth()->user()->isFeatureEnabled('Feature Name')`

**File Upload Management:**
- Spatie Media Library for all file handling
- Temporary upload endpoint: `/tmp-media/upload`
- Storage linking required: `php artisan storage:link`

### Database Schema Patterns

**Multi-Tenant Design:**
- Most tables include `company_id` for data isolation
- Soft deletes implemented on core entities
- Optimized indexing for search operations (properties, locations)

**Key Performance Optimizations:**
- Database indexing on search columns
- Job queues for bulk operations (PIAB imports)
- Caching for frequently accessed data

### Frontend Technology Stack

**Backend Frontend (crm-backend):**
- Livewire 3 with Volt for reactive components
- Tailwind CSS for styling
- Alpine.js for JavaScript interactions
- Vite for asset compilation and hot reloading

**Frontend Application (crm-frontend):**
- Laravel with MaryUI component library
- DaisyUI + Tailwind CSS
- Theme system for multi-tenant customization
- Puppeteer for PDF generation

### Testing Infrastructure

**Pest PHP Configuration:**
- Primary testing framework with plugins
- RefreshDatabase trait for isolated tests
- Plugins: Faker, Livewire, Stress Testing, Type Coverage, Watch mode
- Feature and Unit test organization

### Configuration Management

**Key Configuration Files:**
- `config/modules.php` - Module system settings
- `config/plans.php` - Subscription plan definitions
- `config/custom.php` - Application-specific configuration
- Module routes: `Modules/{Module}/routes/web.php` and `api.php`

### Environment-Specific Setup

**Development Environment:**
- Use Laravel Sail for Docker development
- Laravel Telescope enabled for debugging
- Laravel Debugbar for query optimization

**Production Considerations:**
- Laravel Octane with FrankenPHP for performance
- Stripe webhooks configuration required
- Google Maps API key required for location features
- Social login credentials per company configuration

### Static Assets & Theming

**SeamlessHTML Template:**
- Pre-built HTML templates for quick theming
- Bootstrap-based responsive layouts
- Real estate specific components and layouts
- Asset organization for easy customization

## Important Development Notes

**Module Development:**
- Always run `php artisan module:publish` after modifying JavaScript files in modules
- Use company-scoped queries for all multi-tenant data access
- Follow the established service layer pattern for business logic

**Security Considerations:**
- Never bypass the `set_company_id` middleware after authentication
- Always validate company ownership before data access
- Use feature flags to control access to functionality

**Performance Best Practices:**
- Leverage job queues for time-consuming operations
- Use database indexing on search columns
- Implement proper caching strategies for frequently accessed data